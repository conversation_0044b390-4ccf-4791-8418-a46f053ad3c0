package net.xianmu.usercenter.facade.auth.input;

import lombok.Data;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;

import java.util.Date;

@Data
public class RoleAdminInput {
    /**
     * 操作人authUserId
     */
    private Long opAuthUserId;

    /**
     * 角色名
     */
    private String rolename;
    /**
     * 备注
     */
    private String remarks;
    private Date createTime;
    private Date updateTime;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 系统来源
     *
     * @see net.xianmu.authentication.client.input.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 0默认，非超级管理员  超级管理员1
     */
    private Byte superAdmin;

    public static RoleAdminInput buildDefaultRoleAdminInput(Long tenantId, Long authUserId, Integer systemOrigin) {
        RoleAdminInput role = new RoleAdminInput();
        role.setRolename(TenantDefaultConstant.ADMIN_NAME);
        role.setRemarks(TenantDefaultConstant.ADMIN_NAME);
        role.setTenantId(tenantId);
        role.setSystemOrigin(systemOrigin);
        role.setSuperAdmin(TenantDefaultConstant.ADMIN_FLAG);
        role.setOpAuthUserId(authUserId);
        return role;
    }

    public static AuthRoleUpdateVO converterToAuthRoleUpdateVO(RoleAdminInput input) {
        if (input == null) {
            return null;
        }

        AuthRoleUpdateVO authRoleUpdateVO = new AuthRoleUpdateVO();

        authRoleUpdateVO.setRolename(input.getRolename());
        authRoleUpdateVO.setRemarks(input.getRemarks());
        authRoleUpdateVO.setCreateTime(input.getCreateTime());
        authRoleUpdateVO.setUpdateTime(input.getUpdateTime());
        authRoleUpdateVO.setTenantId(input.getTenantId());
        if (input.getSystemOrigin() != null) {
            authRoleUpdateVO.setSystemOrigin(input.getSystemOrigin().byteValue());
        }
        authRoleUpdateVO.setSuperAdmin(input.getSuperAdmin());

        return authRoleUpdateVO;
    }

}
