package net.xianmu.usercenter.facade.app;

import net.xianmu.usercenter.facade.app.dto.SkuDTO;
import net.xianmu.usercenter.facade.app.input.SkuQueryInput;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DemoQueryFacade {

    //此处调用他方接口,
//    @DubboReference
//    private SkuQueryProvider skuQueryProvider

    public List<SkuDTO> querySkuInfos(SkuQueryInput input){
        /*
        伪代码,这边没有做他方系统的demo,所以这里注释形式写了份代码
        1.旨在说明调用二方服务的方法,应用内不允许直接注入他方服务操作,只允许注入Facade层的组件进行操作
        2.并强调防腐层中不允许将他方服务使用的东西透传到自己应用中
        SkuQueryReq req = new SkuQueryReq();
        req.setSkuName(input.getSkuName())
        DubboResponse<List<SkuResp>> dubboResponse = QueryProvider.querySkuInfos(req);
        if(dubboResponse.isSuccess()) {
            List<SkuResp> skuResp = (List<SkuResp>)dubboResponse.getData();
            List<SkuDTO> skuDTOList = skuResp.stream().map(SkuConverter::resp2DTO).collect(Collectors.toList());
            return skuDTOList;
        }
         */
        return null;
    }
}
