package net.xianmu.usercenter.facade.auth;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserUpdateInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.provider.AuthUserCommandV2Provider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.client.provider.AuthUserUpdateProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.common.constants.AppConsts;
import net.xianmu.usercenter.common.enums.UserEnums;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.facade.auth.input.RoleAdminInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthUserFacade {

    @DubboReference
    private AuthUserProvider authUserProvider;

    @DubboReference
    private AuthUserCommandV2Provider authUserCommandV2Provider;

    @DubboReference
    private AuthUserQueryProvider authUserQueryProvider;

    @DubboReference
    private AuthUserUpdateProvider authUserUpdateProvider;



    /**
     * 更新用户信息
     *
     * @param input
     * @return
     */
    public Boolean updateUser(UserBase userBase) {
        DubboResponse<UserBase> response = authUserProvider.updateUser(this.getSystemOrigin(userBase.getSystemOrigin()), userBase);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        return Boolean.TRUE;
    }

    /**
     * 创建用户(租户)
     *
     * @param tenantAccountDTO
     * @return
     */
    public Long createUser(UserBase userBase, BaseUserExtend extend) {
        DubboResponse<UserBase> response = authUserCommandV2Provider.createUser(this.getSystemOrigin(userBase.getSystemOrigin()), userBase, extend);
        if (!response.isSuccess()) {
            log.error("创建用户失败");
            throw new BizException(response.getMsg());
        }

        UserBase data = response.getData();
        return data.getId();
    }


    /**
     * 创建指定租户的角色
     *
     * @param dto
     * @return
     */
    public Long addNewAdminRole(RoleAdminInput input) {
        Long authUserId = input.getOpAuthUserId();
        AuthRoleUpdateVO authRoleUpdateVO = RoleAdminInput.converterToAuthRoleUpdateVO(input);
        DubboResponse<AuthRole> response = authUserProvider.addRole(this.getSystemOrigin(input.getSystemOrigin()), authUserId, input.getTenantId(), authRoleUpdateVO);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        AuthRole data = response.getData();
        return data.getId();
    }


    public List<AuthUserResp> queryAuthUserList(Integer systemOrigin, Long tenantId, List<Long> bizIdList) {
        AuthUserQueryInput input = new AuthUserQueryInput();
        input.setBizIds(bizIdList);
        input.setTenantId(tenantId);
        DubboResponse<List<AuthUserResp>> response = authUserQueryProvider.queryAuthUserList(getSystemOrigin(systemOrigin), input);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }


    private SystemOriginEnum getSystemOrigin(Integer originEnum) {
        SystemOriginEnum systemOrigin = SystemOriginEnum.COSFO_OMS;
        if (Objects.nonNull(originEnum)) {
            systemOrigin = SystemOriginEnum.getSystemOriginByType(originEnum);
        }
        return systemOrigin;
    }


    /**
     * 获取对接auth的来源：
     * auth那边的来源并不是标识系统请求的来源，而是标识账号的体系：如 tms体系、大客户体系、cosfo-mall体系等等
     *
     * @return
     */
    public static Integer getAuthSystemOriginByType(UserEnums.SizeEnum sizeEnum, Long tenantId) {
        UserEnums.TypeEnum typeEnum = UserEnums.TypeEnum.getByTenantId(tenantId);
        // saas 业务
        if (UserEnums.TypeEnum.SAAS.equals(typeEnum)) {
            if (UserEnums.SizeEnum.TENANT.equals(sizeEnum)) {
                return SystemOriginEnum.COSFO_MANAGE.getType();
            } else if (UserEnums.SizeEnum.MERCHANT.equals(sizeEnum)) {
                return SystemOriginEnum.COSFO_MALL.getType();
            }
        }

        // 鲜沐业务
        if (UserEnums.SizeEnum.REGIONAL.equals(sizeEnum)) {
            return SystemOriginEnum.ADMIN.getType();
        } else if (UserEnums.SizeEnum.MERCHANT.equals(sizeEnum)) {
            return SystemOriginEnum.MALL.getType();
        }

        return SystemOriginEnum.COSFO_OMS.getType();
    }


    public UserBase createAccount(SystemOriginEnum systemOrigin, UserBase userBase, BaseUserExtend extend) {
        DubboResponse<UserBase> response= null;
        if(systemOrigin.getType().equals(SystemOriginEnum.COSFO_MALL.getType())) {
            response = authUserCommandV2Provider.createUser(systemOrigin, userBase, extend);
        } else {
            response = authUserProvider.createUser(systemOrigin, userBase, extend);
        }
        validateResponse(response);
        return response.getData();
    }

    private void validateResponse(DubboResponse<?> response) {
        if (!response.isSuccess()) {
            if (!StringUtils.isEmpty(response.getCode()) && response.getCode().startsWith(AppConsts.USER_CENTER_BIZ_CODE_PREFIX)) {
                throw new BizException(response.getMsg());
            } else {
                throw new ProviderException(response.getMsg());
            }
        }
    }

    public void updateAccount(SystemOriginEnum systemOriginEnum, AuthUserUpdateInput input) {
        DubboResponse<Boolean> response = authUserUpdateProvider.updateAuthUser(systemOriginEnum, input);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
    }
}
