package net.xianmu.usercenter.starter.tenant;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtCommandReq;
import net.xianmu.usercenter.client.tenant.provider.MerchantQueryProvider;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.MerchantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/27 10:40
 */
@Slf4j
public class TenantConfigTest extends BaseTest {

    @Autowired
    private MerchantQueryProvider merchantQueryProvider;

    @Test
    public void getTenantAndCompany() {
        DubboResponse<MerchantResultResp> company = merchantQueryProvider.getMerchantByTenantId(2L);
        log.info(JSON.toJSONString(company));
    }

    @Test
    public void getList() {
        MerchantQueryReq req = new MerchantQueryReq();
        req.setTenantIdList(Collections.singletonList(2L));
        DubboResponse<List<MerchantResultResp>> company = merchantQueryProvider.getTenantConfigs(req);
        log.info(JSON.toJSONString(company));
    }


}
