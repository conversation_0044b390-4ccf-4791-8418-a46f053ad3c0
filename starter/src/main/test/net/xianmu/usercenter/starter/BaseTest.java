package net.xianmu.usercenter.starter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/8/31 17:11
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BaseTest {

    @Autowired
    private MerchantStoreAccountCommandRepository accountCommandRepository;

    @Test
    public void createAccount(){
        String str = "{\n" +
        //"      \"id\": 67890,\n" +
        "      \"tenantId\": 2,\n" +
                "      \"storeId\": 12345,\n" +
                "      \"accountName\": \"Example Account 2\",\n" +
                "      \"phone\": \"**********\",\n" +
                "      \"type\": 1,\n" +
                "      \"registerTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"auditTime\": \"2023-06-05T12:45:00\",\n" +
                "      \"openId\": \"exampleopenid2\",\n" +
                "      \"unionId\": \"exampleunionid2\",\n" +
                "      \"createTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"updateTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"status\": 0,\n" +
                "      \"lastLoginTime\": \"2023-06-05T13:15:00\",\n" +
                "      \"deleteFlag\": 1\n" +
                "    }";
        MerchantStoreAccountEntity entity = JSON.parseObject(str, MerchantStoreAccountEntity.class);
        //entity.setId(9900L);
        final MerchantStoreAccountEntity entity1 = accountCommandRepository.create(entity);
        System.out.println(JSON.toJSON(entity1));
    }
}
