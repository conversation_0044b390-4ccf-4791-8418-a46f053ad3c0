package net.xianmu.usercenter.starter.merchat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import jdk.nashorn.internal.ir.LiteralNode;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.*;
import net.xianmu.usercenter.client.merchant.provider.v2.merchant.MerchantStoreQueryProviderV2;
import net.xianmu.usercenter.client.merchant.req.*;
import net.xianmu.usercenter.client.merchant.req.v2.merchant.MerchantBasicQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreExtResp;
import net.xianmu.usercenter.client.tenant.req.MerchantQueryReq;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;


/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/25 14:44
 */
@Slf4j
public class MerchantTest extends BaseTest {

    @Autowired
    private MerchantExtendQueryProvider queryProvider;

    @Autowired
    private MerchantStoreCommandProvider merchantStoreCommandProvider;

    @Autowired
    private MerchantStoreBatchCreateProvider merchantStoreBatchCreateProvider;

    @Autowired
    private MerchantStoreQueryProvider merchantStoreQueryProvider;

    @Autowired
    private MerchantStoreQueryProviderV2 merchantStoreQueryProviderV2;


    @Autowired
    private MerchantStoreExtCommandProvider commandProvider;


    @Autowired
    private MerchantStoreExtQueryProvider merchantStoreExtQueryProvider;
    @Test
    public void getAccountPage() {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(1L);
        req.setMId(344346L);
        req.setPhone("***********");
        req.setQueryManageAccount(true);
        List<PageSortInput> list = new ArrayList<>();
        PageSortInput pageSortInput = new PageSortInput();
        pageSortInput.setOrderBy("ms.id");
        pageSortInput.setSortBy("desc");
        list.add(pageSortInput);
        req.setSortList(list);
        //req.setEndAuditTime(LocalDateTime.now());
        //req.setExactStoreName("邹义测试店铺");
        log.info(JSON.toJSONString(queryProvider.getMerchantStoreAndExtendsByPrimaryKeys(req)));
    }

    @Test
    public void getMerchantStores() {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(2L);
        req.setPhone("434543");
        req.setStoreIdList(Collections.emptyList());
        log.info(JSON.toJSONString(merchantStoreQueryProvider.getMerchantStores(req)));
    }

    @Test
    public void getMerchantStoresByPrimaryKeys() {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(2L);
        req.setPhone("434543");
        req.setStoreIdList(Collections.emptyList());
        log.info(JSON.toJSONString(merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(req)));
    }

    @Test
    public void getMerchantStoresV2() {
        MerchantBasicQueryReq req = new MerchantBasicQueryReq();
        req.setTenantId(2L);
        req.setPhone("43454333333");
        req.setPhonePrefix("43454333333");
        //req.setExactStoreName("阿斯达");
        //req.setStoreIdList(Collections.emptyList());
        log.info(JSON.toJSONString(merchantStoreQueryProviderV2.queryMerchantStoresByKeywords(req)));
    }

    @Test
    public void getMerchantStoreAndExtendsByPrimaryKeys() {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMId(349912L);
        req.setTenantId(1L);
        log.info(JSON.toJSONString(queryProvider.getMerchantStoreAndExtendsByPrimaryKeys(req)));
    }

    @Test
    public void getMerchantStoreById() {
        log.info(JSON.toJSONString(merchantStoreQueryProvider.getMerchantStoreById(143220L)));
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(1L);
        //req.setMId(4L);
        req.setQueryManageAccount(true);
        log.info(JSON.toJSONString(queryProvider.getMerchantStoreAndExtends(req)));
        log.info(JSON.toJSONString(queryProvider.getMerchantStoreAndExtendsByPrimaryKeys(req)));
    }

    @Test
    public void getXmPage() {
        MerchantStorePageQueryReq req = new MerchantStorePageQueryReq();
        req.setPageIndex(1);
        req.setPageSize(10);
       // req.setMId(349912L);
        req.setStatus(0);
       // req.setExcludeStatusList(Collections.singletonList(1));
        log.info(JSON.toJSONString(queryProvider.getMerchantStorePageForXM(req)));
    }

    @Test
    public void getSaasPage() {
        MerchantStorePageQueryReq req = new MerchantStorePageQueryReq();
        //req.setTenantId(1L);
        req.setPageIndex(1);
        req.setPageSize(10);
        req.setTenantIds(Collections.singletonList(1L));
        req.setExcludeStatusList(Collections.singletonList(1));
        log.info(JSON.toJSONString(merchantStoreQueryProvider.getMerchantStorePage(req, new PageQueryReq())));
    }

    @Test
    public void createMerchant() {
        merchantStoreCommandProvider.createMerchantStoreInfo(SystemOriginEnum.COSFO_MANAGE, buildMerchantStoreDomainCommandReq());
    }

    @Test
    public void createMerchantBatch() {
        List<MerchantStoreDomainCommandReq> list = new ArrayList<>();
        list.add(buildMerchantStoreDomainCommandReq());
        DubboResponse<List<MerchantStoreBatchImportResp>> merchantStoreInfoBatch = merchantStoreBatchCreateProvider.createMerchantStoreInfoBatch(SystemOriginEnum.COSFO_MANAGE.type, list);
        log.info(JSON.toJSONString(merchantStoreInfoBatch));
    }

    @Test
    public void createMerchantBatchV2() {
        List<MerchantStoreDomainCommandReq> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            list.add(new MerchantStoreDomainCommandReq());
        }
        DubboResponse<List<MerchantStoreBatchImportResp>> merchantStoreInfoBatch = merchantStoreBatchCreateProvider.createMerchantStoreInfoBatch(SystemOriginEnum.COSFO_MANAGE.type, list);
        log.info(JSON.toJSONString(merchantStoreInfoBatch));
    }



    private MerchantStoreDomainCommandReq buildMerchantStoreDomainCommandReq() {
        String s = "{\n" +
                //"  \"storeId\": 12345,\n" +
                "  \"groupId\": 67890,\n" +
                "  \"merchantStore\": {\n" +
                "    \"id\": 98765,\n" +
                "    \"tenantId\": 2,\n" +
                "    \"storeName\": \"myStoredasewqwq\",\n" +
                "    \"type\": 1,\n" +
                "    \"registerTime\": \"2023-06-05T10:30:00\",\n" +
                "    \"status\": 0,\n" +
                "    \"auditRemark\": \"Pending review\",\n" +
                "    \"remark\": \"Example remark\",\n" +
                "    \"auditTime\": \"2023-06-06T09:45:00\",\n" +
                "    \"billSwitch\": 0,\n" +
                "    \"onlinePayment\": 1,\n" +
                "    \"balanceAuthority\": 1,\n" +
                //"    \"storeNo\": \"ABC123\"\n" +
                "  },\n" +
                "  \"merchantAddressList\": [\n" +
                "    {\n" +
                //"      \"id\": 24680,\n" +
                "      \"tenantId\": 2,\n" +
                //"      \"storeId\": 12345,\n" +
                "      \"province\": \"Province\",\n" +
                "      \"city\": \"City\",\n" +
                "      \"area\": \"Area\",\n" +
                "      \"address\": \"123Street\",\n" +
                "      \"houseNumber\": \"110\",\n" +
                "      \"poiNote\": \"Example Poi Note\",\n" +
                "      \"createTime\": \"2023-06-05T11:00:00\",\n" +
                "      \"updateTime\": \"2023-06-05T11:30:00\",\n" +
                "      \"defaultFlag\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"auditFlag\": 1,\n" +
                "      \"merchantContactList\": [\n" +
                "        {\n" +
                //"          \"id\": 13579,\n" +
                "          \"tenantId\": 2,\n" +
                //"          \"addressId\": 24680,\n" +
                "          \"name\": \"John Doe\",\n" +
                "          \"phone\": \"**********\",\n" +
                "          \"defaultFlag\": 1,\n" +
                "          \"createTime\": \"2023-06-05T11:05:00\",\n" +
                "          \"updateTime\": \"2023-06-05T11:05:00\"\n" +
                "        },\n" +
                "        {\n" +
                //"          \"id\": 24680,\n" +
                "          \"tenantId\": 2,\n" +
                //"          \"addressId\": 24680,\n" +
                "          \"name\": \"Jane Smith\",\n" +
                "          \"phone\": \"**********\",\n" +
                "          \"defaultFlag\": 0,\n" +
                "          \"createTime\": \"2023-06-05T11:10:00\",\n" +
                "          \"updateTime\": \"2023-06-05T11:10:00\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"merchantStoreAccountList\": [\n" +
                "    {\n" +
                //"      \"id\": 12345,\n" +
                "      \"tenantId\": 2,\n" +
                //"      \"storeId\": 12345,\n" +
                "      \"accountName\": \"Example Account 1\",\n" +
                "      \"phone\": \"**********\",\n" +
                "      \"type\": 0,\n" +
                "      \"registerTime\": \"2023-06-05T12:00:00\",\n" +
                "      \"auditTime\": \"2023-06-05T12:30:00\",\n" +
                "      \"openId\": \"exampleopenid1\",\n" +
                "      \"unionId\": \"exampleunionid1\",\n" +
                "      \"createTime\": \"2023-06-05T12:00:00\",\n" +
                "      \"updateTime\": \"2023-06-05T12:00:00\",\n" +
                "      \"status\": 1,\n" +
                "      \"lastLoginTime\": \"2023-06-05T13:00:00\",\n" +
                "      \"deleteFlag\": 0\n" +
                "    },\n" +
                "    {\n" +
                //"      \"id\": 67890,\n" +
                "      \"tenantId\": 2,\n" +
                //"      \"storeId\": 12345,\n" +
                "      \"accountName\": \"Example Account 2\",\n" +
                "      \"phone\": \"**********\",\n" +
                "      \"type\": 1,\n" +
                "      \"registerTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"auditTime\": \"2023-06-05T12:45:00\",\n" +
                "      \"openId\": \"exampleopenid2\",\n" +
                "      \"unionId\": \"exampleunionid2\",\n" +
                "      \"createTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"updateTime\": \"2023-06-05T12:15:00\",\n" +
                "      \"status\": 0,\n" +
                "      \"lastLoginTime\": \"2023-06-05T13:15:00\",\n" +
                "      \"deleteFlag\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}\n";
        MerchantStoreDomainCommandReq req = JSONObject.parseObject(s, MerchantStoreDomainCommandReq.class);
        return req;
    }

    @Test
    public void MerchantStoreExtCommandaDD() {
        MerchantStoreExtCommandReq req = new MerchantStoreExtCommandReq();
        req.setMId(349479L);
        req.setProKey("123123");
        req.setProValue("2");
        commandProvider.addOrUpdateMerchantStoreExt(req);
    }

    @Test
    public void MerchantStoreExtCommandQuery() {
        MerchantStoreExtQueryReq req = new MerchantStoreExtQueryReq();
        req.setMIds(Collections.singletonList(349479L));
        req.setProKey("123123");
        DubboResponse<List<MerchantStoreExtResp>> merchantStoreExtByQuery = merchantStoreExtQueryProvider.getMerchantStoreExtByQuery(req);
        log.info("MerchantStoreExtCommandQuery:{}", JSONUtil.toJsonStr(merchantStoreExtByQuery));
    }
}
