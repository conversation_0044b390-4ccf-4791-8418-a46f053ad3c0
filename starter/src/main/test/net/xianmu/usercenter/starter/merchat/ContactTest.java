package net.xianmu.usercenter.starter.merchat;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/10/10 18:31
 */
@Slf4j
public class ContactTest  extends BaseTest {


    @Autowired
    private MerchantContactQueryProvider contactQueryProvider;

    @Test
    public void getAccountPage() {
        DubboResponse<List<MerchantContactResultResp>> contacts = contactQueryProvider.getMerchantContactsByStoreId(2L, 143106L);
        log.info(JSON.toJSONString(contacts));
    }
}
