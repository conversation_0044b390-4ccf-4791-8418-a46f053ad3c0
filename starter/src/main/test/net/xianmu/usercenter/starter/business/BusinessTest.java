package net.xianmu.usercenter.starter.business;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationQueryProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/10/8 16:45
 */
@Slf4j
public class BusinessTest extends BaseTest {


    @Autowired
    private BusinessInformationQueryProvider provider;

    @Test
    public void getAccountPage() {
        BusinessInformationQueryReq req = new BusinessInformationQueryReq();
        req.setBizIdList(Arrays.asList(2L,2L));
        req.setType(1);
        DubboResponse<List<BusinessInformationResultResp>> page = provider.getBusinessInfos(req);
        log.info(JSON.toJSONString(page));
    }
}
