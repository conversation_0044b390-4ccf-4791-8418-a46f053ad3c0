package net.xianmu.usercenter.starter.merchat;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/25 14:44
 */
@Slf4j
public class AddressTest extends BaseTest {

    @Autowired
    private MerchantAddressQueryProvider queryProvider;

    @Test
    public void getMerchantAddressList() {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(2L);
        req.setStoreId(1L);
        final DubboResponse<List<MerchantAddressResultResp>> merchantAddressList = queryProvider.getMerchantAddressList(req);
        log.info(JSON.toJSONString(merchantAddressList));
    }

    @Test
    public void getAddressAndContacts() {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(1L);
        req.setMId(344346L);
        log.info(JSON.toJSONString(queryProvider.getAddressAndContacts(req)));
    }
}
