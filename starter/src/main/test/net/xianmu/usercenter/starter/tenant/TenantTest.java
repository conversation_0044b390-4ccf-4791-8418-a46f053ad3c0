package net.xianmu.usercenter.starter.tenant;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/27 10:40
 */
@Slf4j
public class TenantTest extends BaseTest {

    @Autowired
    private TenantQueryProvider tenantQueryProvider;

    @Test
    public void getTenantAndCompany() {
        DubboResponse<TenantAndBusinessInfoResultResp> company = tenantQueryProvider.getTenantAndCompany(2L);
        log.info(JSON.toJSONString(company));
    }

    @Test
    public void getPage() {
        TenantQueryReq req = new TenantQueryReq();
        req.setContactName("ce");
        PageQueryReq req1 = new PageQueryReq();
        req1.setPageIndex(1);
        req1.setPageSize(10);
        Object company = tenantQueryProvider.getTenantsPage(req, req1);
        log.info(JSON.toJSONString(company));
    }

    @Test
    public void getTenants() {
        TenantQueryReq req = new TenantQueryReq();
        req.setType(0);
        final DubboResponse<List<TenantResultResp>> tenants = tenantQueryProvider.getTenants(req);
        log.info(JSON.toJSONString(tenants));
    }
}
