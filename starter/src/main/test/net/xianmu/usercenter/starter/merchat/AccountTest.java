package net.xianmu.usercenter.starter.merchat;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreAccountQueryProviderImpl;
import net.xianmu.usercenter.inbound.merchant.provider.MerchantStoreQueryProviderImpl;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/25 14:44
 */
@Slf4j
public class AccountTest extends BaseTest {

    @Autowired
    private MerchantStoreAccountQueryProviderImpl accountQueryProvider;

    @Autowired
    private MerchantStoreQueryProviderImpl merchantStoreQueryProvider;

    @Test
    public void getMerchantStores() {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountPageReq();
        req.setMIdList(Collections.singletonList(12L));
        log.info(JSON.toJSONString(accountQueryProvider.getMerchantStoreAccounts(req)));

    }
}
