package net.xianmu.usercenter.starter.tenant;

import com.alibaba.fastjson.JSON;
import com.aliyun.tea.Tea;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.starter.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/27 10:40
 */
@Slf4j
public class TenantAccountTest extends BaseTest {

    @Autowired
    private TenantAccountQueryProvider queryProvider;

    @Test
    public void getTenantAndCompany() {
        final TenantAccountQueryReq build = TenantAccountQueryReq.builder().idList(Collections.singletonList(2L)).build();
        final DubboResponse<List<TenantAccountResultResp>> tenantAccounts = queryProvider.getTenantAccounts(build);
        log.info(JSON.toJSONString(tenantAccounts));
    }

    @Test
    public void getTenantAccounts() {
        final TenantAccountQueryReq build = TenantAccountQueryReq.builder().email("<EMAIL>").build();
        final DubboResponse<List<TenantAccountResultResp>> tenantAccounts = queryProvider.getTenantAccounts(build);
        log.info(JSON.toJSONString(tenantAccounts));
    }


    @Test
    public void getTenantAccountsPage() {
        final TenantAccountListQueryReq build = TenantAccountListQueryReq.builder().tenantId(2L).username("<EMAIL>").build();
        PageQueryReq req = new PageQueryReq();
        final DubboResponse<PageInfo<TenantAccountResultResp>> tenantAccounts = queryProvider.getTenantAccountsPage(build, req);
        log.info(JSON.toJSONString(tenantAccounts));
    }
}
