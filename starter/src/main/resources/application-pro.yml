server:
  port: 80
  servlet:
    context-path: /usercenter
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: cosfodb
    password: cosfo612022-
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://mse-c5bf59b0-nacos-ans.mse.aliyuncs.com:8848
    parameters:
      namespace: 93cffb5c-f43b-4456-8e2b-13312acd2c7c
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

redis:
  host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
  jedis:
    pool:
      max-active: 40
      max-idle: 20
      max-wait: 5000
      min-idle: 5
  password: summerfarm0619#
  port: 6379
  timeout: 5000
  database: 0
rocketmq:
  consumer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************
  name-server: http://MQ_INST_1788664839736465_BXz3eVFS.cn-hangzhou.mq-internal.aliyuncs.com:8080
  producer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    enableMsgTrace: false
    group: GID_manage
    secret-key: ******************************
    sendMsgTimeout: 30000