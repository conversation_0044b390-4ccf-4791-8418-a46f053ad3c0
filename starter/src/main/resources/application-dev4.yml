server:
  port: 80
  servlet:
    context-path: /usercenter
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ********************************************************************************************************************************
    username: test
    password: xianmu619
  redis:
    host: **************
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 3
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://********:11000
    parameters:
      namespace: cfccb911-1306-4ea7-8a9f-18436f9dd245
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
xm:
  oss:
    # 生产测试不一样
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true

rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: 159.27.10.66:9879
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
