server:
  port: 80
  servlet:
    context-path: /usercenter
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************
          ##driver-class-name: com.mysql.jdbc.Driver
          ##url: ****************************************************************************************************************************
          username: test
          password: xianmu619
        xm:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.jdbc.Driver
          url: ******************************************************************************************************************************************************
          username: test
          password: xianmu619

  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）

  schedulerx2:
    appKey: JTjSKH4CYszuo7Lin5jAVA==
    endpoint: acm.aliyun.com
    groupId: user-center
    namespace: d470595b-6520-4833-a158-239340a08eb2
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
xm:
  oss:
    # 生产测试不一样
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true

rocketmq:
  consumer:
    access-key: 'Rocketmq'
    secret-key: 'Rocketmq'
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: 'Rocketmq'
    group: GID_manage
    secret-key: 'Rocketmq'
    sendMsgTimeout: 10000