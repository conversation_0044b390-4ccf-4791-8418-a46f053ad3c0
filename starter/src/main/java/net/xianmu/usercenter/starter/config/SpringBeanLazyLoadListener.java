package net.xianmu.usercenter.starter.config;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.inbound.common.mq.listener.BinLogOrderlyListener;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 17:06
 */
@Component
@Slf4j
public class SpringBeanLazyLoadListener implements ApplicationListener<ContextRefreshedEvent>, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        log.info("spring 容器初始化完成");
        applicationContext.getBean(BinLogOrderlyListener.class);
    }
}
