package net.xianmu.usercenter.starter.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> ct
 * create at:  2022/2/8  11:53
 * offline 数据源配置
 */
//@Configuration
@Slf4j
//@MapperScan(basePackages = "net.xianmu.usercenter.infrastructure.xianmu.**", sqlSessionFactoryRef = "xmSqlSessionFactory")
public class XianMuDbConfig {

    @Value(value = "${xm.datasource.driverClassName:}")
    private String driverClassName;
    @Value(value = "${xm.datasource.url:}")
    private String url;
    @Value(value = "${xm.datasource.username:}")
    private String username;
    @Value(value = "${xm.datasource.password:}")
    private String password;
    @Value(value = "${xm.datasource.minIdle:}")
    private int minIdle;
    @Value(value = "${xm.datasource.initialSize:}")
    private int initialSize;
    @Value(value = "${xm.datasource.maxActive:}")
    private int maxActive;
    @Value(value = "${xm.datasource.maxWait:}")
    private int maxWait;



    @Bean("xmDruidDataSource")
    public DruidDataSource druidDataSource() {
        log.info("--------开始初始化鲜沐数据源-------");
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setMaxWait(maxWait);
        return druidDataSource;
    }


    @Bean("xmSqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("xmDruidDataSource") DruidDataSource druidDataSource) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(druidDataSource);
        return sqlSessionFactoryBean;
    }

}
