
package net.xianmu.usercenter.starter.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.common.DbTableDml;
import net.xianmu.usercenter.application.datatransfer.MerchantAddressTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreExtTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.RegionalOrganizationTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.XmMerchantAdapterTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountToAuthServiceImpl;
import net.xianmu.usercenter.common.config.MdcDecorator;
import net.xianmu.usercenter.common.constants.DBTableName;
import net.xianmu.usercenter.inbound.common.mq.factory.DbTableDmlFactory;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
//@EnableAsync
@EnableAutoConfiguration
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@DubboComponentScan("net.xianmu.usercenter.**.provider")
@ComponentScan(value = {"net.xianmu.usercenter.*"})
@MapperScan(value = {"net.xianmu.infrastructure.**"})
public class MainConfiguration {

    @PostConstruct
    public void init() {
        log.info("MainConfiguration init...");
    }

    @Bean("dbTableDmlFactory")
    public DbTableDmlFactory dbTableDmlFactory(RegionalOrganizationTransServiceImpl regionalOrganizationTransService, MerchantStoreTransServiceImpl merchantStoreCommandService, MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService, MerchantAddressTransServiceImpl merchantAddressTransService, MerchantStoreAccountToAuthServiceImpl merchantStoreAccountToAuthService, MerchantStoreExtTransServiceImpl merchantStoreExtTransService, XmMerchantAdapterTransServiceImpl xmMerchantAdapterTransService){
        DbTableDmlFactory tableDmlFactory = new DbTableDmlFactory();
        Map<String, DbTableDml> dbTableDmlHashMap = new ConcurrentHashMap<>(64);
        dbTableDmlHashMap.put(DBTableName.MERCHANT,merchantStoreCommandService);
        dbTableDmlHashMap.put(DBTableName.ADMIN, regionalOrganizationTransService);
        dbTableDmlHashMap.put(DBTableName.CONTACT, merchantAddressTransService);
        dbTableDmlHashMap.put(DBTableName.MERCHANT_SUB_ACCOUNT, merchantStoreAccountTransService);
        dbTableDmlHashMap.put(DBTableName.MERCHANT_STORE_ACCOUNT, merchantStoreAccountToAuthService);
        dbTableDmlHashMap.put(DBTableName.MERCHANT_STORE_EXT, merchantStoreExtTransService);
        dbTableDmlHashMap.put(DBTableName.XM_MERCHANT_ADAPTER, xmMerchantAdapterTransService);
        tableDmlFactory.setDbTableDmlMap(dbTableDmlHashMap);
        return tableDmlFactory;
    }


    @Bean("dataSyncThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor initTaskThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(20);
        // 设置队列容量
        executor.setQueueCapacity(1000);
        // 设置线程活跃时间(秒)
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("asyncTask-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    @Bean("merchantBatchCreateThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor initMerchantBatchCreateThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(6);
        executor.setMaxPoolSize(6);
        // 设置队列容量
        executor.setQueueCapacity(1000);
        // 设置线程活跃时间(秒)
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("asyncMerchantBatchCreate-");
        // 配置Mdc装饰
        executor.setTaskDecorator(new MdcDecorator());
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    
    @Bean
    public SimpleApplicationEventMulticaster applicationEventMulticaster(BeanFactory beanFactory, @Qualifier("dataSyncThreadPoolTaskExecutor") ThreadPoolTaskExecutor executor){
        SimpleApplicationEventMulticaster applicationEventMulticaster = new SimpleApplicationEventMulticaster(beanFactory);
        applicationEventMulticaster.setTaskExecutor(executor);
        return applicationEventMulticaster;
    }
}
