package net.xianmu.usercenter.starter;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.starter.config.MainConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

@Slf4j
@SpringBootApplication
@Import({MainConfiguration.class})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("#############服务启动成功##############");
    }
}
