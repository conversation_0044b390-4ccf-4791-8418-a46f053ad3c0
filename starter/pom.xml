<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>usercenter</artifactId>
        <groupId>net.xianmu</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>usercenter-starter</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                    <groupId>org.mybatis.spring.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-domain</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-domain</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-application</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-domain</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-sentinel-support</artifactId>
        </dependency>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>net.xianmu.usercenter.starter.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
