<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>net.xianmu</groupId>
    <artifactId>usercenter</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>


    <properties>
        <maven.compiler.sourcapp.versione>8</maven.compiler.sourcapp.versione>
        <maven.compiler.target>8</maven.compiler.target>
        <app.version>1.0.0-SNAPSHOT</app.version>
        <app.client.version>1.2.7-RELEASE</app.client.version>
        <xianmu-common.version>1.1.15-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.14-RELEASE</xianmu-dubbo.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-task.version>1.0.3</xianmu-task.version>
        <xianmu-oss.version>1.0.2</xianmu-oss.version>
        <xianmu-mq.version>1.1.7</xianmu-mq.version>


        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <page.version>1.3.1</page.version>
        <mysql-connector.version>8.0.22</mysql-connector.version>
        <druid.version>1.1.22</druid.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <schedulerx2.version>1.7.4</schedulerx2.version>
        <redisson.version>3.11.1</redisson.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <nacos-config.version>0.2.12</nacos-config.version>
        <xianmu-redis.version>1.0.0</xianmu-redis.version>
        <sentinel.version>1.0.1-RELEASE</sentinel.version>
        <xianmu-i18n-sdk.version>1.0.2-RELEASE</xianmu-i18n-sdk.version>


    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-facade</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-domain</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-starter</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>${xianmu-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-redis-support</artifactId>
                <version>${xianmu-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.i18n</groupId>
                <artifactId>xianmu-i18n-sdk</artifactId>
                <version>${xianmu-i18n-sdk.version}</version>
            </dependency>


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.3.2</version>
            </dependency>
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.7.10</version>
            </dependency>
            <!-- POJO转换工具 begin -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- POJO转换工具 end -->
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>${schedulerx2.version}</version>
                <!--如果用的是logback，需要把log4j和log4j2排除掉 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>1.17.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>

            <!-- emoji -->
            <dependency>
                <groupId>com.vdurmont</groupId>
                <artifactId>emoji-java</artifactId>
                <version>5.1.1</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>    

<modules>
    <module>application</module>
    <module>domain</module>
    <module>infrastructure</module>
    <module>common</module>
    <module>facade</module>
    <module>starter</module>
  </modules>
</project>
