package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.BusinessInformationEnum;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.common.util.DateUtil;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.businessInfo.domain.BusinessInformationDomainService;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.domain.regional.domain.RegionalOrganizationDomainService;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 10:50
 */

@Service
@Slf4j
public class RegionalOrganizationTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;
    @Autowired
    private RegionalOrganizationDomainService regionalOrganizationDomainService;
    @Autowired
    private BusinessInformationDomainService businessInformationDomainService;
    @Autowired
    private DataTransferRepository adminRepository;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.warn("binlog物理删除！");
            return;
        }

        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        String adminIdStr = data.get("admin_id");
        Long adminId = Long.valueOf(adminIdStr);
        Long tenantId = TenantDefaultConstant.XIAN_MU_TENANT_ID;
        if (!isAdmin(data)) {
            log.info("内部账户无需处理，adminId:{}", adminIdStr);
            return;
        }

        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            this.createForBinLog(data, tenantId);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            // 先查映射，如果没有，那就走新建流程
            RegionalOrganizationEntity entity = regionalOrganizationQueryRepository.selectByAdminAndTenant(adminId, tenantId);
            if (null == entity) {
                this.createForBinLog(data, tenantId);
                return;
            }
            this.updateForBinLog(data, old, entity);
        } else {
            // 删除操作
            log.warn("binlog物理删除！");
        }
    }


    private void createForBinLog(Map<String, String> data, Long tenantId) {
        // 幂等
        Long adminId = Long.valueOf(data.get("admin_id"));
        RegionalOrganizationEntity regionalOrganization = regionalOrganizationQueryRepository.selectByAdminAndTenant(adminId, tenantId);
        if (null != regionalOrganization) {
            log.info("当前大客户已存在，无需重复创建!adminId:{}", adminId);
            return;
        }

        RegionalOrganizationEntity input = converterToRegionalOrganizationEntity(data, tenantId);
        RegionalOrganizationEntity entity = regionalOrganizationDomainService.createForBinLog(input);

        // 保存工商信息
        BusinessInformationEntity informationCommandInput = this.convertToBusinessInfo(data);
        if (informationCommandInput != null) {
            informationCommandInput.setType(BusinessInformationEnum.TypeEnum.REGIONAL_ORGANIZATION.getCode());
            informationCommandInput.setBizId(entity.getId());
            informationCommandInput.setTenantId(entity.getTenantId());
            businessInformationDomainService.createOrUpdateForBinLog(informationCommandInput);
        }
    }

    private void updateForBinLog(Map<String, String> data, Map<String, String> old, RegionalOrganizationEntity entity) {

        // 更新了用户领域
        if (isUpdateAdmin(old)) {
            RegionalOrganizationEntity input = converterToRegionalOrganizationEntity(data, entity.getTenantId());
            input.setId(entity.getId());
            regionalOrganizationDomainService.updateForBinLog(input);
        }
        // 更新了工商域
        if (isUpdateBusinessInfo(old)) {
            BusinessInformationEntity informationCommandInput = this.convertToBusinessInfo(data);
            if (null != informationCommandInput) {
                informationCommandInput.setBizId(entity.getId());
                informationCommandInput.setType(BusinessInformationEnum.TypeEnum.REGIONAL_ORGANIZATION.getCode());
                businessInformationDomainService.createOrUpdateForBinLog(informationCommandInput);
            }
        }
    }


    private RegionalOrganizationEntity converterToRegionalOrganizationEntity(Map<String, String> data, Long tenantId) {
        return RegionalOrganizationEntity.builder()
                .adminId(Long.valueOf(data.get("admin_id")))
                .phone(data.get("phone"))
                .organizationName(data.get("name_remakes") == null ? data.get("realname") : data.get("name_remakes"))
                .source(RegionalOrganizationEnums.Source.XIANMU.getCode())
                .size(RegionalOrganizationEnums.Size.ADMIN.getCode())
                .status(Integer.valueOf(data.get("is_disabled")))
                .tenantId(tenantId)
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), LocalDateTime.now()))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), LocalDateTime.now()))
                .build();
    }

    private BusinessInformationEntity convertToBusinessInfo(Map<String, String> map) {
        String businessLicense = map.get("business_license_address");
        String creditCode = map.get("credit_code");
        String companyName = map.get("realname");
        if (StrUtil.isEmpty(businessLicense) && StrUtil.isEmpty(creditCode) && StrUtil.isEmpty(companyName)) {
            return null;
        }
        return BusinessInformationEntity.builder()
                .companyName(companyName)
                .phone(map.get("phone"))
                .creditCode(map.get("credit_code"))
                .businessLicense(map.get("business_license_address"))
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("create_time"), LocalDateTime.now()))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("update_time"), LocalDateTime.now()))
                .build();
    }

    /**
     * 目前admin表的数据可分为大客户以及内部账户
     * 判断admin是否为大客户
     *
     * @param data
     * @return
     */
    private boolean isAdmin(Map<String, String> data) {
        if (null == data) {
            return false;
        }
        // 大客户类型非空，代表为大客户
        return data.get("admin_type") != null;
    }

    private boolean isUpdateBusinessInfo(Map<String, String> old) {
        return ValidateUtil.isContains(old, "credit_code", "business_license", "realname", "phone");
    }

    private boolean isUpdateAdmin(Map<String, String> old) {
        return ValidateUtil.isContains(old, "phone", "name_remakes", "is_disabled");
    }


    @Override
    public List<Map<String, Object>> selectDataList(Long adminId, Integer offset) {
        return adminRepository.selectAdminList(adminId, offset);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doTransfer(Map<String, Object> data, boolean isCreate) {
        if (CollUtil.isEmpty(data)) {
            return;
        }
        if (data.get("admin_type") == null) {
            log.info("内部账户无需处理，adminId:{}", data.get("admin_id"));
            return;
        }


        final Map<String, String> stringMap = MapUtil.convertToStringMap(data);
        if (isCreate) {
            this.createForBinLog(stringMap, TenantDefaultConstant.XIAN_MU_TENANT_ID);
        } else {
            Long adminId = Long.valueOf(stringMap.get("admin_id"));
            RegionalOrganizationEntity entity = regionalOrganizationQueryRepository.selectByAdminAndTenant(adminId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
            this.updateForBinLog(stringMap, stringMap, entity);
        }
    }
}
