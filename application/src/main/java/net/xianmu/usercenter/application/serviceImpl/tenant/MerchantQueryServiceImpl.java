package net.xianmu.usercenter.application.serviceImpl.tenant;

import net.xianmu.usercenter.api.tenant.service.MerchantQueryService;
import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/25 11:16
 */
@Service
public class MerchantQueryServiceImpl implements MerchantQueryService {


    @Autowired
    private MerchantQueryRepository merchantQueryRepository;

    @Override
    public MerchantEntity getMerchantByTenantId(Long tenantId) {
        return merchantQueryRepository.selectByTenantId(tenantId);
    }

    @Override
    public List<MerchantEntity> getTenantConfigs(MerchantQueryInput input) {
        return merchantQueryRepository.selectByCondition(input);
    }
}
