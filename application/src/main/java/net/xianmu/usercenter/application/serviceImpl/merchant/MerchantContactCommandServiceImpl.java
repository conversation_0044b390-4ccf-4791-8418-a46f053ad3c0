package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.usercenter.api.merchant.service.MerchantContactCommandService;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantContactDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2023-05-29 17:08:24
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantContactCommandServiceImpl implements MerchantContactCommandService {

    @Autowired
    private MerchantContactDomainService merchantContactDomainService;
    @Autowired
    private MerchantContactCommandRepository merchantContactCommandRepository;

    @Override
    public Long create(MerchantContactCommandInput input) {
        MerchantContactEntity entity = merchantContactDomainService.create(input);
        return entity.getId();
    }

    @Override
    public Boolean update(MerchantContactCommandInput input) {
        return merchantContactDomainService.update(input);
    }

    @Override
    public Boolean remove(MerchantContactCommandInput input) {
        ValidateUtil.paramValidate(input, "id");
        return merchantContactCommandRepository.delete(input.getId());
    }

    @Override
    public Boolean removeBatch(MerchantContactCommandInput input) {
        ValidateUtil.paramValidate(input, "idList");
        return merchantContactCommandRepository.deleteBatch(input.getIdList());
    }
}