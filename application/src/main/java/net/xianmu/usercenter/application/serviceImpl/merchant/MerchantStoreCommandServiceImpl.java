package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.redis.support.lock.service.XmLockTemplate;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreCommandService;
import net.xianmu.usercenter.common.config.MdcDecorator;
import net.xianmu.usercenter.common.config.NacosPropertiesHolder;
import net.xianmu.usercenter.common.constants.RedisConstant;
import net.xianmu.usercenter.common.dto.MerchantStoreBatchImportDTO;
import net.xianmu.usercenter.common.enums.MerchantStoreEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreDomainCommandInput;
import net.xianmu.usercenter.common.util.SpringContextHolder;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:58
 */
@Service
@Slf4j
public class MerchantStoreCommandServiceImpl implements MerchantStoreCommandService {

    @Autowired
    private MerchantStoreDomainService merchantStoreDomainService;
    @Resource
    private XmLockTemplate xmLockTemplate;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;


    @Override
    public Long createMerchantStoreInfo(MerchantStoreDomainCommandInput input) {
        // 参数校验、转换
        this.paramTrans(input);

        // 加锁防重
        MerchantStoreCommandInput merchantStore = input.getMerchantStore();
        String storeName = merchantStore.getStoreName();
        Long tenantId = merchantStore.getTenantId();
        String lockKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_CREATE + tenantId + ":" + storeName;
        return xmLockTemplate.executeTryLock(lock -> {
            if (lock) {
                // 创建门店信息
                return SpringContextHolder.getBean("merchantStoreCommandServiceImpl", MerchantStoreCommandServiceImpl.class).doCreate(input);
            }
            throw new BizException("当前租户:【" + tenantId + "】下的门店:【" + storeName + "】正在处理，请勿重复点击");
        }, lockKey, 180000);
    }


    private void paramTrans(MerchantStoreDomainCommandInput input) {
        log.info("应用层接收到新建门店请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "merchantStore", "merchantAddressList", "merchantStoreAccountList");
        MerchantStoreCommandInput merchantStore = input.getMerchantStore();
        if (merchantStore.getTenantId() == null) {
            throw new ParamsException("请求参数异常: 租户id为空!");
        }
        if (merchantStore.getStoreName() == null) {
            throw new ParamsException("请求参数异常: 门店名称为空!");
        }

        if (SystemOriginEnum.COSFO_MANAGE.type.equals(input.getSystemOrigin())) {
            // saas来源，设置默认免审
            if (merchantStore.getStatus() == null) {
                merchantStore.setStatus(MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode());
                merchantStore.setRegisterTime(LocalDateTime.now());
                merchantStore.setAuditTime(LocalDateTime.now());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long doCreate(MerchantStoreDomainCommandInput input) {
        MerchantStoreEntity entity;
        if (input.isBatchCreateFlag()) {
            entity = merchantStoreDomainService.createMerchantStoreInfoBatch(input);
        } else {
            entity = merchantStoreDomainService.createMerchantStoreInfo(input);
        }
        return entity.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMerchantStoreInfo(MerchantStoreDomainCommandInput input) {
        return merchantStoreDomainService.updateMerchantStoreInfo(input);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMerchantStore(MerchantStoreCommandInput input) {
        return merchantStoreDomainService.updateMerchantStore(input);
    }


    /**
     * // todo 现在这种批量的方式只能免除多次rpc的网络开销，本身循环调用创建接口有很大的弊端，待后续优化
     * 方向：
     * 1.减小事务范围
     * 2.数据库批处理
     * 3.并发处理（这里由于获取到的数据库连接不一致，要格外注意校验的问题）
     * 4.分阶段操作，尽量避免在事务内rpc
     *
     * @param list
     */
    @Override
    public List<MerchantStoreBatchImportDTO> createMerchantStoreInfoBatch(List<MerchantStoreDomainCommandInput> list) {
        if (CollUtil.isEmpty(list)) {
            log.warn("批量导入门店数据为空!");
            return Collections.emptyList();
        }
        if (list.size() > 500) {
            log.warn("数据量过大，请分多次处理!");
            throw new BizException("数据量过大，请分多次处理!");
        }

        // 初始化行号
        Long rowNum = 1L;
        for (MerchantStoreDomainCommandInput input : list) {
            input.setBatchCreateRowNum(rowNum);
            rowNum++;
        }

        int batchSize = nacosPropertiesHolder.getMerchantBatchCreateSize();
        List<MerchantStoreBatchImportDTO> resultList = new CopyOnWriteArrayList<>();
        if (batchSize > 1) {
            // 专用线程池，避免共用线程池造成任务堆积
            log.info("异步处理批量导入门店请求, batchSize:{}", batchSize);
            List<List<MerchantStoreDomainCommandInput>> splitAvgList = ListUtil.splitAvg(list, batchSize);
            ThreadPoolTaskExecutor slowExecutor = SpringContextHolder.getBean("merchantBatchCreateThreadPoolTaskExecutor", ThreadPoolTaskExecutor.class);
            try {
                CompletableFuture<Void> allOf = CompletableFuture.allOf(
                        splitAvgList.stream()
                                .map(input -> CompletableFuture.supplyAsync(() -> this.innerCreateMerchantStoreInfoBatch(input), slowExecutor)
                                        .thenAccept(resultList::addAll))
                                .toArray(CompletableFuture[]::new)
                );
                allOf.get(60, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("门店批量导入失败", e);
            }
            resultList.sort(Comparator.comparing(MerchantStoreBatchImportDTO::getRowNum));
        } else {
            log.info("同步处理批量导入门店请求, batchSize:{}", batchSize);
            resultList.addAll(innerCreateMerchantStoreInfoBatch(list));
        }
        return resultList;
    }

    private List<MerchantStoreBatchImportDTO> innerCreateMerchantStoreInfoBatch(List<MerchantStoreDomainCommandInput> batchInputs) {
        List<MerchantStoreBatchImportDTO> resultList = new ArrayList<>();
        if (CollUtil.isNotEmpty(batchInputs)) {
            for (MerchantStoreDomainCommandInput input : batchInputs) {
                resultList.add(createMerchantStoreInfoWithTryCatch(input));
            }
        }
        return resultList;
    }


    public MerchantStoreBatchImportDTO createMerchantStoreInfoWithTryCatch(MerchantStoreDomainCommandInput input) {
        MerchantStoreBatchImportDTO dto = new MerchantStoreBatchImportDTO();
        if (input == null || input.getMerchantStore() == null) {
            dto.setErrorDesc("门店数据不全");
            return dto;
        }
        dto.setRowNum(input.getBatchCreateRowNum());
        MerchantStoreCommandInput merchantStore = input.getMerchantStore();
        dto.setStoreName(merchantStore.getStoreName());
        dto.setTenantId(merchantStore.getTenantId());
        Long storeId;
        try {
            input.setBatchCreateFlag(true);
            storeId = createMerchantStoreInfo(input);
            dto.setStoreId(storeId);
        } catch (ParamsException | BizException e) {
            dto.setErrorDesc(e.getMessage());
        } catch (Exception e) {
            log.warn("批量导入出错，rowNum：{}!", input.getBatchCreateRowNum(), e);
            dto.setErrorDesc("数据异常");
        }
        return dto;
    }

}
