package net.xianmu.usercenter.application.serviceImpl.businessInfo.converter;

import net.xianmu.usercenter.api.businessInfo.dto.BusinessInformationDTO;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 17:01:58
 * @version 1.0
 *
 */
public class BusinessInformationConverter {


    private BusinessInformationConverter() {
        // 无需实现
    }


    public static List<BusinessInformationDTO> toBusinessInformationDTOList(List<BusinessInformationEntity> businessInformationEntityList) {
        if (businessInformationEntityList == null) {
            return Collections.emptyList();
        }
        List<BusinessInformationDTO> businessInformationDTOList = new ArrayList<>();
        for (BusinessInformationEntity businessInformationEntity : businessInformationEntityList) {
            businessInformationDTOList.add(toBusinessInformationDTO(businessInformationEntity));
        }
        return businessInformationDTOList;
    }

    public static BusinessInformationDTO toBusinessInformationDTO(BusinessInformationEntity businessInformationEntity) {
        if (businessInformationEntity == null) {
            return null;
        }
        BusinessInformationDTO businessInformationDTO = new BusinessInformationDTO();
        businessInformationDTO.setId(businessInformationEntity.getId());
        businessInformationDTO.setTenantId(businessInformationEntity.getTenantId());
        businessInformationDTO.setBizId(businessInformationEntity.getBizId());
        businessInformationDTO.setType(businessInformationEntity.getType());
        businessInformationDTO.setCompanyName(businessInformationEntity.getCompanyName());
        businessInformationDTO.setCreditCode(businessInformationEntity.getCreditCode());
        businessInformationDTO.setProvince(businessInformationEntity.getProvince());
        businessInformationDTO.setCity(businessInformationEntity.getCity());
        businessInformationDTO.setArea(businessInformationEntity.getArea());
        businessInformationDTO.setAddress(businessInformationEntity.getAddress());
        businessInformationDTO.setPhone(businessInformationEntity.getPhone());
        businessInformationDTO.setBusinessLicense(businessInformationEntity.getBusinessLicense());
        businessInformationDTO.setCreateTime(businessInformationEntity.getCreateTime());
        businessInformationDTO.setUpdateTime(businessInformationEntity.getUpdateTime());
        businessInformationDTO.setContactName(businessInformationEntity.getContactName());
        return businessInformationDTO;
    }
}
