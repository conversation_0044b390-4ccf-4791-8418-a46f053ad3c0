package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.usercenter.api.merchant.dto.MerchantStorePropertiesExtDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStorePropertiesExtQueryService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStorePropertiesExtRepository;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStorePropertiesExtConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantStorePropertiesExtQueryServiceImpl implements MerchantStorePropertiesExtQueryService {
    @Autowired
    MerchantStorePropertiesExtRepository merchantStorePropertiesExtRepository;
    @Override
    public List<MerchantStorePropertiesExtDTO> queryByMidsKey(List<Long> mIds, String proKey) {
        List<MerchantStorePropertiesExtEntity> merchantStorePropertiesExtEntities = merchantStorePropertiesExtRepository.selectByMIds(mIds, proKey);
        return merchantStorePropertiesExtEntities.stream().map(MerchantStorePropertiesExtConverter::convertMerchantStoreExtDto).collect(Collectors.toList());
    }
}
