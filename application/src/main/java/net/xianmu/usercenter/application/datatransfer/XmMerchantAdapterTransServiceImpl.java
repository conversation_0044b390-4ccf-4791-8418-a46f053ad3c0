package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.domain.merchant.domain.XmMerchantAdapterDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/8/30 16:18
 */

@Service
@Slf4j
public class XmMerchantAdapterTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private DataTransferRepository dataTransferRepository;

    @Autowired
    private XmMerchantAdapterDomainService xmMerchantAdapterDomainService;

    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {

    }

    @Override
    public List<Map<String, Object>> selectDataList(Long mId, Integer offset) {
        return dataTransferRepository.selectMerchantList(mId, offset);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doTransfer(Map<String, Object> data, boolean isCreate) {
        if (CollUtil.isEmpty(data)) {
            return;
        }

        Object mIdObject = data.get("m_id");
        Long mId = Long.valueOf(mIdObject.toString());
        Map<String, String> stringMap = MapUtil.convertToStringMap(data);
        if(notInit(stringMap)) {
            log.info("当前门店m_Id:{}暂无合作模式!", mId);
            return;
        }
        MerchantStoreEntity entity = merchantStoreQueryRepository.selectByMId(mId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
        if(null == entity) {
            log.warn("当前门店m_Id:{}暂未同步!", mId);
            return;
        }
        xmMerchantAdapterDomainService.createOrUpdateForBinLog(stringMap, entity);
    }

    /**
     * 判断是否初始化（这里是为了避免null数据初始化上去有性能影响）
     * @return
     */
    private boolean notInit(Map<String, String> stringMap){
        return null == stringMap.get("direct");
    }
}
