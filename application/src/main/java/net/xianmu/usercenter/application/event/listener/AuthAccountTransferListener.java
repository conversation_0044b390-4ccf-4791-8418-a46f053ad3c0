package net.xianmu.usercenter.application.event.listener;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.login.UserStatusEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountToAuthServiceImpl;
import net.xianmu.usercenter.application.event.AuthAccountTransferEvent;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.converter.FastJsonConverter;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.util.DateUtil;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAccountMappingEntity;
import net.xianmu.usercenter.facade.auth.AuthUserFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/11/23 16:24
 */

@Component
@Slf4j
public class AuthAccountTransferListener implements ApplicationListener<AuthAccountTransferEvent> {

    @Autowired
    private MerchantStoreAccountToAuthServiceImpl merchantStoreAccountToAuthService;


    @Override
    public void onApplicationEvent(AuthAccountTransferEvent event) {
        log.info("AuthAccountTransferEvent start. event:{}", FastJsonConverter.convert(event));
        Map<String, String> dataMap = event.getDataMap();
        Map<String, String> oldMap = event.getOldMap();
        String type = event.getType();
        try {
            if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
                merchantStoreAccountToAuthService.transferXmStockData(oldMap, true);
            } else if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
                merchantStoreAccountToAuthService.transferXmStockData(dataMap, false);
            } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
                // 如果是逻辑删除
                merchantStoreAccountToAuthService.transferXmStockData(dataMap, getDeleteFlag(dataMap));
            } else {
                log.warn("未知操作!");
            }
        } catch (Exception e) {
            log.error("鲜沐账户数据同步到auth出错!!", e);
        }
        log.info("AuthAccountTransferEvent end.");
    }

    /**
     * 判断当前数据是否逻辑删除
     *
     * @param dataMap
     * @return
     */
    private boolean getDeleteFlag(Map<String, String> dataMap) {
        Integer flag = Integer.valueOf(dataMap.get("delete_flag"));
        return MerchantAccountEnums.DeleteFlag.DELETED.getCode().equals(flag);
    }
}
