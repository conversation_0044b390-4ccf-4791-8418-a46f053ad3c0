package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.api.merchant.service.MerchantStorePropertiesExtCommandService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStorePropertiesExtRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static net.xianmu.usercenter.common.constants.TenantDefaultConstant.XIAN_MU_TENANT_ID;

@Service
public class MerchantStorePropertiesExtCommandServiceImpl implements MerchantStorePropertiesExtCommandService {
    @Autowired
    MerchantStorePropertiesExtRepository merchantStorePropertiesExtRepository;
    @Autowired
    MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Override
    public Boolean addOrUpdateExt(Long mId, String proKey, String value) {
        MerchantStorePropertiesExtEntity merchantStorePropertiesExtEntity = new MerchantStorePropertiesExtEntity();
        merchantStorePropertiesExtEntity.setMId(mId);
        merchantStorePropertiesExtEntity.setProKey(proKey);
        merchantStorePropertiesExtEntity.setProValue(value);
        merchantStorePropertiesExtEntity.setCreateTime(LocalDateTime.now());
        merchantStorePropertiesExtEntity.setUpdateTime(LocalDateTime.now());
        MerchantStoreEntity merchantStoreEntity = merchantStoreQueryRepository.selectByMId(mId, XIAN_MU_TENANT_ID);
        if (merchantStoreEntity != null){
            merchantStorePropertiesExtEntity.setStoreId(merchantStoreEntity.getId());
        }
        return merchantStorePropertiesExtRepository.createOrUpdate(merchantStorePropertiesExtEntity);
    }
}
