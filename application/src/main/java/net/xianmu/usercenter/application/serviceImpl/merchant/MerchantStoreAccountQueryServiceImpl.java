package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreAccountDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreAccountQueryService;
import net.xianmu.usercenter.application.serviceImpl.merchant.converter.MerchantStoreAccountConverter;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreAccountDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:14
 */
@Service
@Slf4j
public class MerchantStoreAccountQueryServiceImpl implements MerchantStoreAccountQueryService {

    @Autowired
    private MerchantStoreAccountDomainService accountDomainService;

    @Autowired
    private MerchantStoreAccountQueryRepository merchantStoreAccountQueryRepository;


    @Override
    public MerchantStoreAccountEntity getMerchantStoreAccountById(Long id) {
        if(null == id) {
            return null;
        }

        List<MerchantStoreAccountEntity> entity = accountDomainService.getMerchantStoreAccounts(MerchantStoreAccountQueryInput.builder().id(id).build());
        return CollUtil.isEmpty(entity) ? null : entity.get(0);
    }

    @Override
    public List<MerchantStoreAccountEntity> getMerchantStoreAccountsByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return accountDomainService.getMerchantStoreAccounts(MerchantStoreAccountQueryInput.builder().idList(idList).build());
    }

    @Override
    public List<MerchantStoreAccountEntity> getMerchantStoreAccounts(MerchantStoreAccountQueryInput req) {
        return accountDomainService.getMerchantStoreAccounts(req);
    }

    @Override
    public List<MerchantStoreAccountEntity> getMerchantStoreAccountsWithFuzzy(MerchantStoreAccountQueryInput req) {
        return merchantStoreAccountQueryRepository.selectByConditionWithFuzzy(req);
    }

    @Override
    public PageInfo<MerchantStoreEntity> getAccountPage(MerchantStoreAccountQueryInput req, PageQueryInput page) {
        ValidateUtil.paramValidate(req, "tenantId");
        return accountDomainService.getAccountPageWithAuth(req, page);
    }
}
