package net.xianmu.usercenter.application.serviceImpl.tenant;

import net.xianmu.usercenter.api.tenant.service.TenantAccountCommandService;
import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;
import net.xianmu.usercenter.domain.tenant.domain.TenantAccountDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 11:23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TenantAccountCommandServiceImpl implements TenantAccountCommandService {
    @Autowired
    private TenantAccountDomainService tenantAccountDomainService;

    @Override
    public Long create(TenantAccountCommandInput req) {
        return tenantAccountDomainService.create(req);
    }

    @Override
    public void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccountCommandInput req) {
        tenantAccountDomainService.updateByTenantIdsAndPhone(tenantIds, req);
    }

    @Override
    public void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountCommandInput req) {
        tenantAccountDomainService.updateByTenantIdsAndAuthId(tenantIds, req);
    }

    @Override
    public void remove(TenantAccountCommandInput req) {
        tenantAccountDomainService.remove(req);
    }

    @Override
    public void updatePassword(TenantAccountCommandInput req) {
        tenantAccountDomainService.updatePassword(req);
    }
}
