package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.usercenter.api.merchant.service.MerchantAddressCommandService;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;
import net.xianmu.usercenter.domain.merchant.domain.MerchantAddressDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2023-05-29 13:50:22
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantAddressCommandServiceImpl implements MerchantAddressCommandService {

    @Autowired
    private MerchantAddressDomainService merchantAddressDomainService;

    @Override
    public Long create(MerchantAddressCommandInput input) {
        return merchantAddressDomainService.create(input);
    }

    @Override
    public Boolean update(MerchantAddressCommandInput input) {
        return merchantAddressDomainService.update(input);
    }

    @Override
    public Boolean remove(MerchantAddressCommandInput input) {
        return merchantAddressDomainService.remove(input);
    }
}