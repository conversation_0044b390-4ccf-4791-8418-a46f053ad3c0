package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantContactQueryService;
import net.xianmu.usercenter.application.serviceImpl.merchant.converter.MerchantContactConverter;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@Service
public class MerchantContactQueryServiceImpl implements MerchantContactQueryService {

    @Autowired
    private MerchantContactQueryRepository merchantContactQueryRepository;

    @Override
    public List<MerchantContactDTO> getMerchantContacts(MerchantContactQueryInput req) {
        return MerchantContactConverter.toMerchantContactDTOList(merchantContactQueryRepository.selectByCondition(req));
    }

    @Override
    public List<MerchantContactDTO> getMerchantContactsByStoreId(Long tenantId, Long storeId) {
        return MerchantContactConverter.toMerchantContactDTOList(merchantContactQueryRepository.selectByStoreId(tenantId, storeId));
    }

}