package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreQueryService;
import net.xianmu.usercenter.application.serviceImpl.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.common.config.NacosPropertiesHolder;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.enums.MerchantStoreEnums;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:14
 */
@Service
@Slf4j
public class MerchantStoreQueryServiceImpl implements MerchantStoreQueryService {

    @Autowired
    private MerchantStoreDomainService merchantStoreDomainService;
    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Autowired
    private MerchantStoreAccountQueryRepository accountQueryRepository;
    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;


    @Override
    public MerchantStoreEntity getMerchantStoreById(Long id) {
        if (null == id) {
            return null;
        }
        return merchantStoreDomainService.getMerchantStoreById(id);
    }

    @Override
    public List<MerchantStoreEntity> getMerchantStoresByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return merchantStoreDomainService.getMerchantStoresByIds(idList);
    }

    @Override
    public List<MerchantStoreEntity> getMerchantStores(MerchantStoreQueryInput req) {
        return merchantStoreDomainService.getMerchantStores(req);
    }

    @Override
    public PageInfo<MerchantStoreDTO> getMerchantStoreAndAddressPage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput) {
        return PageInfoConverter.toPageResp(merchantStoreQueryRepository.selectMerchantStoreAndAddressPage(input, pageQueryInput), MerchantStoreConverter::toMerchantStoreDTO);
    }

    @Override
    public PageInfo<MerchantStoreDTO> selectMerchantStorePage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput) {
        return PageInfoConverter.toPageResp(merchantStoreQueryRepository.selectMerchantStorePage(input, pageQueryInput), MerchantStoreConverter::toMerchantStoreDTO);
    }

    @Override
    public List<MerchantStoreEntity> getMerchantStoreAndExtends(MerchantStoreQueryInput req) {
        int pageIndex = req.getPageIndex() == null ? 1 : req.getPageIndex();
        int pageSize = req.getPageSize() == null ? nacosPropertiesHolder.getDefaultQuerySize() : req.getPageSize();
        pageSize = pageSize > nacosPropertiesHolder.getMaxQuerySize() ? nacosPropertiesHolder.getMaxQuerySize() : pageSize;
        PageHelper.startPage(pageIndex, pageSize, false);
        List<MerchantStoreEntity> entities = merchantStoreQueryRepository.selectMerchantStoreAndExtends(req);

        // 拼接主账户信息
        this.wrapManegeAccount(entities, req.isQueryManageAccount());

        return entities;
    }


    private void wrapManegeAccount(List<MerchantStoreEntity> entities, boolean queryManageAccount) {
        if (CollUtil.isEmpty(entities) || !queryManageAccount) {
            return;
        }

        try {

            // 借用租户id走索引，这里一般只有一个租户
            Map<Long, List<MerchantStoreEntity>> map = entities.stream().collect(Collectors.groupingBy(MerchantStoreEntity::getTenantId));
            map.forEach((k, v) -> {
                List<Long> storeIds = v.stream().map(MerchantStoreEntity::getId).collect(Collectors.toList());
                List<MerchantStoreAccountEntity> accountEntities = accountQueryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(k).storeIdList(storeIds).type(MerchantAccountEnums.Type.MANAGER.getCode()).build());

                MerchantStoreEntity.wrapManegeAccount(entities, accountEntities);
            });

        } catch (Exception e) {
            List<Long> collect = entities.stream().map(MerchantStoreEntity::getId).collect(Collectors.toList());
            log.error("门店账户信息异常! storeId: {}", JSON.toJSONString(collect), e);
        }

    }

    @Override
    public List<Long> selectMIdListByCondition(MerchantStoreQueryInput req) {
        return merchantStoreQueryRepository.selectMIdListByCondition(req);
    }

    @Override
    public PageInfo<MerchantStoreEntity> selectMerchantStorePageForXM(MerchantStorePageQueryInput input) {
        ValidateUtil.paramValidate(input);

        boolean singleQuery = false;
        // 连表转单表
        /*String phone = input.getPhone();
        if (StrUtil.isNotBlank(phone)) {
            List<MerchantStoreAccountEntity> entities = accountQueryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().phonePrefix(phone).tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID).build());
            if (CollUtil.isEmpty(entities)) {
                return PageInfo.of(Collections.emptyList());
            }
            List<Long> collect = entities.stream().map(MerchantStoreAccountEntity::getStoreId).collect(Collectors.toList());
            input.retainStoreIdList(collect);
            singleQuery = true;
        }*/

        String storeName = input.getStoreName();
        if (StrUtil.isNotBlank(storeName)) {
            input.setXmPageDefaultQuerySize(nacosPropertiesHolder.getXmPageDefaultQuerySize());
            List<Long> storeId = merchantStoreQueryRepository.selectStoreIdForXmPage(input);
            if (CollUtil.isEmpty(storeId)) {
                return PageInfo.of(Collections.emptyList());
            }
            input.retainStoreIdList(storeId);
            singleQuery = true;
        }

        // 单表查询未查询到id，代表没有数据直接返回
        if(singleQuery && CollUtil.isEmpty(input.getStoreIds())) {
            return PageInfo.emptyPageInfo();
        }

        // 查询降级
        this.warpQueryParam(input);

        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        return PageInfo.of(merchantStoreQueryRepository.selectMerchantStorePageForXM(input));
    }


    /**
     * 如果请求参数只传了：tenantId,areaNos。那么默认只查最近三个月的门店数据
     * @param input
     */
    private void warpQueryParam(MerchantStorePageQueryInput input) {
        // 查询非正常的门店时，数据量不大，无需处理
        Integer status = input.getStatus();
        if(status != null
                && !MerchantStoreEnums.PageQueryStatus.AUDIT_SUCCESS.getCode().equals(status)
                && !MerchantStoreEnums.PageQueryStatus.PENDING_VERIFICATION.getCode().equals(status)
                && !MerchantStoreEnums.PageQueryStatus.PENDING_SUBMISSION.getCode().equals(status)) {
            return;
        }

        Map map = JSONObject.parseObject(JSON.toJSONString(input), Map.class);
        map.remove("tenantId");
        map.remove("status");
        map.remove("areaNos");
        map.remove("pageIndex");
        map.remove("pageSize");
        map.remove("sortList");

        // 如果只有上面的三个参数,那么降级为查询最近三个月的门店
        boolean notExist = CollUtil.isEmpty(map);
        if (notExist) {
            input.setStartTime(net.xianmu.usercenter.common.util.DateUtil.toLocalDateTime(DateUtil.offsetMonth(new Date(), -3)));
        }
    }

    public static void main(String[] args) {
        System.out.println(net.xianmu.usercenter.common.util.DateUtil.toLocalDateTime(DateUtil.offsetMonth(new Date(), -3)));
    }

    private List<Long> buildStoreIdParamByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return null;
        }
        List<MerchantStoreAccountEntity> entities = accountQueryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().phonePrefix(phone).tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID).build());
        if (CollUtil.isEmpty(entities)) {
            return null;
        }
        return entities.stream().map(MerchantStoreAccountEntity::getStoreId).collect(Collectors.toList());
    }
}
