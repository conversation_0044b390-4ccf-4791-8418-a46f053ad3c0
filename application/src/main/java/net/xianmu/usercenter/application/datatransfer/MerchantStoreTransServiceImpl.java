package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.constants.MerchantDefaultConstant;
import net.xianmu.usercenter.common.constants.RedisConstant;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.BusinessInformationEnum;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.enums.MerchantStoreChangeLogEnums;
import net.xianmu.usercenter.common.enums.MerchantStoreEnums;
import net.xianmu.usercenter.common.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.util.DateUtil;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.common.util.RedisLockUtil;
import net.xianmu.usercenter.common.util.SpringContextHolder;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.businessInfo.domain.BusinessInformationDomainService;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreChangeLogDomainService;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreDomainService;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreExtDomainService;
import net.xianmu.usercenter.domain.merchant.domain.XmMerchantAdapterDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreChangeLogRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.regional.domain.RegionalOrganizationDomainService;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 10:55
 */

@Service
@Slf4j
public class MerchantStoreTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private MerchantStoreDomainService merchantStoreDomainService;
    @Autowired
    private XmMerchantAdapterDomainService xmMerchantAdapterDomainService;
    @Autowired
    private MerchantStoreExtDomainService merchantStoreExtDomainService;
    @Autowired
    private MerchantStoreChangeLogDomainService merchantStoreChangeLogDomainService;
    @Autowired
    private BusinessInformationDomainService businessInformationDomainService;
    @Autowired
    private RegionalOrganizationDomainService regionalOrganizationDomainService;


    @Autowired
    private MerchantStoreChangeLogRepository merchantStoreChangeLogRepository;
    @Autowired
    private MerchantStoreQueryRepository merchantQueryRepository;
    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;
    @Autowired
    private DataTransferRepository dataTransferRepository;
    @Autowired
    private MerchantStoreAccountQueryRepository merchantStoreAccountQueryRepository;
    @Autowired
    private MerchantStoreAccountCommandRepository merchantStoreAccountCommandRepository;
    @Autowired
    private MerchantAddressQueryRepository merchantAddressQueryRepository;
    @Autowired
    private MerchantAddressCommandRepository merchantAddressCommandRepository;


    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        String mIdStr;
        MerchantStoreTransServiceImpl merchantStoreTransService = SpringContextHolder.getBean(MerchantStoreTransServiceImpl.class);
        // 删除
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.warn("门店表binlog物理删除！");
            mIdStr = old.get("m_id");
            MerchantStoreEntity entity = merchantQueryRepository.selectByMId(Long.valueOf(mIdStr), TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null != entity) {
                log.info("待删除数据，storeId:{}", entity.getId());
                merchantStoreTransService.deleteForBinLog(entity.getId());
            }
            return;
        }


        mIdStr = data.get("m_id");
        Long mId = Long.valueOf(mIdStr);
        // 处理核心业务逻辑
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            merchantStoreTransService.createMerchantForBinLog(data, mId);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            // 先查映射，如果没有，那就走新建流程
            MerchantStoreEntity entity = merchantQueryRepository.selectByMId(mId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null == entity) {
                merchantStoreTransService.createMerchantForBinLog(data, mId);
                return;
            }
            // 更新操作
            merchantStoreTransService.updateMerchantForBinLog(data, old, entity);
        } else {
            // 删除操作
            log.error("binlog物理删除！");
        }
    }


    public void createMerchantForBinLog(Map<String, String> data, Long mId) {
        // 0.幂等
        MerchantStoreEntity entity = merchantQueryRepository.selectByMId(mId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
        if (null != entity) {
            log.info("当前门店已存在，无需重复创建! mId:{}", mId);
            return;
        }

        String lockKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_BINLOG + mId;
        try {
            boolean success = RedisLockUtil.tryLock(lockKey, 2, 30);
            if (!success) {
                log.warn("门店mid:{}获取锁失败，自动开始重试。", mId);
                throw new BizException("当前门店地址、账户数据正在同步");
            }
            log.info("加锁成功，开始初始化门店数据, key: {}, time: {}", lockKey, System.currentTimeMillis());

            MerchantStoreTransServiceImpl merchantStoreTransService = SpringContextHolder.getBean(MerchantStoreTransServiceImpl.class);
            merchantStoreTransService.createMerchantForBinLogInner(data, mId);
        } catch (Exception e) {
            throw e;
        } finally {
            RedisLockUtil.unlockIfHeldByCurrentThread(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createMerchantForBinLogInner(Map<String, String> data, Long mId) {

        // 1.先看区域组织
        RegionalOrganizationEntity regional = this.getRegionalRegionalOrganization(data);
        if (regional == null) {
            // 根据admin_id查不到对应的区域时，暂时不处理，等到存量数据同步后就正常了
            log.info("当前门店所属的大客户信息暂未同步：mId：{}", mId);
            return;
        }

        // 1.保存门店信息
        MerchantStoreCommandInput input = this.convertToStore(data, regional.getTenantId(), regional.getId());
        MerchantStoreEntity merchantStoreEntity = merchantStoreDomainService.createMerchantForBinLog(input, mId);

        // 2.变更记录
        MerchantStoreChangeLogEntity changeLogEntity = MerchantStoreChangeLogEntity.builder()
                .tenantId(merchantStoreEntity.getTenantId())
                .regionalId(merchantStoreEntity.getRegionalId())
                .storeId(merchantStoreEntity.getId())
                .opType(MerchantStoreChangeLogEnums.OpType.CREATE.getCode())
                .inviterChannelCode(data.get("invitecode"))
                .merchantChannelCode(data.get("inviter_channel_code"))
                .createTime(LocalDateTime.now())
                .build();
        merchantStoreChangeLogDomainService.createOrUpdate(changeLogEntity);

        // 3.如果是拉黑状态，初始化一条拉黑的记录
        String status = data.get("islock");
        if (status.equals(MerchantStoreEnums.XmStatus.PULL_BLACK.getCode().toString())) {
            MerchantStoreChangeLogEntity pullBlackEntity = MerchantStoreChangeLogEntity.builder()
                    .tenantId(merchantStoreEntity.getTenantId())
                    .regionalId(merchantStoreEntity.getRegionalId())
                    .storeId(merchantStoreEntity.getId())
                    .opType(MerchantStoreChangeLogEnums.OpType.PULL_BLACK.getCode())
                    .opName(data.get("pull_black_operator"))
                    .opRemark(data.get("pull_black_remark"))
                    .build();
            merchantStoreChangeLogDomainService.createOrUpdate(pullBlackEntity);
        }

        // 4.保存工商信息
        BusinessInformationEntity informationEntity = this.convertToBusinessInfo(data);
        if (informationEntity != null) {
            informationEntity.setType(BusinessInformationEnum.TypeEnum.MERCHANT_STORE.getCode());
            informationEntity.setBizId(merchantStoreEntity.getId());
            informationEntity.setTenantId(merchantStoreEntity.getTenantId());
            businessInformationDomainService.createOrUpdateForBinLog(informationEntity);
        }

        // 5.处理账户、地址信息
        this.handleAccountAndAddress(merchantStoreEntity);
    }

    private void handleAccountAndAddress(MerchantStoreEntity merchantStoreEntity) {
        // 账户
        List<MerchantStoreAccountEntity> accountEntities = merchantStoreAccountQueryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID).mId(merchantStoreEntity.getMId()).build());
        if (CollUtil.isNotEmpty(accountEntities)) {
            List<MerchantStoreAccountEntity> updateAccountList = new ArrayList<>();
            for (MerchantStoreAccountEntity accountEntity : accountEntities) {
                if (accountEntity.getStoreId() == null) {
                    accountEntity.setStoreId(merchantStoreEntity.getId());
                    updateAccountList.add(accountEntity);
                }
            }
            merchantStoreAccountCommandRepository.updateSelectiveBatch(updateAccountList);
        }


        // 地址
        List<MerchantAddressEntity> addressEntities = merchantAddressQueryRepository.selectByCondition(MerchantAddressQueryInput.builder().tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID).mId(merchantStoreEntity.getMId()).build());
        if (CollUtil.isNotEmpty(addressEntities)) {
            List<MerchantAddressEntity> updateAddressList = new ArrayList<>();
            for (MerchantAddressEntity addressEntity : addressEntities) {
                if (addressEntity.getStoreId() == null) {
                    addressEntity.setStoreId(merchantStoreEntity.getId());
                    updateAddressList.add(addressEntity);
                }
            }
            merchantAddressCommandRepository.updateSelectiveBatch(updateAddressList);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMerchantForBinLog(Map<String, String> data, Map<String, String> old, MerchantStoreEntity entity) {
        // 变更了用户信息？
        if (ValidateUtil.isContains(old, "admin_id", "mname", "enterprise_scale", "register_time", "islock", "remark", "audit_time", "type", "channel_code", "area_no")) {
            merchantStoreDomainService.updateMerchantForBinLog(data, old, entity.getId());
        }

        // 变更拓展表信息？
        if (ValidateUtil.isContains(old, "pop_view", "change_pop", "first_login_pop", "display_button", "province", "city", "area", "role_id", "pre_register_flag", "poi_note", "operate_status", "business_line")) {
            merchantStoreExtDomainService.createOrUpdateForBinLog(data, entity);
        }

        // 变更业务适配表信息？
        if (ValidateUtil.isContains(old, "direct")) {
            xmMerchantAdapterDomainService.createOrUpdateForBinLog(data, entity);
        }

        // 取消客户来源？
        if (ValidateUtil.isContains(old, "invitecode", "inviter_channel_code")) {
            MerchantStoreChangeLogEntity createLog = merchantStoreChangeLogRepository.selectByStoreId(entity.getId(), MerchantStoreChangeLogEnums.OpType.CREATE.getCode());
            if (createLog != null) {
                merchantStoreChangeLogDomainService.updateChannelCode(createLog.getId(), data.get("invitecode"), data.get("inviter_channel_code"));
            }
        }

        // 拉黑？
        if (isPullBlack(data, old)) {
            MerchantStoreEntity merchantStoreEntity = merchantQueryRepository.selectById(entity.getId());
            MerchantStoreChangeLogEntity changeLogEntity = MerchantStoreChangeLogEntity.builder()
                    .tenantId(merchantStoreEntity.getTenantId())
                    .regionalId(merchantStoreEntity.getRegionalId())
                    .storeId(merchantStoreEntity.getId())
                    .opType(MerchantStoreChangeLogEnums.OpType.PULL_BLACK.getCode())
                    .opName(data.get("pull_black_operator"))
                    .opRemark(data.get("pull_black_remark"))
                    .build();
            merchantStoreChangeLogDomainService.createOrUpdate(changeLogEntity);
        }

        // 变更了工商信息？
        if (ValidateUtil.isContains(old, "business_license_address")) {
            // 先查工商信息
            BusinessInformationEntity informationCommandInput = this.convertToBusinessInfo(data);
            if (null != informationCommandInput) {
                informationCommandInput.setBizId(entity.getId());
                informationCommandInput.setType(BusinessInformationEnum.TypeEnum.MERCHANT_STORE.getCode());
                businessInformationDomainService.createOrUpdateForBinLog(informationCommandInput);
            }
        }
    }


    /**
     * 是否为拉黑操作
     *
     * @return
     */
    private boolean isPullBlack(Map<String, String> data, Map<String, String> old) {
        String newStatus = data.get("islock");
        String oldStatus = old.get("islock");
        if (StrUtil.isBlank(oldStatus)) {
            return false;
        }
        return !oldStatus.equals(newStatus) && newStatus.equals(MerchantStoreEnums.XmStatus.PULL_BLACK.getCode().toString());
    }


    /**
     * 获取门店所属的区域组织
     *
     * @return
     */
    private RegionalOrganizationEntity getRegionalRegionalOrganization(Map<String, String> data) {
        String adminId = data.get("admin_id");
        RegionalOrganizationEntity entity;
        if (StrUtil.isBlank(adminId)) {
            // adminId为空则代表为单店
            // 初始化一个单店类型的区域组织
            RegionalOrganizationEntity input = converterToRegionalOrganizationEntity(data, TenantDefaultConstant.XIAN_MU_TENANT_ID);
            entity = regionalOrganizationDomainService.createForBinLog(input);
        } else {
            // 如果为大客户，那么直接查adminId对应的区域组织
            entity = regionalOrganizationQueryRepository.selectByAdminAndTenant(Long.valueOf(adminId), TenantDefaultConstant.XIAN_MU_TENANT_ID);
        }
        return entity;
    }

    private RegionalOrganizationEntity converterToRegionalOrganizationEntity(Map<String, String> data, Long tenantId) {
        return RegionalOrganizationEntity.builder()
                .phone(data.get("phone"))
                .organizationName(data.get("mname"))
                .source(RegionalOrganizationEnums.Source.XIANMU.getCode())
                .size(RegionalOrganizationEnums.Size.MERCHANT.getCode())
                .status(RegionalOrganizationEnums.Status.NORMAL.getCode())
                .tenantId(tenantId)
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), null))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), null))
                .build();
    }


    private MerchantStoreCommandInput convertToStore(Map<String, String> map, Long tenantId, Long regionalId) {
        Integer businessLine = map.get("business_line") == null ? null : Integer.valueOf(map.get("business_line"));
        return MerchantStoreCommandInput.builder()
                .tenantId(tenantId)
                .regionalId(regionalId)
                .storeName(map.get("mname"))
                .type(MerchantStoreEntity.transTypeFromXm(map.get("enterprise_scale")))
                .status(MerchantStoreEntity.transStatusFromXm(map.get("islock")))
                .registerTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("register_time"), null))
                .auditRemark(map.get("remark"))
                .auditTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("audit_time"), null))
                .remark(null)
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("register_time"), null))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("update_time"), null))
                .billSwitch(MerchantDefaultConstant.DEFAULT_BILL_SWITCH)
                .onlinePayment(MerchantDefaultConstant.DEFAULT_ONLINE_PAYMENT)
                .storeNo(null)
                .balanceAuthority(MerchantDefaultConstant.DEFAULT_BALANCE_AUTHORITY)
                .mId(Long.valueOf(map.get("m_id")))
                .businessType(map.get("type"))
                .channelCode(map.get("channel_code"))
                .popView(Integer.valueOf(map.get("pop_view")))
                .changePop(Integer.valueOf(map.get("change_pop")))
                .firstLoginPop(Integer.valueOf(map.get("first_login_pop")))
                .displayButton(Integer.valueOf(map.get("display_button")))
                .preRegisterFlag(MerchantStoreExtEntity.converterPreRegisterFlag(map.get("pre_register_flag")))
                .areaNo(Integer.valueOf(map.get("area_no")))
                .province(map.get("province"))
                .city(map.get("city"))
                .area(map.get("area"))
                .mockLoginFlag(MerchantStoreExtEntity.converterMockLoginFlag(map.get("role_id")))
                .poiNote(map.get("poi_note"))
                .direct(MapUtil.getInteger(map, "direct"))
                .operateStatus(Integer.valueOf(map.get("operate_status")))
                .businessLine(businessLine)
                .build();
    }


    private BusinessInformationEntity convertToBusinessInfo(Map<String, String> map) {
        String businessLicense = map.get("business_license");
        if (StrUtil.isEmpty(businessLicense)) {
            return null;
        }
        return BusinessInformationEntity.builder()
                .companyName(map.get("mname"))
                .phone(map.get("phone"))
                .businessLicense(map.get("business_license"))
                .createTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("create_time"), null))
                .updateTime(DateUtil.getTimeWithBinLogPatternDefault(map.get("update_time"), null))
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteForBinLog(Long id) {
        merchantStoreDomainService.deleteForBinLog(id);
    }


    @Override
    public List<Map<String, Object>> selectDataList(Long mId, Integer offset) {
        return dataTransferRepository.selectMerchantList(mId, offset);
    }

    @Override
    public void doTransfer(Map<String, Object> data, boolean isCreate) {
        if (CollUtil.isEmpty(data)) {
            return;
        }
        MerchantStoreTransServiceImpl merchantStoreTransService = SpringContextHolder.getBean(MerchantStoreTransServiceImpl.class);
        Object mIdObject = data.get("m_id");
        Long mId = Long.valueOf(mIdObject.toString());
        Map<String, String> stringMap = MapUtil.convertToStringMap(data);

        if (isCreate) {
            merchantStoreTransService.createMerchantForBinLog(stringMap, mId);
        } else {
            // 先查映射，如果没有，那就走新建流程
            MerchantStoreEntity entity = merchantQueryRepository.selectByMId(mId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null == entity) {
                merchantStoreTransService.createMerchantForBinLog(stringMap, mId);
            } else {
                merchantStoreTransService.updateMerchantForBinLog(stringMap, stringMap, entity);
            }
        }
    }


    public void refreshMerchantData(List<Long> mIdList) {
        List<Map<String, Object>> maps = dataTransferRepository.selectMerchantListByIdList(mIdList);
        if (CollUtil.isEmpty(maps)) {
            log.warn("门店不存在！");
            return;
        }
        MerchantStoreTransServiceImpl bean = SpringContextHolder.getBean(MerchantStoreTransServiceImpl.class);
        maps.forEach(map -> bean.doTransfer(map, false));
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteMerchantData(List<Long> mIdList) {
        if (CollUtil.isEmpty(mIdList)) {
            return;
        }
        for (Long mId : mIdList) {
            MerchantStoreEntity entity = merchantQueryRepository.selectByMId(mId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null != entity) {
                log.info("待删除数据，mId:{}, storeId:{}", mId, entity.getId());
                merchantStoreDomainService.deleteForBinLog(entity.getId());
            }
        }
    }


    /**
     * 重新同步拉黑数据
     */
    public void reTransferPullBlackData() {
        // 获取鲜沐是拉黑状态但是没有拉黑记录的数据
        List<MerchantStoreEntity> merchantStoreEntities = merchantQueryRepository.selectNoPullBlackRecordMerchant();
        if (CollUtil.isEmpty(merchantStoreEntities)) {
            log.info("无需要处理的门店数据");
            return;
        }

        // 根据m_id查询拉黑的人和备注
        List<Long> mIdList = merchantStoreEntities.stream().map(MerchantStoreEntity::getMId).collect(Collectors.toList());
        if (CollUtil.isEmpty(mIdList)) {
            log.info("无需要处理的门店数据");
            return;
        }
        List<Map<String, Object>> maps = dataTransferRepository.selectMerchantListByIdList(mIdList);
        Map<Object, Map<String, Object>> pullBlackMap = maps.stream().collect(Collectors.toMap(map -> map.get("id"), Function.identity()));

        // 生成拉黑记录
        MerchantStoreTransServiceImpl bean = SpringContextHolder.getBean(MerchantStoreTransServiceImpl.class);
        bean.generatePullBlackRecord(buildPullBlackRecordList(merchantStoreEntities, pullBlackMap));
    }

    @Transactional(rollbackFor = Exception.class)
    public void generatePullBlackRecord(List<MerchantStoreChangeLogEntity> entities) {
        merchantStoreChangeLogDomainService.createBatch(entities);
    }

    private List<MerchantStoreChangeLogEntity> buildPullBlackRecordList(List<MerchantStoreEntity> merchantStoreEntities, Map<Object, Map<String, Object>> pullBlackMap) {
        List<MerchantStoreChangeLogEntity> resultList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntities) {
            Map<String, Object> map = pullBlackMap.get(merchantStoreEntity.getMId());
            if (CollUtil.isEmpty(map)) {
                log.warn("该门店暂无拉黑数据，data:{}", JSON.toJSONString(merchantStoreEntity));
                continue;
            }
            String opName = map.get("pull_black_operator") == null ? null : map.get("pull_black_operator").toString();
            String opRemark = map.get("pull_black_remark") == null ? null : map.get("pull_black_remark").toString();

            MerchantStoreChangeLogEntity pullBlackEntity = MerchantStoreChangeLogEntity.builder()
                    .tenantId(merchantStoreEntity.getTenantId())
                    .regionalId(merchantStoreEntity.getRegionalId())
                    .storeId(merchantStoreEntity.getId())
                    .opType(MerchantStoreChangeLogEnums.OpType.PULL_BLACK.getCode())
                    .opName(opName)
                    .opRemark(opRemark)
                    .build();
            resultList.add(pullBlackEntity);
        }
        return resultList;
    }
}
