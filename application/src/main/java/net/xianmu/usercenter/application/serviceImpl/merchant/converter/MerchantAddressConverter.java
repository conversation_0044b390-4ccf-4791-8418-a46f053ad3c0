package net.xianmu.usercenter.application.serviceImpl.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public class MerchantAddressConverter {


    private MerchantAddressConverter() {
        // 无需实现
    }

    public static List<MerchantAddressEntity> toMerchantAddressEntityList(List<MerchantAddressDTO> merchantAddressDTOList) {
        if (merchantAddressDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressEntity> merchantAddressEntityList = new ArrayList<>();
        for (MerchantAddressDTO merchantAddressDTO : merchantAddressDTOList) {
            merchantAddressEntityList.add(toMerchantAddressEntity(merchantAddressDTO));
        }
        return merchantAddressEntityList;
    }

    public static MerchantAddressEntity toMerchantAddressEntity(MerchantAddressDTO merchantAddressDTO) {
        if (merchantAddressDTO == null) {
            return null;
        }
        MerchantAddressEntity merchantAddressEntity = new MerchantAddressEntity();
        merchantAddressEntity.setId(merchantAddressDTO.getId());
        merchantAddressEntity.setTenantId(merchantAddressDTO.getTenantId());
        merchantAddressEntity.setStoreId(merchantAddressDTO.getStoreId());
        merchantAddressEntity.setProvince(merchantAddressDTO.getProvince());
        merchantAddressEntity.setCity(merchantAddressDTO.getCity());
        merchantAddressEntity.setArea(merchantAddressDTO.getArea());
        merchantAddressEntity.setAddress(merchantAddressDTO.getAddress());
        merchantAddressEntity.setHouseNumber(merchantAddressDTO.getHouseNumber());
        merchantAddressEntity.setPoiNote(merchantAddressDTO.getPoiNote());
        merchantAddressEntity.setCreateTime(merchantAddressDTO.getCreateTime());
        merchantAddressEntity.setUpdateTime(merchantAddressDTO.getUpdateTime());
        merchantAddressEntity.setDefaultFlag(merchantAddressDTO.getDefaultFlag());
        merchantAddressEntity.setStatus(merchantAddressDTO.getStatus());
        return merchantAddressEntity;
    }

    public static List<MerchantAddressDTO> toMerchantAddressDTOList(List<MerchantAddressEntity> merchantAddressEntityList) {
        if (merchantAddressEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressDTO> merchantAddressDTOList = new ArrayList<>();
        for (MerchantAddressEntity merchantAddressEntity : merchantAddressEntityList) {
            merchantAddressDTOList.add(toMerchantAddressDTO(merchantAddressEntity));
        }
        return merchantAddressDTOList;
    }

    public static MerchantAddressDTO toMerchantAddressDTO(MerchantAddressEntity merchantAddressEntity) {
        if (merchantAddressEntity == null) {
            return null;
        }
        MerchantAddressDTO merchantAddressDTO = new MerchantAddressDTO();
        merchantAddressDTO.setId(merchantAddressEntity.getId());
        merchantAddressDTO.setTenantId(merchantAddressEntity.getTenantId());
        merchantAddressDTO.setStoreId(merchantAddressEntity.getStoreId());
        merchantAddressDTO.setProvince(merchantAddressEntity.getProvince());
        merchantAddressDTO.setCity(merchantAddressEntity.getCity());
        merchantAddressDTO.setArea(merchantAddressEntity.getArea());
        merchantAddressDTO.setAddress(merchantAddressEntity.getAddress());
        merchantAddressDTO.setHouseNumber(merchantAddressEntity.getHouseNumber());
        merchantAddressDTO.setPoiNote(merchantAddressEntity.getPoiNote());
        merchantAddressDTO.setCreateTime(merchantAddressEntity.getCreateTime());
        merchantAddressDTO.setUpdateTime(merchantAddressEntity.getUpdateTime());
        merchantAddressDTO.setDefaultFlag(merchantAddressEntity.getDefaultFlag());
        merchantAddressDTO.setStatus(merchantAddressEntity.getStatus());
        merchantAddressDTO.setDeliveryAddress(merchantAddressEntity.getDeliveryAddress());
        merchantAddressDTO.setContactName(merchantAddressEntity.getContactName());
        merchantAddressDTO.setContactPhone(merchantAddressEntity.getContactPhone());
        return merchantAddressDTO;
    }
}
