package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.enums.InvoiceConfigEnums;
import net.xianmu.usercenter.domain.invoice.entity.InvoiceConfigEntity;
import net.xianmu.usercenter.domain.invoice.repository.InvoiceConfigRepository;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 11:06
 */

//@Service
//@Transactional(rollbackFor = Exception.class)
@Slf4j
public class InvoiceConfigTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private InvoiceConfigRepository invoiceConfigRepository;
    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.error("binlog物理删除！");
            return;
        }

        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        Long id = Long.valueOf(data.get("id"));


        // 处理核心业务逻辑
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            this.createMerchantForBinLog(data);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            // 先查映射，如果没有，那就走新建流程
            InvoiceConfigEntity mapping = invoiceConfigRepository.selectById(id);
            if (null == mapping) {
                this.createMerchantForBinLog(data);
                return;
            }
            // 更新操作
            this.updateMerchantForBinLog(data, mapping);
        } else {
            // 删除操作
        }

    }


    @Override
    public List<Map<String, Object>> selectDataList(Long adminId, Integer offset) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doTransfer(Map<String, Object> data, boolean isCreate) {

    }

    private void updateMerchantForBinLog(Map<String, String> data, InvoiceConfigEntity entity) {
        // 更新地址
        InvoiceConfigEntity invoiceConfigEntity = InvoiceConfigEntity.converterToEntityForBinLog(data);
        invoiceConfigEntity.setId(entity.getId());
        invoiceConfigRepository.createOrUpdate(invoiceConfigEntity);

    }

    private void createMerchantForBinLog(Map<String, String> data) {
        // 1.幂等
        Long id = Long.valueOf(data.get("id"));
        InvoiceConfigEntity mapping = invoiceConfigRepository.selectById(id);
        if (null == mapping) {
            log.info("当前发票已存在，无需再次同步");
            return;
        }

        // 2.数据兼容、预处理
        Integer type = Integer.valueOf(data.get("type"));
        Long bizId ;
        if(InvoiceConfigEnums.TypeEnum.MERCHANT_STORE.getCode().equals(type)) {
            // 单店维度
            String mIdStr = data.get("merchant_id");
            if(StrUtil.isBlank(mIdStr)) {
                log.info("鲜沐脏数据，无需处理。invoiceId:{}, merchantId：{}", id, mIdStr);
                return;
            }
            // 判断门店是否存在，不存在就后面再处理
            MerchantStoreEntity merchantStoreEntity = merchantStoreQueryRepository.selectByMId(Long.valueOf(mIdStr), TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null == merchantStoreEntity) {
                log.info("当前发票所属的门店数据还未同步, mId:{}", mIdStr);
                return;
            }
            bizId = merchantStoreEntity.getId();
        } else if(InvoiceConfigEnums.TypeEnum.REGIONAL_ORGANIZATION.getCode().equals(type)) {
            // 大客户维度
            String adminIdStr = data.get("admin_id");
            if(StrUtil.isBlank(adminIdStr)) {
                log.info("鲜沐脏数据，无需处理。invoiceId:{}, adminId：{}", id, adminIdStr);
                return;
            }
            // 判断大客户对应的区域是否存在，不存在就后面再处理
            RegionalOrganizationEntity regionalOrganization = regionalOrganizationQueryRepository.selectByAdminAndTenant(Long.valueOf(adminIdStr), TenantDefaultConstant.XIAN_MU_TENANT_ID);
            if (null == regionalOrganization) {
                log.info("当前发票所属的大客户数据还未同步, adminIdStr:{}", adminIdStr);
                return;
            }
            bizId = regionalOrganization.getId();
        } else {
            log.info("鲜沐脏数据，无需处理。invoiceId:{}, type：{}", id, type);
            return;
        }

        // 3.创建发票信息
        InvoiceConfigEntity invoiceConfigEntity = InvoiceConfigEntity.converterToEntityForBinLog(data);
        invoiceConfigEntity.setBizId(bizId);
        invoiceConfigRepository.createOrUpdate(invoiceConfigEntity);
    }
}
