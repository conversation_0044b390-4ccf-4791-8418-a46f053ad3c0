package net.xianmu.usercenter.application.serviceImpl.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreGroupDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
public class MerchantStoreGroupConverter {


    private MerchantStoreGroupConverter() {
        // 无需实现
    }

    public static List<MerchantStoreGroupDTO> toMerchantStoreGroupDTOList(List<MerchantStoreGroupEntity> merchantStoreGroupEntityList) {
        if (merchantStoreGroupEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupDTO> merchantStoreGroupDTOList = new ArrayList<>();
        for (MerchantStoreGroupEntity merchantStoreGroupEntity : merchantStoreGroupEntityList) {
            merchantStoreGroupDTOList.add(toMerchantStoreGroupDTO(merchantStoreGroupEntity));
        }
        return merchantStoreGroupDTOList;
    }

    public static MerchantStoreGroupDTO toMerchantStoreGroupDTO(MerchantStoreGroupEntity merchantStoreGroupEntity) {
        if (merchantStoreGroupEntity == null) {
            return null;
        }
        MerchantStoreGroupDTO merchantStoreGroupDTO = new MerchantStoreGroupDTO();
        merchantStoreGroupDTO.setStoreId(merchantStoreGroupEntity.getStoreId());
        merchantStoreGroupDTO.setMerchantStoreGroupName(merchantStoreGroupEntity.getName());
        merchantStoreGroupDTO.setMerchantStoreGroupId(merchantStoreGroupEntity.getId());
        merchantStoreGroupDTO.setType(merchantStoreGroupEntity.getType());
        merchantStoreGroupDTO.setCreateTime(merchantStoreGroupEntity.getCreateTime());
        merchantStoreGroupDTO.setUpdateTime(merchantStoreGroupEntity.getUpdateTime());
        return merchantStoreGroupDTO;
    }

}
