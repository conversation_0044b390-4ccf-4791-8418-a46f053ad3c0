package net.xianmu.usercenter.application.serviceImpl.regional;

import cn.hutool.core.collection.CollUtil;
import net.xianmu.usercenter.api.regional.service.RegionalOrganizationQueryService;
import net.xianmu.usercenter.common.input.query.RegionalOrganizationQueryInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:46:41
* @version 1.0
*
*/
@Service
public class RegionalOrganizationQueryServiceImpl implements RegionalOrganizationQueryService {

    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;

    @Override
    public RegionalOrganizationEntity saasGetRegionalByTenantId(Long tenantId) {
        if(tenantId == null) {
            return null;
        }
        RegionalOrganizationQueryInput builder = RegionalOrganizationQueryInput.builder().tenantId(tenantId).build();
        List<RegionalOrganizationEntity> entities = regionalOrganizationQueryRepository.selectByCondition(builder);
        return CollUtil.isEmpty(entities) ? null : entities.get(0);
    }

    @Override
    public List<RegionalOrganizationEntity> saasGetRegionalByTenantId(List<Long> tenantIds) {
        if(CollUtil.isEmpty(tenantIds)) {
            return new ArrayList<>();
        }
        RegionalOrganizationQueryInput builder = RegionalOrganizationQueryInput.builder().tenantIds(tenantIds).build();
        List<RegionalOrganizationEntity> entities = regionalOrganizationQueryRepository.selectByCondition(builder);
        return CollUtil.isEmpty(entities) ? new ArrayList<>() : entities;
    }
}