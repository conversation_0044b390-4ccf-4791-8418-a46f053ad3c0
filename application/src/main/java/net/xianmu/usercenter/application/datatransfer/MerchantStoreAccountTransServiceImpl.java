package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.config.MqBizException;
import net.xianmu.usercenter.application.event.AuthAccountTransferEvent;
import net.xianmu.usercenter.common.constants.RedisConstant;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.common.util.RedisLockUtil;
import net.xianmu.usercenter.common.util.SpringContextHolder;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreAccountDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAccountMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAccountMappingRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 10:53
 */

@Service
@Slf4j
public class MerchantStoreAccountTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private MerchantStoreAccountDomainService merchantStoreAccountDomainService;
    @Autowired
    private MerchantStoreAccountQueryRepository accountQueryRepository;
    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Autowired
    private XmMerchantAccountMappingRepository xmMerchantAccountMappingRepository;
    @Autowired
    private DataTransferRepository dataTransferRepository;
    @Autowired
    private ApplicationContext applicationContext;


    /**
     * binlog 同步
     *
     * @param pair
     * @param dtsModel
     */
    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        String accountIdStr;
        Long xmAccountId;
        MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService = SpringContextHolder.getBean(MerchantStoreAccountTransServiceImpl.class);

        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.warn("子账户表binlog物理删除！");
            accountIdStr = old.get("account_id");
            XmMerchantAccountMappingEntity entity = getAccountMapping(Long.valueOf(accountIdStr));
            if (null != entity) {
                log.info("待删除数据，accountId:{}", entity.getAccountId());
                merchantStoreAccountTransService.deleteForBinLog(entity.getAccountId());
            }
        } else if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            merchantStoreAccountTransService.createAccountForBinLog(data);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            accountIdStr = data.get("account_id");
            xmAccountId = Long.valueOf(accountIdStr);
            // 先查映射，如果没有，那就走新建流程
            XmMerchantAccountMappingEntity mapping = getAccountMapping(xmAccountId);
            if (null == mapping) {
                merchantStoreAccountTransService.createAccountForBinLog(data);
            } else {
                // 更新操作
                merchantStoreAccountTransService.updateAccountForBinLog(data, mapping);
            }
        } else {
            log.warn("未知操作!");
        }

        // 同步到auth
        applicationContext.publishEvent(new AuthAccountTransferEvent(applicationContext, type, old, data));

    }

    private XmMerchantAccountMappingEntity getAccountMapping(Long xmAccountId) {
        XmMerchantAccountMappingEntity mapping = xmMerchantAccountMappingRepository.selectByXmAccountId(xmAccountId);
        if (null == mapping) {
            MerchantStoreAccountEntity entity = accountQueryRepository.selectByXmAccountId(xmAccountId);
            if (entity == null) {
                return null;
            }
            mapping = new XmMerchantAccountMappingEntity();
            mapping.setAccountId(entity.getId());
            mapping.setXmAccountId(entity.getXmAccountId());
        }
        return mapping;
    }

    
    public void createAccountForBinLog(Map<String, String> data) {
        // 幂等，如果已经存在了那就无需再次创建了
        XmMerchantAccountMappingEntity mapping = getAccountMapping(Long.valueOf(data.get("account_id")));
        if (null != mapping) {
            log.info("该账户已存在，无需重复创建。accountId:{}", data.get("account_id"));
            return;
        }

        // 判断门店是否存在，不存在就后面再处理
        String mIdStr = data.get("m_id");
        if (StrUtil.isBlank(mIdStr)) {
            log.error("数据异常，该联系人无关联的门店");
            throw new BizException("数据异常，该联系人无关联的门店");
        }

        String lockKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_BINLOG + mIdStr;
        try {
            boolean success = RedisLockUtil.tryLock(lockKey, 2, 30);
            if (!success) {
                log.warn("门店mid:{}获取锁失败，自动开始重试。", mIdStr);
                throw new BizException("当前门店基础、地址数据正在同步");
            }
            log.info("加锁成功，开始初始化门店账户数据, key: {}, time:{}", lockKey, System.currentTimeMillis());

            MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService = SpringContextHolder.getBean(MerchantStoreAccountTransServiceImpl.class);
            merchantStoreAccountTransService.createAccountForBinLogInner(data);
        } catch (Exception e) {
            throw e;
        } finally {
            RedisLockUtil.unlockIfHeldByCurrentThread(lockKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void createAccountForBinLogInner(Map<String, String> data) {
        Long storeId = this.getStoreId(data);
        MerchantStoreAccountEntity build = MerchantStoreAccountEntity.converterToEntityForBinLog(data);
        build.setStoreId(storeId);
        merchantStoreAccountDomainService.createOrUpdateForBinLog(build, Long.valueOf(data.get("account_id")));

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAccountForBinLog(Map<String, String> data, XmMerchantAccountMappingEntity mapping) {
        // 获取最新的m_id，兼容店铺迁移逻辑
        Long storeId = this.getStoreId(data);
        MerchantStoreAccountEntity build = MerchantStoreAccountEntity.converterToEntityForBinLog(data);
        build.setId(mapping.getAccountId());
        build.setStoreId(storeId);
        merchantStoreAccountDomainService.createOrUpdateForBinLog(build, mapping.getXmAccountId());
    }

    private Long getStoreId(Map<String, String> data) {
        // 判断门店是否存在，不存在就后面再处理
        String mIdStr = data.get("m_id");
        if (StrUtil.isBlank(mIdStr)) {
            log.error("数据异常，该子账户无关联的门店");
            throw new BizException("数据异常，该子账户无关联的门店");
        }
        MerchantStoreEntity merchantStoreEntity = merchantStoreQueryRepository.selectByMId(Long.valueOf(mIdStr), TenantDefaultConstant.XIAN_MU_TENANT_ID);
        if (null == merchantStoreEntity) {
            log.warn("当前账户:{}所属的门店数据还未同步, mId:{}", data.get("account_id"), mIdStr);
            return null;
        }
        return merchantStoreEntity.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteForBinLog(Long id) {
        merchantStoreAccountDomainService.deleteForBinLog(id);
    }


    @Override
    public List<Map<String, Object>> selectDataList(Long accountId, Integer offset) {
        return dataTransferRepository.selectMerchantAccountList(accountId, offset);
    }


    public void refreshAccountData(List<Long> idList) {
        List<Map<String, Object>> maps = dataTransferRepository.selectMerchantAccountListByIdList(idList);
        if (CollUtil.isEmpty(maps)) {
            log.warn("账户不存在！");
            return;
        }
        MerchantStoreAccountTransServiceImpl bean = SpringContextHolder.getBean(MerchantStoreAccountTransServiceImpl.class);
        maps.forEach(map -> bean.doTransfer(map, false));
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteMerchantData(List<Long> accountIdList) {
        if (CollUtil.isEmpty(accountIdList)) {
            return;
        }
        for (Long xmAccountId : accountIdList) {
            XmMerchantAccountMappingEntity entity = getAccountMapping(xmAccountId);
            if (null != entity) {
                log.info("待删除数据，accountId:{}", entity.getAccountId());
                merchantStoreAccountDomainService.deleteForBinLog(entity.getAccountId());
            }
        }
    }


    @Override
    public void doTransfer(Map<String, Object> data, boolean isCreate) {
        if (CollUtil.isEmpty(data)) {
            return;
        }
        MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService = SpringContextHolder.getBean(MerchantStoreAccountTransServiceImpl.class);
        Map<String, String> stringMap = MapUtil.convertToStringMap(data);

        if (isCreate) {
            merchantStoreAccountTransService.createAccountForBinLog(stringMap);
        } else {
            Long xmAccountId = Long.valueOf(stringMap.get("account_id"));
            // 先查映射，如果没有，那就走新建流程
            XmMerchantAccountMappingEntity mapping = getAccountMapping(xmAccountId);
            if (null == mapping) {
                merchantStoreAccountTransService.createAccountForBinLog(stringMap);
            } else {
                // 更新操作
                merchantStoreAccountTransService.updateAccountForBinLog(stringMap, mapping);
            }
        }

        // 同步到auth
        String type = isCreate ? DtsModelTypeEnum.INSERT.name() : DtsModelTypeEnum.UPDATE.name();
        applicationContext.publishEvent(new AuthAccountTransferEvent(applicationContext, type, stringMap, stringMap));
    }
}
