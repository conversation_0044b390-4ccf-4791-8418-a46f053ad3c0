package net.xianmu.usercenter.application.serviceImpl.tenant.converter.v2;

import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.dto.TenantDTO;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-05 11:32:09
 * @version 1.0
 *
 */
public class TenantConverterV2 {


    private TenantConverterV2() {
        // 无需实现
    }


    public static List<TenantAndBusinessInfoResultResp> toTenantAndBusinessInfoResultRespList(List<TenantAndBusinessEntity> tenantAndBusinessEntityList) {
        if (tenantAndBusinessEntityList == null) {
            return Collections.emptyList();
        }
        List<TenantAndBusinessInfoResultResp> tenantAndBusinessInfoResultRespList = new ArrayList<>();
        for (TenantAndBusinessEntity tenantAndBusinessEntity : tenantAndBusinessEntityList) {
            tenantAndBusinessInfoResultRespList.add(toTenantAndBusinessInfoResultResp(tenantAndBusinessEntity));
        }
        return tenantAndBusinessInfoResultRespList;
    }

    public static TenantAndBusinessInfoResultResp toTenantAndBusinessInfoResultResp(TenantAndBusinessEntity tenantAndBusinessEntity) {
        if (tenantAndBusinessEntity == null) {
            return null;
        }
        TenantAndBusinessInfoResultResp tenantAndBusinessInfoResultResp = new TenantAndBusinessInfoResultResp();
        tenantAndBusinessInfoResultResp.setTenantId(tenantAndBusinessEntity.getTenantId());
        tenantAndBusinessInfoResultResp.setTenantName(tenantAndBusinessEntity.getTenantName());
        tenantAndBusinessInfoResultResp.setPhone(tenantAndBusinessEntity.getPhone());
        tenantAndBusinessInfoResultResp.setCompanyName(tenantAndBusinessEntity.getCompanyName());
        tenantAndBusinessInfoResultResp.setContactName(tenantAndBusinessEntity.getContactName());
        tenantAndBusinessInfoResultResp.setCreditCode(tenantAndBusinessEntity.getCreditCode());
        tenantAndBusinessInfoResultResp.setTenantCompanyId(tenantAndBusinessEntity.getTenantCompanyId());
        tenantAndBusinessInfoResultResp.setAdminId(tenantAndBusinessEntity.getAdminId());
        tenantAndBusinessInfoResultResp.setCreateTime(tenantAndBusinessEntity.getCreateTime());
        tenantAndBusinessInfoResultResp.setUpdateTime(tenantAndBusinessEntity.getUpdateTime());
        tenantAndBusinessInfoResultResp.setOpUname(tenantAndBusinessEntity.getOpUname());
        tenantAndBusinessInfoResultResp.setType(tenantAndBusinessEntity.getTenantType());
        tenantAndBusinessInfoResultResp.setStatus(tenantAndBusinessEntity.getStatus());
// Not mapped TO fields:
// type
// status
        return tenantAndBusinessInfoResultResp;
    }

    public static List<TenantAndBusinessEntity> toTenantAndBusinessEntityList(List<TenantAndBusinessInfoResultResp> tenantAndBusinessInfoResultRespList) {
        if (tenantAndBusinessInfoResultRespList == null) {
            return Collections.emptyList();
        }
        List<TenantAndBusinessEntity> tenantAndBusinessEntityList = new ArrayList<>();
        for (TenantAndBusinessInfoResultResp tenantAndBusinessInfoResultResp : tenantAndBusinessInfoResultRespList) {
            tenantAndBusinessEntityList.add(toTenantAndBusinessEntity(tenantAndBusinessInfoResultResp));
        }
        return tenantAndBusinessEntityList;
    }

    public static TenantAndBusinessEntity toTenantAndBusinessEntity(TenantAndBusinessInfoResultResp tenantAndBusinessInfoResultResp) {
        if (tenantAndBusinessInfoResultResp == null) {
            return null;
        }
        TenantAndBusinessEntity tenantAndBusinessEntity = new TenantAndBusinessEntity();
        tenantAndBusinessEntity.setTenantId(tenantAndBusinessInfoResultResp.getTenantId());
        tenantAndBusinessEntity.setTenantName(tenantAndBusinessInfoResultResp.getTenantName());
        tenantAndBusinessEntity.setPhone(tenantAndBusinessInfoResultResp.getPhone());
        tenantAndBusinessEntity.setCompanyName(tenantAndBusinessInfoResultResp.getCompanyName());
        tenantAndBusinessEntity.setContactName(tenantAndBusinessInfoResultResp.getContactName());
        tenantAndBusinessEntity.setCreditCode(tenantAndBusinessInfoResultResp.getCreditCode());
        tenantAndBusinessEntity.setTenantCompanyId(tenantAndBusinessInfoResultResp.getTenantCompanyId());
        tenantAndBusinessEntity.setAdminId(tenantAndBusinessInfoResultResp.getAdminId());
        tenantAndBusinessEntity.setCreateTime(tenantAndBusinessInfoResultResp.getCreateTime());
        tenantAndBusinessEntity.setUpdateTime(tenantAndBusinessInfoResultResp.getUpdateTime());
        tenantAndBusinessEntity.setOpUname(tenantAndBusinessInfoResultResp.getOpUname());
// Not mapped FROM fields:
// type
// status
        return tenantAndBusinessEntity;
    }
}
