package net.xianmu.usercenter.application.serviceImpl.businessInfo;

import net.xianmu.usercenter.api.businessInfo.service.BusinessInformationCommandService;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.businessInfo.domain.BusinessInformationDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 17:25
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class BusinessInformationCommandServiceImpl implements BusinessInformationCommandService {

    @Autowired
    private BusinessInformationDomainService businessInformationDomainService;

    @Override
    public void insert(BusinessInformationCommandInput input) {
        businessInformationDomainService.insert(input);
    }

    @Override
    public void update(BusinessInformationCommandInput input) {
        businessInformationDomainService.update(input);
    }

    @Override
    public void updateWithNull(BusinessInformationCommandInput input) {
        businessInformationDomainService.updateWithNull(input);
    }
}
