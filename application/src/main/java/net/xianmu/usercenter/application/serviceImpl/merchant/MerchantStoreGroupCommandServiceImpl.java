package net.xianmu.usercenter.application.serviceImpl.merchant;

import net.xianmu.usercenter.api.merchant.service.MerchantStoreGroupCommandService;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.MerchantStoreGroupBatchImportDTO;
import net.xianmu.usercenter.common.input.command.MerchantStoreGroupCommandInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreGroupDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-06-01 17:36:39
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantStoreGroupCommandServiceImpl implements MerchantStoreGroupCommandService {

    @Autowired
    private MerchantStoreGroupDomainService merchantStoreGroupDomainService;

    @Override
    public Long create(MerchantStoreGroupCommandInput input) {
        ValidateUtil.paramValidate(input, "tenantId", "name");
        if(null == input.getType()) {
            input.setType(TenantDefaultConstant.GROUP_TYPE_NOT_DEFAULT);
        }

        MerchantStoreGroupEntity entity = merchantStoreGroupDomainService.create(input);
        return entity.getId();
    }

    @Override
    public Boolean update(MerchantStoreGroupCommandInput input) {

        return merchantStoreGroupDomainService.update(input);
    }

    @Override
    public Boolean remove(MerchantStoreGroupCommandInput input) {
        return merchantStoreGroupDomainService.remove(input);
    }

    @Override
    public List<MerchantStoreGroupBatchImportDTO> batchCreate(List<MerchantStoreGroupBatchImportDTO> list, Long tenantId) {
        return merchantStoreGroupDomainService.batchCreate(list, tenantId);
    }
}