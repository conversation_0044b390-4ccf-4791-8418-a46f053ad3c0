package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.config.MqBizException;
import net.xianmu.usercenter.api.common.DbTableDml;
import net.xianmu.usercenter.common.converter.BinLogConverter;
import net.xianmu.usercenter.common.converter.FastJsonConverter;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmDataTransferTaskParamListDTO;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.util.SpringContextHolder;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/6/28 11:32
 */

@Slf4j
public abstract class AbstractDbTableDml implements DbTableDml {


    @Override
    public void handle(DtsModel dtsModel) {
        List<XmPair<Map<String, String>, Map<String, String>>> pairList = BinLogConverter.getAlignedData(dtsModel);
        if (CollUtil.isEmpty(pairList)) {
            log.warn("DtsModel数据格式有误!!");
            return;
        }

        // 数据转换
        for (XmPair<Map<String, String>, Map<String, String>> pair : pairList) {
            try {
                log.info("当前同步数据：data: {}", FastJsonConverter.convert(pair));
                AbstractDbTableDml bean = SpringContextHolder.getBean(this.getClass());
                bean.doService(pair, dtsModel);
            }  catch (BizException e) {
                log.error("biz exception code :{}, message:{}", FastJsonConverter.convert(e.getErrorCode()), e.getMessage());
                throw e;
            } catch (Exception e) {
                log.error("fail pair data :{}", FastJsonConverter.convert(pair));
                // 直接告警，人工补偿
                log.error("binlog同步数据失败!", e);
            }
        }

    }


    /**
     * 解析binlog，触发业务同步
     *
     * @param pair
     * @param dtsModel
     */
    public abstract void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel);


    /**
     * 定时任务触发同步存量数据
     */
    @Override
    /**
     * 定时任务
     *
     * @param dto
     */
    public void handleForTask(XmDataTransferTaskParamListDTO dto, boolean isCreate) throws InterruptedException {
        Long startId = dto.getStartId() == null ? 0L : dto.getStartId();
        Long endId = dto.getEndId() == null ? Long.MAX_VALUE : dto.getEndId();
        Integer sleep = dto.getSleep();
        Integer offset = dto.getOffset() == null ? 500 : dto.getOffset();
        Integer threadNum = dto.getThreadNum();

        while (startId < endId) {
            // 捞取数据
            List<Map<String, Object>> mapPageInfo = this.selectDataList(startId, offset);
            if (CollUtil.isEmpty(mapPageInfo)) {
                log.info("该批次暂无数据, startId:{},offset :{}", startId, offset);
                return;
            }

            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            if (threadNum == null) {
                // 同步处理
                this.transferList(mapPageInfo, isCreate);
            } else {
                // 并发处理
                ThreadPoolTaskExecutor executor = SpringContextHolder.getBean("dataSyncThreadPoolTaskExecutor", ThreadPoolTaskExecutor.class);
                int threadBatchSize = offset / threadNum + (offset % threadNum == 0 ? 0 : 1);
                List<List<Map<String, Object>>> split = CollUtil.split(mapPageInfo, threadBatchSize);
                CountDownLatch latch = new CountDownLatch(split.size());
                split.forEach(list -> executor.execute(() -> asyncTransferList(list, latch, isCreate)));
                latch.await(2, TimeUnit.HOURS);
            }
            log.info("当前批次执行完毕, startId:{},offset :{}", startId, offset);

            // 获取数量不足批次大小的时候代表同步结束了
            int currentSize = mapPageInfo.size();
            if (currentSize < offset) {
                break;
            }

            // 获取当前批次的最大id，用于下次循环时优化性能
            startId = this.getMaxId(mapPageInfo);

            // sleep?
            if (sleep != null) {
                Thread.sleep(sleep);
            }
        }
    }

    private void asyncTransferList(List<Map<String, Object>> list, CountDownLatch latch, boolean isCreate) {
        this.transferList(list, isCreate);
        latch.countDown();
    }

    private void transferList(List<Map<String, Object>> list, boolean isCreate) {
        AbstractDbTableDml bean = SpringContextHolder.getBean(this.getClass());
        for (Map<String, Object> stringObjectMap : list) {
            try {
                bean.doTransfer(stringObjectMap, isCreate);
            } catch (Exception e) {
                log.error("error data :{}", FastJsonConverter.convert(stringObjectMap));
                log.error("定时任务同步鲜沐数据出错!", e);
            }
        }
    }


    /**
     * 获取当前批次的id最大值
     *
     * @param
     * @return
     */
    public Long getMaxId(List<Map<String, Object>> mapPageInfo) {
        Map<String, Object> map = mapPageInfo.get(mapPageInfo.size() - 1);
        return Long.valueOf(map.get("id").toString());
    }

    /**
     * 获取待执行的数据列表
     *
     * @param adminId
     * @param offset
     * @return
     */
    public abstract List<Map<String, Object>> selectDataList(Long adminId, Integer offset);

    /**
     * 定时任务触发同步存量数据
     */
    public abstract void doTransfer(Map<String, Object> data, boolean isCreate);


}
