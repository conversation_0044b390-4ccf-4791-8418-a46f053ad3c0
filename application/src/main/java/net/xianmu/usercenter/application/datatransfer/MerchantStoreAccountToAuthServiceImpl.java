package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.login.UserStatusEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.application.datatransfer.AbstractDbTableDml;
import net.xianmu.usercenter.common.config.NacosPropertiesHolder;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.converter.FastJsonConverter;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.util.DateUtil;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import net.xianmu.usercenter.facade.auth.AuthUserFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:58
 */
@Service
@Slf4j
public class MerchantStoreAccountToAuthServiceImpl extends AbstractDbTableDml {

    @Autowired
    private MerchantStoreAccountQueryRepository queryRepository;

    @Autowired
    private AuthUserFacade authUserFacade;

    @Autowired
    private DataTransferRepository dataTransferRepository;

    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;


    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        Map<String, String> data = pair.getKey();
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.warn("binlog物理删除！");
            return;
        }


        Long tenantId = Long.valueOf(data.get("tenant_id"));
        if (TenantDefaultConstant.XIAN_MU_TENANT_ID.equals(tenantId)) {
            log.info("鲜沐数据，无需处理");
            return;
        }
        // 掉
        authUserFacade.createAccount(SystemOriginEnum.COSFO_MALL, this.buildUserBaseFromSaas(data), this.buildExtendFromSaas(data));
    }


    public void xmTransferToAuthTask() throws InterruptedException {
        Long startId = 0L;
        int offset = 200;

        while (true) {
            // 捞取数据
            List<Map<String, Object>> mapPageInfo = dataTransferRepository.selectMerchantAccountList(startId, offset);
            log.info("当前执行批次, startId:{},offset :{}", startId, offset);
            if (CollUtil.isEmpty(mapPageInfo)) {
                log.info("该批次暂无数据, startId:{},offset :{}", startId, offset);
                return;
            }

            // 同步
            mapPageInfo.forEach(data -> {
                Map<String, String> stringMap = MapUtil.convertToStringMap(data);
                // 已删除状态的无需初始化
                Integer flag = Integer.valueOf(stringMap.get("delete_flag"));
                if(MerchantAccountEnums.DeleteFlag.DELETED.getCode().equals(flag)) {
                    log.info("已删除状态的数据无需同步到auth.data:{}", FastJsonConverter.convert(data));
                    return;
                }
                transferXmStockData(stringMap, false);
            });

            log.info("当前批次执行完毕, startId:{},offset :{}", startId, offset);

            // 获取数量不足批次大小的时候代表同步结束了
            int currentSize = mapPageInfo.size();
            if (currentSize < offset) {
                break;
            }
   
            startId = this.getMaxId(mapPageInfo);

            // sleep?
            Thread.sleep(1000);
        }
    }

    /**
     * 同步xm数据
     *
     * @param dataMap
     * @param deleteFlag
     */
    public void transferXmStockData(Map<String, String> dataMap, boolean deleteFlag) {
        try {
            // 脏数据名单的数据不同步
            String phone = dataMap.get("phone");
            if (StrUtil.isBlank(phone)) {
                log.error("数据异常,手机号数据为空!");
                return;
            }
            List<String> dirtyPhoneList = nacosPropertiesHolder.getDirtyPhoneList();
            if (CollUtil.isNotEmpty(dirtyPhoneList) && dirtyPhoneList.contains(phone)) {
                log.warn("脏数据暂不同步。phone: {}, dirtyPhoneList: {}", phone, FastJsonConverter.convert(dirtyPhoneList));
                return;
            }

            authUserFacade.createAccount(SystemOriginEnum.MALL, this.buildUserBaseFromXm(dataMap), this.buildExtendFromXm(dataMap, deleteFlag));
        } catch (Exception e) {
            log.error("鲜沐账户数据同步到auth出错!!,accountId: {}", dataMap.get("account_id"), e);
        }
    }

    public void saasTransferToAuthTask() {
        int count = 0;

        // 1.查询所有saas子账户数据
        List<MerchantStoreAccountEntity> entities = queryRepository.selectSaasAccount();
        // 掉
        if (CollUtil.isNotEmpty(entities)) {
            for (MerchantStoreAccountEntity entity : entities) {
                authUserFacade.createAccount(SystemOriginEnum.COSFO_MALL, this.buildUserBase(entity), this.buildExtend(entity));
                count++;

                if (count == 200) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    count = 0;
                }
            }
        }
    }


    private UserBase buildUserBaseFromXm(Map<String, String> data) {
        UserBase base = new UserBase();
        base.setPhone(data.get("phone"));
        base.setUsername(data.get("phone"));
        base.setTenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID);
        base.setStatus(this.converterStatusForAuth(Integer.valueOf(data.get("delete_flag"))));
        return base;
    }


    private BaseUserExtend buildExtendFromXm(Map<String, String> data, boolean deleteFlag) {
        BaseUserExtend base = new BaseUserExtend();
        base.setBizUserId(Long.valueOf(data.get("account_id")));
        base.setMpOpenid(data.get("mp_openid"));
        base.setOpenid(data.get("openid"));
        base.setUnionId(data.get("unionid"));
        base.setMpUnionId(data.get("unionid"));
        base.setDeleteAccountRelation(deleteFlag);
        base.setLastLoginTime(DateUtil.toDate(DateUtil.getTimeWithBinLogPatternDefault(data.get("login_time"), null)));
        return base;
    }


    private UserBase buildUserBaseFromSaas(Map<String, String> data) {
        UserBase base = new UserBase();
        base.setPhone(data.get("phone"));
        base.setUsername(data.get("phone"));
        base.setTenantId(Long.valueOf(data.get("tenant_id")));
        base.setStatus(this.converterStatusForAuth(Integer.valueOf(data.get("delete_flag"))));
        return base;
    }


    private BaseUserExtend buildExtendFromSaas(Map<String, String> data) {
        BaseUserExtend base = new BaseUserExtend();
        base.setBizUserId(Long.valueOf(data.get("id")));
        base.setMpOpenid(data.get("open_id"));
        base.setOpenid(data.get("oa_open_id"));
        base.setUnionId(data.get("union_id"));
        base.setMpUnionId(data.get("union_id"));
        base.setAuditStatus(Integer.valueOf(data.get("status")));
        base.setLastLoginTime(DateUtil.toDate(DateUtil.getTimeWithBinLogPatternDefault(data.get("last_login_time"), null)));
        return base;
    }


    private UserBase buildUserBase(MerchantStoreAccountEntity accountEntity) {
        UserBase base = new UserBase();
        base.setPhone(accountEntity.getPhone());
        base.setUsername(accountEntity.getPhone());
        base.setTenantId(accountEntity.getTenantId());
        base.setStatus(this.converterStatusForAuth(accountEntity.getDeleteFlag()));
        return base;
    }


    private BaseUserExtend buildExtend(MerchantStoreAccountEntity accountEntity) {
        BaseUserExtend base = new BaseUserExtend();
        base.setBizUserId(accountEntity.getId());
        base.setMpOpenid(accountEntity.getOpenId());
        base.setOpenid(accountEntity.getOaOpenId());
        base.setUnionId(accountEntity.getUnionId());
        base.setMpUnionId(accountEntity.getUnionId());
        base.setAuditStatus(accountEntity.getStatus());
        base.setLastLoginTime(DateUtil.toDate(accountEntity.getLastLoginTime()));
        return base;
    }

    private Integer converterStatusForAuth(Integer deleteFlag) {
        if (MerchantAccountEnums.DeleteFlag.DELETED.getCode().equals(deleteFlag)) {
            return UserStatusEnum.IN_VALID.getStatus();
        }
        return UserStatusEnum.VALID.getStatus();
    }


    @Override
    public List<Map<String, Object>> selectDataList(Long adminId, Integer offset) {
        return null;
    }

    @Override
    public void doTransfer(Map<String, Object> data, boolean isCreate) {

    }
}
