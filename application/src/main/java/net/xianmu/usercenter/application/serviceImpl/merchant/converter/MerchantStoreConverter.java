package net.xianmu.usercenter.application.serviceImpl.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreConverter {


    private MerchantStoreConverter() {
        // 无需实现
    }

    public static List<MerchantStoreDTO> toMerchantStoreDTOList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreDTO> merchantStoreDTOList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreDTOList.add(toMerchantStoreDTO(merchantStoreEntity));
        }
        return merchantStoreDTOList;
    }

    public static MerchantStoreDTO toMerchantStoreDTO(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        merchantStoreDTO.setId(merchantStoreEntity.getId());
        merchantStoreDTO.setTenantId(merchantStoreEntity.getTenantId());
        merchantStoreDTO.setStoreName(merchantStoreEntity.getStoreName());
        merchantStoreDTO.setType(merchantStoreEntity.getType());
        merchantStoreDTO.setRegisterTime(merchantStoreEntity.getRegisterTime());
        merchantStoreDTO.setStatus(merchantStoreEntity.getStatus());
        merchantStoreDTO.setAuditRemark(merchantStoreEntity.getAuditRemark());
        merchantStoreDTO.setRemark(merchantStoreEntity.getRemark());
        merchantStoreDTO.setAuditTime(merchantStoreEntity.getAuditTime());
        merchantStoreDTO.setCreateTime(merchantStoreEntity.getCreateTime());
        merchantStoreDTO.setUpdateTime(merchantStoreEntity.getUpdateTime());
        merchantStoreDTO.setBillSwitch(merchantStoreEntity.getBillSwitch());
        merchantStoreDTO.setOnlinePayment(merchantStoreEntity.getOnlinePayment());
        merchantStoreDTO.setStoreNo(merchantStoreEntity.getStoreNo());
        merchantStoreDTO.setBalanceAuthority(merchantStoreEntity.getBalanceAuthority());
        merchantStoreDTO.setPhone(merchantStoreEntity.getPhone());
        merchantStoreDTO.setProvince(merchantStoreEntity.getProvince());
        merchantStoreDTO.setCity(merchantStoreEntity.getCity());
        merchantStoreDTO.setArea(merchantStoreEntity.getArea());
        merchantStoreDTO.setAddress(merchantStoreEntity.getAddress());
        merchantStoreDTO.setHouseNumber(merchantStoreEntity.getHouseNumber());
        merchantStoreDTO.setDeliveryAddress(merchantStoreEntity.getDeliveryAddress());
        merchantStoreDTO.setPlaceOrderPermissionTimeLimited(merchantStoreEntity.getPlaceOrderPermissionTimeLimited ());
        merchantStoreDTO.setPlaceOrderPermissionExpiryTime(merchantStoreEntity.getPlaceOrderPermissionExpiryTime ());
        merchantStoreDTO.setEnableOfflinePayment (merchantStoreEntity.getEnableOfflinePayment ());
        return merchantStoreDTO;
    }

}
