package net.xianmu.usercenter.application.serviceImpl.tenant;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.tenant.service.MerchantCommandService;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.input.command.MerchantCommandInput;
import net.xianmu.usercenter.domain.tenant.domain.MerchantDomainService;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantQueryRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-26 18:35:35
 */

@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MerchantCommandServiceImpl  implements MerchantCommandService {

    @Autowired
    private MerchantDomainService merchantDomainService;
    @Autowired
    private TenantQueryRepository tenantQueryRepository;
    @Autowired

    private MerchantQueryRepository merchantQueryRepository;

    @Override
    public Long create(MerchantCommandInput input) {
        MerchantEntity merchantEntity = merchantDomainService.create(input);
        return merchantEntity.getId();
    }

    @Override
    public Boolean update(MerchantCommandInput input) {
        return merchantDomainService.updateSelective(input);
    }


    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        String adminIdStr = data.get("admin_id");
        if (StrUtil.isBlank(adminIdStr)) {
            log.warn("adminId为空!");
            return;
        }

        TenantEntity tenantEntity = tenantQueryRepository.getTenantByAdminId(Long.valueOf(adminIdStr));
        if (null == tenantEntity) {
            // 根据admin_id查不到租户id时，暂时不处理，等到存量数据同步后就正常了
            log.info("当前大客户暂无租户信息：adminId：{}", adminIdStr);
            return;
        }
        Long tenantId = tenantEntity.getId();
        MerchantCommandInput input;

        // 根据admin_id获取租户信息
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            input = converterToMerchantCommandInput(data, tenantId);
            merchantDomainService.create(input);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            // 先查映射，如果没有，那就走新建流程
            MerchantEntity merchantEntity = merchantQueryRepository.selectByTenantId(tenantId);
            input = converterToMerchantCommandInput(data, tenantId);
            if (null == merchantEntity) {
                merchantDomainService.create(input);
                return;
            }
            // 更新操作
            input.setId(merchantEntity.getId());
            merchantDomainService.updateSelective(input);
        } else {
            // 删除操作
        }

    }


    private MerchantCommandInput converterToMerchantCommandInput(Map<String, String> data, Long tenantId){
        return MerchantCommandInput.builder()
                .tenantId(tenantId)
                .build();
    }
}