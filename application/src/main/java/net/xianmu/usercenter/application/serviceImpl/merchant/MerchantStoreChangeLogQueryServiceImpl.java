package net.xianmu.usercenter.application.serviceImpl.merchant;


import net.xianmu.usercenter.api.merchant.service.MerchantStoreChangeLogQueryService;
import net.xianmu.usercenter.common.input.query.MerchantStoreChangeLogQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreChangeLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-10-27 14:57:37
* @version 1.0
*
*/
@Service
public class MerchantStoreChangeLogQueryServiceImpl implements MerchantStoreChangeLogQueryService {

    @Autowired
    private MerchantStoreChangeLogRepository merchantStoreChangeLogQueryRepository;

    @Override
    public List<MerchantStoreChangeLogEntity> getMerchantChangeLogs(MerchantStoreChangeLogQueryInput input) {
        return merchantStoreChangeLogQueryRepository.selectByCondition(input);
    }
}