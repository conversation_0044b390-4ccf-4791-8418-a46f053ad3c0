package net.xianmu.usercenter.application.serviceImpl.businessInfo;

import net.xianmu.usercenter.api.businessInfo.dto.BusinessInformationDTO;
import net.xianmu.usercenter.api.businessInfo.service.BusinessInformationQueryService;
import net.xianmu.usercenter.application.serviceImpl.businessInfo.converter.BusinessInformationConverter;
import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;
import net.xianmu.usercenter.domain.businessInfo.domain.BusinessInformationDomainService;
import net.xianmu.usercenter.domain.businessInfo.repository.BusinessInformationQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 17:25
 */
@Service
public class BusinessInformationQueryServiceImpl implements BusinessInformationQueryService {

    @Autowired
    private BusinessInformationDomainService businessInformationDomainService;

    @Autowired
    private BusinessInformationQueryRepository queryRepository;

    @Override
    public BusinessInformationDTO getBusinessInfoByBizIdAndType(BusinessInformationQueryInput input) {
        return BusinessInformationConverter.toBusinessInformationDTO(businessInformationDomainService.getBusinessInfoByBizIdAndType(input));
    }

    @Override
    public List<BusinessInformationDTO> getBusinessInfos(BusinessInformationQueryInput input) {
        return BusinessInformationConverter.toBusinessInformationDTOList(queryRepository.selectByCondition(input));
    }
}
