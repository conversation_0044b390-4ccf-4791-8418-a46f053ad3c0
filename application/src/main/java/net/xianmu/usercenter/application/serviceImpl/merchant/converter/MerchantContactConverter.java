package net.xianmu.usercenter.application.serviceImpl.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
public class MerchantContactConverter {


    private MerchantContactConverter() {
        // 无需实现
    }

    public static List<MerchantContactDTO> toMerchantContactDTOList(List<MerchantContactEntity> merchantContactEntityList) {
        if (merchantContactEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactDTO> merchantContactDTOList = new ArrayList<>();
        for (MerchantContactEntity merchantContactEntity : merchantContactEntityList) {
            merchantContactDTOList.add(toMerchantContactDTO(merchantContactEntity));
        }
        return merchantContactDTOList;
    }

    public static MerchantContactDTO toMerchantContactDTO(MerchantContactEntity merchantContactEntity) {
        if (merchantContactEntity == null) {
            return null;
        }
        MerchantContactDTO merchantContactDTO = new MerchantContactDTO();
        merchantContactDTO.setId(merchantContactEntity.getId());
        merchantContactDTO.setTenantId(merchantContactEntity.getTenantId());
        merchantContactDTO.setAddressId(merchantContactEntity.getAddressId());
        merchantContactDTO.setName(merchantContactEntity.getName());
        merchantContactDTO.setPhone(merchantContactEntity.getPhone());
        merchantContactDTO.setDefaultFlag(merchantContactEntity.getDefaultFlag());
        merchantContactDTO.setCreateTime(merchantContactEntity.getCreateTime());
        merchantContactDTO.setUpdateTime(merchantContactEntity.getUpdateTime());
        return merchantContactDTO;
    }

    public static List<MerchantContactEntity> toMerchantContactEntityList(List<MerchantContactDTO> merchantContactDTOList) {
        if (merchantContactDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactEntity> merchantContactEntityList = new ArrayList<>();
        for (MerchantContactDTO merchantContactDTO : merchantContactDTOList) {
            merchantContactEntityList.add(toMerchantContactEntity(merchantContactDTO));
        }
        return merchantContactEntityList;
    }

    public static MerchantContactEntity toMerchantContactEntity(MerchantContactDTO merchantContactDTO) {
        if (merchantContactDTO == null) {
            return null;
        }
        MerchantContactEntity merchantContactEntity = new MerchantContactEntity();
        merchantContactEntity.setId(merchantContactDTO.getId());
        merchantContactEntity.setTenantId(merchantContactDTO.getTenantId());
        merchantContactEntity.setAddressId(merchantContactDTO.getAddressId());
        merchantContactEntity.setName(merchantContactDTO.getName());
        merchantContactEntity.setPhone(merchantContactDTO.getPhone());
        merchantContactEntity.setDefaultFlag(merchantContactDTO.getDefaultFlag());
        merchantContactEntity.setCreateTime(merchantContactDTO.getCreateTime());
        merchantContactEntity.setUpdateTime(merchantContactDTO.getUpdateTime());
        return merchantContactEntity;
    }
}
