package net.xianmu.usercenter.application.serviceImpl.tenant;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.tenant.dto.TenantAccountDTO;
import net.xianmu.usercenter.api.tenant.service.TenantAccountQueryService;
import net.xianmu.usercenter.application.serviceImpl.tenant.converter.TenantAccountConverter;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.tenant.domain.TenantAccountDomainService;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;
import net.xianmu.usercenter.domain.tenant.repository.TenantAccountQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:30
 */
@Service
public class TenantAccountQueryServiceImpl implements TenantAccountQueryService {

    @Autowired
    private TenantAccountDomainService tenantAccountDomainService;

    @Autowired
    private TenantAccountQueryRepository tenantAccountQueryRepository;

    @Override
    public TenantAccountDTO getTenantAccount(Long authUserId) {
        TenantAccountEntity tenantAccountEntity = tenantAccountDomainService.selectTenantAccountByAuthUserId(authUserId);
        return TenantAccountConverter.toTenantAccountDTO(tenantAccountEntity);
    }

    @Override
    public List<TenantAccountDTO> getTenantAccountsByAuthUserIds(List<Long> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return TenantAccountConverter.toTenantAccountDTOList(tenantAccountQueryRepository.selectByAuthUserIds(list));
    }

    @Override
    public List<TenantAccountDTO> getTenantAccounts(TenantAccountListQueryInput req) {
        return TenantAccountConverter.toTenantAccountDTOList(tenantAccountQueryRepository.selectByByCondition(req));
    }

    @Override
    public TenantAccountDTO getTenantAccountById(Long id) {
        TenantAccountEntity tenantAccountEntity = tenantAccountDomainService.selectTenantAccountById(id);
        return TenantAccountConverter.toTenantAccountDTO(tenantAccountEntity);
    }

    @Override
    public List<TenantAccountDTO> getTenantAccountByTenantIdsAndPhone(List<Long> tenantIdList, String phone) {
        if (CollUtil.isEmpty(tenantIdList)) {
            return Collections.emptyList();
        }
        return TenantAccountConverter.toTenantAccountDTOList(tenantAccountQueryRepository.getTenantAccountByTenantIdsAndPhone(tenantIdList, phone));
    }

    @Override
    public PageInfo<TenantAccountDTO> getTenantAccountsPage(TenantAccountListQueryInput input, PageQueryInput pageQueryInput) {

        PageInfo<TenantAccountEntity> tenantAccountsPage = tenantAccountDomainService.getTenantAccountsPage(input, pageQueryInput);
        return PageInfoConverter.toPageResp(tenantAccountsPage, TenantAccountConverter::toTenantAccountDTO);
    }
}
