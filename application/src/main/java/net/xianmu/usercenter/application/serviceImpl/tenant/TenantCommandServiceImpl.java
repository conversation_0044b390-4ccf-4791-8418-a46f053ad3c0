package net.xianmu.usercenter.application.serviceImpl.tenant;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.businessInfo.service.BusinessInformationCommandService;
import net.xianmu.usercenter.api.tenant.service.TenantCommandService;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreDomainService;
import net.xianmu.usercenter.domain.regional.domain.RegionalOrganizationDomainService;
import net.xianmu.usercenter.domain.tenant.domain.TenantDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:58
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TenantCommandServiceImpl implements TenantCommandService {
    @Autowired
    private TenantDomainService tenantDomainService;
    @Autowired
    private MerchantStoreDomainService merchantStoreDomainService;
    @Autowired
    private RegionalOrganizationDomainService regionalOrganizationDomainService;
    @Autowired
    private BusinessInformationCommandService businessInformationCommandService;



    @Override
    public Long createTenantInfo(TenantCommandInput input) {
        log.info("应用层接收到新建租户请求：input:{}", JSON.toJSONString(input));
        // 主体信息
        final Long tenantId = tenantDomainService.insertTenantMainInfo(input);
        // 区域组织
        regionalOrganizationDomainService.create(input);
        // 门店信息
        merchantStoreDomainService.insertDefaultGroup(input);
        // 工商信息
        businessInformationCommandService.insert(TenantCommandInput.converterToBusinessInformationCommandInput(input));

        return tenantId;
    }

    @Override
    public void update(TenantCommandInput input) {
        tenantDomainService.update(input);
    }

    @Override
    public void batchUpdateOp(List<Long> ids, String opUname, Long opUid) {
        tenantDomainService.batchUpdateOp(ids, opUname, opUid);
    }
}
