package net.xianmu.usercenter.application.serviceImpl.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreAccountDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreAccountConverter {


    private MerchantStoreAccountConverter() {
        // 无需实现
    }



    public static List<MerchantStoreAccountDTO> toMerchantStoreAccountDTOList(List<MerchantStoreAccountEntity> merchantStoreAccountEntityList) {
        if (merchantStoreAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountDTO> merchantStoreAccountDTOList = new ArrayList<>();
        for (MerchantStoreAccountEntity merchantStoreAccountEntity : merchantStoreAccountEntityList) {
            merchantStoreAccountDTOList.add(toMerchantStoreAccountDTO(merchantStoreAccountEntity));
        }
        return merchantStoreAccountDTOList;
    }

    public static MerchantStoreAccountDTO toMerchantStoreAccountDTO(MerchantStoreAccountEntity merchantStoreAccountEntity) {
        if (merchantStoreAccountEntity == null) {
            return null;
        }
        MerchantStoreAccountDTO merchantStoreAccountDTO = new MerchantStoreAccountDTO();
        merchantStoreAccountDTO.setId(merchantStoreAccountEntity.getId());
        merchantStoreAccountDTO.setTenantId(merchantStoreAccountEntity.getTenantId());
        merchantStoreAccountDTO.setStoreId(merchantStoreAccountEntity.getStoreId());
        merchantStoreAccountDTO.setAccountName(merchantStoreAccountEntity.getAccountName());
        merchantStoreAccountDTO.setPhone(merchantStoreAccountEntity.getPhone());
        merchantStoreAccountDTO.setType(merchantStoreAccountEntity.getType());
        merchantStoreAccountDTO.setRegisterTime(merchantStoreAccountEntity.getRegisterTime());
        merchantStoreAccountDTO.setAuditTime(merchantStoreAccountEntity.getAuditTime());
        merchantStoreAccountDTO.setOpenId(merchantStoreAccountEntity.getOpenId());
        merchantStoreAccountDTO.setUnionId(merchantStoreAccountEntity.getUnionId());
        merchantStoreAccountDTO.setCreateTime(merchantStoreAccountEntity.getCreateTime());
        merchantStoreAccountDTO.setUpdateTime(merchantStoreAccountEntity.getUpdateTime());
        merchantStoreAccountDTO.setStatus(merchantStoreAccountEntity.getStatus());
        merchantStoreAccountDTO.setLastLoginTime(merchantStoreAccountEntity.getLastLoginTime());
        merchantStoreAccountDTO.setDeleteFlag(merchantStoreAccountEntity.getDeleteFlag());
        merchantStoreAccountDTO.setOaOpenId(merchantStoreAccountEntity.getOaOpenId());
        merchantStoreAccountDTO.setXmAccountId(merchantStoreAccountEntity.getXmAccountId());
        return merchantStoreAccountDTO;
    }
}
