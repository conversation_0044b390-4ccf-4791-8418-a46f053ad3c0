package net.xianmu.usercenter.application.serviceImpl.regional;

import net.xianmu.usercenter.api.regional.service.RegionalOrganizationAccountQueryService;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationAccountQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:46:41
* @version 1.0
*
*/
@Service
public class RegionalOrganizationAccountQueryServiceImpl implements RegionalOrganizationAccountQueryService {

    @Autowired
    private RegionalOrganizationAccountQueryRepository regionalOrganizationAccountQueryRepository;

}