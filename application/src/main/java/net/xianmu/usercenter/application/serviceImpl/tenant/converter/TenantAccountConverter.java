package net.xianmu.usercenter.application.serviceImpl.tenant.converter;

import net.xianmu.usercenter.api.tenant.dto.TenantAccountDTO;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 14:25
 */
public class TenantAccountConverter {


    private TenantAccountConverter() {
        // 无需实现
    }


    public static TenantAccountDTO toTenantAccountDTO(TenantAccountEntity tenantAccountEntity) {
        if (tenantAccountEntity == null) {
            return null;
        }
        TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
        tenantAccountDTO.setId(tenantAccountEntity.getId());
        tenantAccountDTO.setAuthUserId(tenantAccountEntity.getAuthUserId());
        tenantAccountDTO.setTenantId(tenantAccountEntity.getTenantId());
        tenantAccountDTO.setPhone(tenantAccountEntity.getPhone());
        tenantAccountDTO.setNickname(tenantAccountEntity.getNickname());
        tenantAccountDTO.setProfilePicture(tenantAccountEntity.getProfilePicture());
        tenantAccountDTO.setStatus(tenantAccountEntity.getStatus());
        tenantAccountDTO.setOperatorTime(tenantAccountEntity.getOperatorTime());
        tenantAccountDTO.setOperatorPhone(tenantAccountEntity.getOperatorPhone());
        tenantAccountDTO.setUpdateTime(tenantAccountEntity.getUpdateTime());
        tenantAccountDTO.setCreateTime(tenantAccountEntity.getCreateTime());
        tenantAccountDTO.setDeletedFlag(tenantAccountEntity.getDeletedFlag());
        tenantAccountDTO.setEmail(tenantAccountEntity.getEmail());
        tenantAccountDTO.setUpdater(tenantAccountEntity.getUpdater());
        return tenantAccountDTO;
    }


    public static List<TenantAccountDTO> toTenantAccountDTOList(List<TenantAccountEntity> tenantAccountEntityList) {
        if (tenantAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<TenantAccountDTO> tenantAccountDTOList = new ArrayList<>();
        for (TenantAccountEntity tenantAccountEntity : tenantAccountEntityList) {
            tenantAccountDTOList.add(toTenantAccountDTO(tenantAccountEntity));
        }
        return tenantAccountDTOList;
    }
}
