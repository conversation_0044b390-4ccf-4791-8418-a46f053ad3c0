package net.xianmu.usercenter.application.serviceImpl.regional;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.regional.service.RegionalOrganizationCommandService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-06 16:46:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class RegionalOrganizationCommandServiceImpl implements RegionalOrganizationCommandService {




}