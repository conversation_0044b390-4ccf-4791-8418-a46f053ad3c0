package net.xianmu.usercenter.application.serviceImpl.regional;

import net.xianmu.usercenter.api.regional.service.RegionalOrganizationAccountCommandService;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationAccountQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:46:41
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class RegionalOrganizationAccountCommandServiceImpl implements RegionalOrganizationAccountCommandService {

    @Autowired
    private RegionalOrganizationAccountQueryRepository regionalOrganizationAccountQueryRepository;
}