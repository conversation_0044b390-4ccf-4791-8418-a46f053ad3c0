package net.xianmu.usercenter.application.serviceImpl.tenant.converter;

import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.dto.TenantDTO;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-05 11:32:09
 * @version 1.0
 *
 */
public class TenantConverter {


    private TenantConverter() {
        // 无需实现
    }

    public static List<TenantDTO> toTenantDTOList(List<TenantEntity> tenantEntityList) {
        if (tenantEntityList == null) {
            return Collections.emptyList();
        }
        List<TenantDTO> tenantDTOList = new ArrayList<>();
        for (TenantEntity tenantEntity : tenantEntityList) {
            tenantDTOList.add(toTenantDTO(tenantEntity));
        }
        return tenantDTOList;
    }

    public static TenantDTO toTenantDTO(TenantEntity tenantEntity) {
        if (tenantEntity == null) {
            return null;
        }
        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setId(tenantEntity.getId());
        tenantDTO.setPhone(tenantEntity.getPhone());
        tenantDTO.setPassword(tenantEntity.getPassword());
        tenantDTO.setTenantName(tenantEntity.getTenantName());
        tenantDTO.setType(tenantEntity.getType());
        tenantDTO.setStatus(tenantEntity.getStatus());
        tenantDTO.setCreateTime(tenantEntity.getCreateTime());
        tenantDTO.setUpdateTime(tenantEntity.getUpdateTime());
        tenantDTO.setAdminId(tenantEntity.getAdminId());
        tenantDTO.setOpUid(tenantEntity.getOpUid());
        tenantDTO.setOpUname(tenantEntity.getOpUname());
        tenantDTO.setOperator(tenantEntity.getOperator());
        tenantDTO.setBelongDB(tenantEntity.getBelongDB());
        tenantDTO.setProfitSharingSwitch(tenantEntity.getProfitSharingSwitch());
        tenantDTO.setOnlinePayChannel(tenantEntity.getOnlinePayChannel());
        tenantDTO.setEmail(tenantEntity.getEmail());
        tenantDTO.setAccountLoginType(tenantEntity.getAccountLoginType());
        return tenantDTO;
    }



    public static List<TenantAndBusinessDTO> toTenantAndBusinessDTOList(List<TenantAndBusinessEntity> tenantAndBusinessEntityList) {
        if (tenantAndBusinessEntityList == null) {
            return Collections.emptyList();
        }
        List<TenantAndBusinessDTO> tenantAndBusinessDTOList = new ArrayList<>();
        for (TenantAndBusinessEntity tenantAndBusinessEntity : tenantAndBusinessEntityList) {
            tenantAndBusinessDTOList.add(toTenantAndBusinessDTO(tenantAndBusinessEntity));
        }
        return tenantAndBusinessDTOList;
    }

    public static TenantAndBusinessDTO toTenantAndBusinessDTO(TenantAndBusinessEntity tenantAndBusinessEntity) {
        if (tenantAndBusinessEntity == null) {
            return null;
        }
        TenantAndBusinessDTO tenantAndBusinessDTO = new TenantAndBusinessDTO();
        tenantAndBusinessDTO.setTenantId(tenantAndBusinessEntity.getTenantId());
        tenantAndBusinessDTO.setTenantName(tenantAndBusinessEntity.getTenantName());
        tenantAndBusinessDTO.setPhone(tenantAndBusinessEntity.getPhone());
        tenantAndBusinessDTO.setCompanyName(tenantAndBusinessEntity.getCompanyName());
        tenantAndBusinessDTO.setCreditCode(tenantAndBusinessEntity.getCreditCode());
        tenantAndBusinessDTO.setTenantCompanyId(tenantAndBusinessEntity.getTenantCompanyId());
        tenantAndBusinessDTO.setAdminId(tenantAndBusinessEntity.getAdminId());
        tenantAndBusinessDTO.setCreateTime(tenantAndBusinessEntity.getCreateTime());
        tenantAndBusinessDTO.setUpdateTime(tenantAndBusinessEntity.getUpdateTime());
        tenantAndBusinessDTO.setOpUname(tenantAndBusinessEntity.getOpUname());
        tenantAndBusinessDTO.setContactName(tenantAndBusinessEntity.getContactName());
        return tenantAndBusinessDTO;
    }
}
