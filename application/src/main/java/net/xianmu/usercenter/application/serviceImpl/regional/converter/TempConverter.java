package net.xianmu.usercenter.application.serviceImpl.regional.converter;

import net.xianmu.usercenter.common.input.command.RegionalOrganizationCommandInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/7 13:58
 */
public class TempConverter {


    private TempConverter() {
        // 无需实现
    }

    public static List<RegionalOrganizationCommandInput> toRegionalOrganizationCommandInputList(List<RegionalOrganizationEntity> regionalOrganizationEntityList) {
        if (regionalOrganizationEntityList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganizationCommandInput> regionalOrganizationCommandInputList = new ArrayList<>();
        for (RegionalOrganizationEntity regionalOrganizationEntity : regionalOrganizationEntityList) {
            regionalOrganizationCommandInputList.add(toRegionalOrganizationCommandInput(regionalOrganizationEntity));
        }
        return regionalOrganizationCommandInputList;
    }

    public static RegionalOrganizationCommandInput toRegionalOrganizationCommandInput(RegionalOrganizationEntity regionalOrganizationEntity) {
        if (regionalOrganizationEntity == null) {
            return null;
        }
        RegionalOrganizationCommandInput regionalOrganizationCommandInput = new RegionalOrganizationCommandInput();
        regionalOrganizationCommandInput.setId(regionalOrganizationEntity.getId());
        regionalOrganizationCommandInput.setTenantId(regionalOrganizationEntity.getTenantId());
        regionalOrganizationCommandInput.setPhone(regionalOrganizationEntity.getPhone());
        regionalOrganizationCommandInput.setOrganizationName(regionalOrganizationEntity.getOrganizationName());
        regionalOrganizationCommandInput.setSource(regionalOrganizationEntity.getSource());
        regionalOrganizationCommandInput.setSize(regionalOrganizationEntity.getSize());
        regionalOrganizationCommandInput.setStatus(regionalOrganizationEntity.getStatus());
        regionalOrganizationCommandInput.setAdminId(regionalOrganizationEntity.getAdminId());
        regionalOrganizationCommandInput.setCreateTime(regionalOrganizationEntity.getCreateTime());
        regionalOrganizationCommandInput.setUpdateTime(regionalOrganizationEntity.getUpdateTime());
        regionalOrganizationCommandInput.setCreator(regionalOrganizationEntity.getCreator());
        regionalOrganizationCommandInput.setUpdater(regionalOrganizationEntity.getUpdater());
        return regionalOrganizationCommandInput;
    }

    public static List<RegionalOrganizationEntity> toRegionalOrganizationEntityList(List<RegionalOrganizationCommandInput> regionalOrganizationCommandInputList) {
        if (regionalOrganizationCommandInputList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganizationEntity> regionalOrganizationEntityList = new ArrayList<>();
        for (RegionalOrganizationCommandInput regionalOrganizationCommandInput : regionalOrganizationCommandInputList) {
            regionalOrganizationEntityList.add(toRegionalOrganizationEntity(regionalOrganizationCommandInput));
        }
        return regionalOrganizationEntityList;
    }

    public static RegionalOrganizationEntity toRegionalOrganizationEntity(RegionalOrganizationCommandInput regionalOrganizationCommandInput) {
        if (regionalOrganizationCommandInput == null) {
            return null;
        }
        RegionalOrganizationEntity regionalOrganizationEntity = new RegionalOrganizationEntity();
        regionalOrganizationEntity.setId(regionalOrganizationCommandInput.getId());
        regionalOrganizationEntity.setTenantId(regionalOrganizationCommandInput.getTenantId());
        regionalOrganizationEntity.setPhone(regionalOrganizationCommandInput.getPhone());
        regionalOrganizationEntity.setOrganizationName(regionalOrganizationCommandInput.getOrganizationName());
        regionalOrganizationEntity.setSource(regionalOrganizationCommandInput.getSource());
        regionalOrganizationEntity.setSize(regionalOrganizationCommandInput.getSize());
        regionalOrganizationEntity.setStatus(regionalOrganizationCommandInput.getStatus());
        regionalOrganizationEntity.setAdminId(regionalOrganizationCommandInput.getAdminId());
        regionalOrganizationEntity.setCreateTime(regionalOrganizationCommandInput.getCreateTime());
        regionalOrganizationEntity.setUpdateTime(regionalOrganizationCommandInput.getUpdateTime());
        regionalOrganizationEntity.setCreator(regionalOrganizationCommandInput.getCreator());
        regionalOrganizationEntity.setUpdater(regionalOrganizationCommandInput.getUpdater());
        return regionalOrganizationEntity;
    }
}
