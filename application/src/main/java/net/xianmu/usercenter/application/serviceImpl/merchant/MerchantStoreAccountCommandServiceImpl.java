package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreAccountCommandService;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreAccountDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:58
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MerchantStoreAccountCommandServiceImpl implements MerchantStoreAccountCommandService {


    @Autowired
    private MerchantStoreAccountDomainService merchantStoreAccountDomainService;
    @Autowired
    private MerchantStoreAccountCommandRepository merchantStoreAccountCommandRepository;


    @Override
    public Long create(MerchantStoreAccountCommandInput input) {
        MerchantStoreAccountEntity entity = merchantStoreAccountDomainService.createAccount(input);
        return entity.getId();
    }

    @Override
    public Boolean update(MerchantStoreAccountCommandInput input) {
        return merchantStoreAccountDomainService.updateAccount(input);
    }

    @Override
    public Boolean remove(MerchantStoreAccountCommandInput input) {
        return merchantStoreAccountDomainService.deleteAccount(input);
    }

    @Override
    public Boolean removeBatch(MerchantStoreAccountCommandInput input) {
        List<Long> idList = input.getIdList();
        if (CollUtil.isEmpty(idList)) {
            return true;
        }
        idList.forEach(id -> remove(MerchantStoreAccountCommandInput.builder().id(id).build()));
        return true;
    }

    @Override
    public Boolean updateStatusBatch(List<Long> idList, Integer status) {
        return merchantStoreAccountCommandRepository.updateStatusBatch(idList, status);
    }


}
