package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantAddressQueryService;
import net.xianmu.usercenter.common.config.NacosPropertiesHolder;
import net.xianmu.usercenter.common.constants.AppConsts;
import net.xianmu.usercenter.common.enums.MerchantAddressEnums;
import net.xianmu.usercenter.common.enums.SortTypeEnum;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */
@Service
@Slf4j
public class MerchantAddressQueryServiceImpl implements MerchantAddressQueryService {


    @Autowired
    private MerchantAddressQueryRepository merchantAddressQueryRepository;
    @Autowired
    private MerchantContactQueryRepository merchantContactQueryRepository;

    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Override
    public List<MerchantAddressEntity> getMerchantAddressList(MerchantAddressQueryInput req) {
        return merchantAddressQueryRepository.selectByCondition(req);
    }

    @Override
    public List<MerchantAddressEntity> getAddressAndContacts(MerchantAddressQueryInput req) {
        ValidateUtil.paramValidate(req, "tenantId");
        // 必要参数校验，避免不传参数导致影响服务
        boolean paramExist = ValidateUtil.keyParamExist(req, "id", "storeId", "storeIdList", "xmContactId", "mId", "xmContactIdList", "city");
        if (!paramExist) {
            // 应cr建议，这边保持和查数据库一样的风格，不抛出异常，返回空数据列表业务自行处理
            log.warn("请求核心参数缺失!");
            return Collections.emptyList();
        }


        // 数据量限制
        int pageIndex = req.getPageIndex() == null ? 1 : req.getPageIndex();
        int pageSize = req.getPageSize() == null ? nacosPropertiesHolder.getDefaultQuerySize() : req.getPageSize();
        pageSize = pageSize > nacosPropertiesHolder.getMaxQuerySize() ? nacosPropertiesHolder.getMaxQuerySize() : pageSize;
        PageHelper.startPage(pageIndex, pageSize, false);

        // 地址
        List<MerchantAddressEntity> addressEntities = merchantAddressQueryRepository.selectByCondition(req);
        List<Long> addressList = addressEntities.stream().map(MerchantAddressEntity::getId).collect(Collectors.toList());

        // 联系人
        if (CollUtil.isNotEmpty(addressList)) {
            List<MerchantContactEntity> merchantContactEntities = merchantContactQueryRepository.selectByCondition(MerchantContactQueryInput.builder().addressIdList(addressList).tenantId(req.getTenantId()).build());
            MerchantAddressEntity.warpMerchantAddressEntity(addressEntities, merchantContactEntities);
        }
        return addressEntities;
    }


    @Override
    public List<String> getConcatAddress(Long tenantId) {
        if (null == tenantId) {
            log.warn("租户id为空！");
            return Collections.emptyList();
        }
        return merchantAddressQueryRepository.selectConcatAddressByTenantIdAndStatus(tenantId, MerchantAddressEnums.status.NORMAL.getCode());
    }

}