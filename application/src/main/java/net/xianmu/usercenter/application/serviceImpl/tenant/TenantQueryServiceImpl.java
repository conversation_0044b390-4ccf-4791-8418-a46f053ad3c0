package net.xianmu.usercenter.application.serviceImpl.tenant;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.dto.TenantDTO;
import net.xianmu.usercenter.api.tenant.service.TenantQueryService;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.tenant.domain.TenantDomainService;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xianmu.usercenter.application.serviceImpl.tenant.converter.TenantConverter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:14
 */
@Service
@Slf4j
public class TenantQueryServiceImpl implements TenantQueryService {

    @Autowired
    private TenantDomainService tenantDomainService;
    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;


    @Override
    public TenantDTO getTenantById(Long id) {
        return TenantConverter.toTenantDTO(tenantDomainService.getTenantById(id));
    }

    @Override
    public List<TenantDTO> getTenantsByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return TenantConverter.toTenantDTOList(tenantDomainService.getTenantsByIds(idList));
    }

    @Override
    public PageInfo<TenantAndBusinessEntity> getTenantsPage(TenantQueryInput req, PageQueryInput pageQueryInput) {
        return tenantDomainService.getTenantsPage(req, pageQueryInput);
    }

    @Override
    public List<TenantDTO> getTenants(TenantQueryInput req) {
        return TenantConverter.toTenantDTOList(tenantDomainService.getTenants(req));
    }

    @Override
    public List<TenantAndBusinessDTO> getTenantAndCompanyList(TenantQueryInput req) {
        return TenantConverter.toTenantAndBusinessDTOList(tenantDomainService.getTenantAndCompanyList(req));
    }

    @Override
    public TenantAndBusinessDTO getTenantAndCompany(Long tenantId) {
        final List<TenantAndBusinessEntity> entityList = tenantDomainService.getTenantAndCompanyByIds(Collections.singletonList(tenantId));
        return CollUtil.isEmpty(entityList) ? null : TenantConverter.toTenantAndBusinessDTO(entityList.get(0));
    }

    @Override
    public List<TenantAndBusinessDTO> getTenantAndCompanyByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return TenantConverter.toTenantAndBusinessDTOList(tenantDomainService.getTenantAndCompanyByIds(idList));
    }

    @Override
    public Integer getStoreCount(MerchantStoreQueryInput req) {
        ValidateUtil.paramValidate(req);
        Long tenantId = req.getTenantId();
        if (null == tenantId) {
            log.warn("租户id为空！");
            return 0;
        }
        return merchantStoreQueryRepository.countStoreNum(req);
    }
}
