package net.xianmu.usercenter.application.datatransfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.config.MqBizException;
import net.xianmu.usercenter.common.constants.RedisConstant;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmPair;
import net.xianmu.usercenter.common.enums.DtsModelTypeEnum;
import net.xianmu.usercenter.common.util.MapUtil;
import net.xianmu.usercenter.common.util.RedisLockUtil;
import net.xianmu.usercenter.common.util.SpringContextHolder;
import net.xianmu.usercenter.domain.merchant.domain.MerchantAddressDomainService;
import net.xianmu.usercenter.domain.merchant.domain.MerchantContactDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/12 11:06
 */

@Service
@Slf4j
public class MerchantAddressTransServiceImpl extends AbstractDbTableDml {

    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Autowired
    private MerchantAddressCommandRepository merchantAddressCommandRepository;
    @Autowired
    private MerchantAddressQueryRepository merchantAddressQueryRepository;
    @Autowired
    private MerchantContactQueryRepository merchantContactQueryRepository;
    @Autowired
    private MerchantAddressDomainService merchantAddressDomainService;
    @Autowired
    private MerchantContactDomainService merchantContactDomainService;
    @Autowired
    private DataTransferRepository dataTransferRepository;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        String type = dtsModel.getType();
        Map<String, String> data = pair.getKey();
        Map<String, String> old = pair.getValue();
        MerchantAddressTransServiceImpl merchantAddressTransService = SpringContextHolder.getBean(MerchantAddressTransServiceImpl.class);


        if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            log.warn("联系人-地址表binlog物理删除！");
            String id = old.get("contact_id");
            MerchantAddressEntity entity = merchantAddressQueryRepository.selectByXmContactId(Long.valueOf(id));
            if (null != entity) {
                log.info("待删除数据，地址Id:{}", entity.getId());
                merchantAddressTransService.deleteForBinLog(entity.getId());
            }

            //删除地址（这个地址是默认地址情况下） -- Redis中的清除缓存
            String isDefault = data.get("is_default");
            String mId = data.get("m_id");
            if (Objects.equals(isDefault, "1")){
                log.info("删除缓存数据，地址Id:{}", data.get("contact_id"));
                merchantContactDomainService.deleteRedisCacheAddress(Long.parseLong(mId));
            }
            return;
        }

        String contactIdStr = data.get("contact_id");
        if (StrUtil.isBlank(contactIdStr)) {
            log.error("数据异常，联系人id为空!");
            return;
        }
        Long contactId = Long.valueOf(contactIdStr);


        // 处理核心业务逻辑
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            merchantAddressTransService.createContactForBinLog(data);
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            //假如默认地址被修改则需要删除缓存
            String oldIsDefault = old.get("is_default");
            String isDefault = data.get("is_default");
            String mId = data.get("m_id");
            if (!StringUtils.isEmpty(oldIsDefault) && !Objects.equals(isDefault, oldIsDefault)) {
                log.info("删除缓存数据，地址Id:{}", data.get("contact_id"));
                merchantContactDomainService.deleteRedisCacheAddress(Long.parseLong(mId));
            }

            //物理删除默认地址需要删除缓存
            String oldStatus = old.get("status");
            String status = data.get("status");
            if (!StringUtils.isEmpty(oldStatus) && !Objects.equals(status, oldStatus) && Objects.equals(isDefault, "1")) {
                log.info("删除缓存数据，地址Id:{}", data.get("contact_id"));
                merchantContactDomainService.deleteRedisCacheAddress(Long.parseLong(mId));
            }

            // 先查映射，如果没有，那就走新建流程
            MerchantAddressEntity addressEntity = merchantAddressQueryRepository.selectByXmContactId(contactId);
            if (null == addressEntity) {
                merchantAddressTransService.createContactForBinLog(data);
                return;
            }
            // 更新操作
            merchantAddressTransService.updateContactForBinLog(data, addressEntity);
        } else {
            // 删除操作
            log.error("binlog物理删除！");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateContactForBinLog(Map<String, String> data, MerchantAddressEntity oldAddress) {
        // 获取最新门店id
        Long storeId = this.getStoreId(oldAddress.getMId(), data);

        // 更新地址
        MerchantAddressEntity updateAddress = MerchantAddressEntity.converterToEntityForBinLog(data);
        updateAddress.setId(oldAddress.getId());
        updateAddress.setStoreId(storeId);
        merchantAddressDomainService.createOrUpdateFotBinLog(updateAddress);
        // 更新联系人
        MerchantContactEntity oldContact = merchantContactQueryRepository.selectDefaultContact(TenantDefaultConstant.XIAN_MU_TENANT_ID, oldAddress.getId());
        MerchantContactEntity updateContact = MerchantContactEntity.converterToEntityForBinLog(data);
        updateContact.setId(oldContact.getId());
        updateContact.setAddressId(oldAddress.getId());
        merchantContactDomainService.createOrUpdateFotBinLog(updateContact);

    }

    public void createContactForBinLog(Map<String, String> data) {
        // 幂等，如果已经存在了那就无需再次创建了
        MerchantAddressEntity entity = merchantAddressQueryRepository.selectByXmContactId(Long.valueOf(data.get("contact_id")));
        if (null != entity) {
            log.info("该地址-联系人已存在，无需重复创建");
            return;
        }

        // 判断门店是否存在，不存在就后面再处理
        String mIdStr = data.get("m_id");
        if (StrUtil.isBlank(mIdStr)) {
            log.error("数据异常，该联系人无关联的门店");
            throw new BizException("数据异常，该联系人无关联的门店");
        }

        String lockKey = RedisConstant.SYSTEM_PREFIX + RedisConstant.MERCHANT_BINLOG + mIdStr;
        try {
            boolean success = RedisLockUtil.tryLock(lockKey, 2, 30);
            if (!success) {
                log.warn("门店mid:{}获取锁失败，自动开始重试。", mIdStr);
                throw new BizException("当前门店基础、账户数据正在同步");
            }
            log.info("加锁成功，开始初始化门店地址数据, key: {}, time: {}", lockKey, System.currentTimeMillis());

            MerchantAddressTransServiceImpl merchantAddressTransService = SpringContextHolder.getBean(MerchantAddressTransServiceImpl.class);
            merchantAddressTransService.createContactForBinLogInner(data);
        } catch (Exception e) {
            throw e;
        } finally {
            RedisLockUtil.unlockIfHeldByCurrentThread(lockKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void createContactForBinLogInner(Map<String, String> data) {
        String mIdStr = data.get("m_id");
        Long storeId = this.getStoreId(Long.valueOf(mIdStr), data);

        // 创建地址
        MerchantAddressEntity addressEntity = MerchantAddressEntity.converterToEntityForBinLog(data);
        addressEntity.setStoreId(storeId);
        addressEntity = merchantAddressDomainService.createOrUpdateFotBinLog(addressEntity);

        // 创建联系人
        MerchantContactEntity contactEntity = MerchantContactEntity.converterToEntityForBinLog(data);
        contactEntity.setAddressId(addressEntity.getId());
        merchantContactDomainService.createOrUpdateFotBinLog(contactEntity);
    }


    private Long getStoreId(Long mid, Map<String, String> data) {
        MerchantStoreEntity merchantStoreEntity = merchantStoreQueryRepository.selectByMId(mid, TenantDefaultConstant.XIAN_MU_TENANT_ID);
        if (null == merchantStoreEntity) {
            log.warn("当前联系人:{}所属的门店数据还未同步, mId:{}", data.get("contact_id"), mid);
            return null;
        }
        return merchantStoreEntity.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteForBinLog(Long id) {
        merchantAddressCommandRepository.delete(id);
    }


    @Override
    public List<Map<String, Object>> selectDataList(Long contactId, Integer offset) {
        return dataTransferRepository.selectContactList(contactId, offset);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doTransfer(Map<String, Object> data, boolean isCreate) {
        if (CollUtil.isEmpty(data)) {
            return;
        }
        MerchantAddressTransServiceImpl merchantAddressTransService = SpringContextHolder.getBean(MerchantAddressTransServiceImpl.class);
        Map<String, String> stringMap = MapUtil.convertToStringMap(data);

        if (isCreate) {
            merchantAddressTransService.createContactForBinLog(stringMap);
        } else {
            Long contactId = Long.valueOf(stringMap.get("contact_id"));
            // 先查映射，如果没有，那就走新建流程
            MerchantAddressEntity addressEntity = merchantAddressQueryRepository.selectByXmContactId(contactId);
            if (null == addressEntity) {
                merchantAddressTransService.createContactForBinLog(stringMap);
            } else {
                // 更新操作
                this.updateContactForBinLog(stringMap, addressEntity);
            }
        }
    }


    public void refreshAddressData(List<Long> idList) {
        List<Map<String, Object>> maps = dataTransferRepository.selectContactListListByIdList(idList);
        if (CollUtil.isEmpty(maps)) {
            log.warn("联系人地址不存在！");
            return;
        }
        MerchantAddressTransServiceImpl bean = SpringContextHolder.getBean(MerchantAddressTransServiceImpl.class);
        maps.forEach(map -> bean.doTransfer(map, false));
    }
}
