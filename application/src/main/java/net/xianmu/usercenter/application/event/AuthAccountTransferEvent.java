package net.xianmu.usercenter.application.event;

import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/11/23 16:21
 */
@Getter
public class AuthAccountTransferEvent extends ApplicationEvent {

    // 操作类型
    String type;
    // 原始数据
    private Map<String, String> oldMap;
    // 当前数据
    private Map<String, String> dataMap;

    public AuthAccountTransferEvent(Object source, String type, Map<String, String> oldMap, Map<String, String> dataMap) {
        super(source);
        this.type = type;
        this.oldMap = oldMap;
        this.dataMap = dataMap;
    }
}
