package net.xianmu.usercenter.application.serviceImpl.merchant;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreGroupDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreGroupQueryService;
import net.xianmu.usercenter.application.serviceImpl.merchant.converter.MerchantStoreGroupConverter;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.domain.MerchantStoreGroupDomainService;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupMappingQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:36
 */

@Service
public class MerchantStoreGroupQueryServiceImpl implements MerchantStoreGroupQueryService {

    @Autowired
    private MerchantStoreGroupDomainService merchantStoreGroupDomainService;

    @Autowired
    private MerchantStoreGroupMappingQueryRepository mappingQueryRepository;

    @Autowired
    private MerchantStoreGroupQueryRepository merchantStoreGroupQueryRepository;

    @Override
    public List<MerchantStoreGroupDTO> getGroupByStoreIds(Long tenantId, List<Long> storeIdList) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupDTOList(merchantStoreGroupQueryRepository.selectByStoreIds(tenantId, storeIdList));
    }


    @Override
    public List<MerchantStoreGroupDTO> getGroupByGroupIds(Long tenantId, List<Long> groupIdList) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupDTOList(merchantStoreGroupQueryRepository.selectGroupByGroupIds(tenantId, groupIdList));
    }

    @Override
    public List<MerchantStoreGroupEntity> getGroupsWithStoreCount(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput) {
        ValidateUtil.paramValidate(merchantStoreGroupQueryInput, "tenantId");
        Long tenantId = merchantStoreGroupQueryInput.getTenantId();
        String name = merchantStoreGroupQueryInput.getMerchantStoreGroupName();
        return merchantStoreGroupQueryRepository.queryGroupsWithStoreCount(tenantId, name);
    }

    @Override
    public List<MerchantStoreGroupDTO> getMerchantStoreGroups(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupDTOList(merchantStoreGroupQueryRepository.selectByCondition(merchantStoreGroupQueryInput));
    }

    @Override
    public PageInfo<MerchantStoreGroupDTO> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput, PageQueryInput pageQueryInput) {
        ValidateUtil.paramValidate(merchantStoreGroupQueryInput, "tenantId");
        final Long tenantId = merchantStoreGroupQueryInput.getTenantId();
        PageInfo<MerchantStoreGroupEntity> entityPage = merchantStoreGroupDomainService.getMerchantStoreGroupPage(merchantStoreGroupQueryInput, pageQueryInput);
        final PageInfo<MerchantStoreGroupDTO> dtoPage = PageInfoConverter.toPageResp(entityPage, MerchantStoreGroupConverter::toMerchantStoreGroupDTO);
        final List<MerchantStoreGroupDTO> list = dtoPage.getList();
        if (CollUtil.isEmpty(list)) {
            return dtoPage;
        }

        // 补充门店数,这里最好是批量查再汇总
        list.forEach(dto -> {
            dto.setStoreNum(mappingQueryRepository.countStoreNumByGroupId(dto.getMerchantStoreGroupId(), tenantId));
        });
        return dtoPage;
    }
}
