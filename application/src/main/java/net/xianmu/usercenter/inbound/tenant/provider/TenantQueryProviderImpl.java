package net.xianmu.usercenter.inbound.tenant.provider;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.service.TenantQueryService;
import net.xianmu.usercenter.application.serviceImpl.tenant.converter.v2.TenantConverterV2;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import net.xianmu.usercenter.common.config.NacosPropertiesHolder;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import net.xianmu.usercenter.inbound.page.PageConverter;
import net.xianmu.usercenter.inbound.tenant.converter.TenantConverter;
import net.xianmu.usercenter.inbound.tenant.converter.TenantReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:01
 */
@DubboService
@Slf4j
public class TenantQueryProviderImpl implements TenantQueryProvider {
    @Autowired
    private TenantQueryService tenantQueryService;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Override
    public DubboResponse<TenantResultResp> getTenantById(Long id) {
        return DubboResponse.getOK(TenantConverter.toTenantResultResp(tenantQueryService.getTenantById(id)));
    }

    @Override
    public DubboResponse<List<TenantResultResp>> getTenantsByIds(List<Long> idList) {
        return DubboResponse.getOK(TenantConverter.toTenantResultRespList(tenantQueryService.getTenantsByIds(idList)));
    }

    @Override
    public DubboResponse<PageInfo<TenantAndBusinessInfoResultResp>> getTenantsPage(TenantQueryReq tenantQueryReq, PageQueryReq pageQueryReq) {
        PageInfo<TenantAndBusinessEntity> tenantsPage = tenantQueryService.getTenantsPage(TenantReqConverter.toTenantQueryInput(tenantQueryReq), PageConverter.toPageQueryInput(pageQueryReq));
        PageInfo<TenantAndBusinessInfoResultResp> pageResp = PageInfoConverter.toPageResp(tenantsPage, TenantConverterV2::toTenantAndBusinessInfoResultResp);
        return DubboResponse.getOK(pageResp);
    }

    @Override
    public DubboResponse<List<TenantResultResp>> getTenants(TenantQueryReq tenantQueryReq) {
        TenantQueryInput tenantQueryInput = TenantReqConverter.toTenantQueryInput(tenantQueryReq);
        TenantQueryInput.paramValidate(tenantQueryInput, 200, nacosPropertiesHolder.isTenantQueryValidateSwitch());

        return DubboResponse.getOK(TenantConverter.toTenantResultRespList(tenantQueryService.getTenants(tenantQueryInput)));
    }

    @Override
    public DubboResponse<Integer> getStoreCount(MerchantStoreQueryReq merchantStoreQueryReq) {
        return DubboResponse.getOK(tenantQueryService.getStoreCount(MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq)));
    }


    @Override
    public DubboResponse<List<TenantAndBusinessInfoResultResp>> getTenantAndCompanyList(TenantQueryReq tenantQueryReq) {
        return DubboResponse.getOK(TenantConverter.toTenantAndBusinessInfoResultRespList(tenantQueryService.getTenantAndCompanyList(TenantReqConverter.toTenantQueryInput(tenantQueryReq))));
    }

    @Override
    public DubboResponse<TenantAndBusinessInfoResultResp> getTenantAndCompany(Long id) {
        return DubboResponse.getOK(TenantConverter.toTenantAndBusinessInfoResultResp(tenantQueryService.getTenantAndCompany(id)));
    }

    @Override
    public DubboResponse<List<TenantAndBusinessInfoResultResp>> getTenantAndCompanyByIds(List<Long> idList) {
        return DubboResponse.getOK(TenantConverter.toTenantAndBusinessInfoResultRespList(tenantQueryService.getTenantAndCompanyByIds(idList)));
    }
}
