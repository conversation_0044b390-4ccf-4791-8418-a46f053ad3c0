package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreAccountConverterV2 {


    private MerchantStoreAccountConverterV2() {
        // 无需实现
    }



    public static List<MerchantStoreAccountResultResp> toMerchantStoreAccountResultRespList(List<MerchantStoreAccountEntity> merchantStoreAccountEntityList) {
        if (merchantStoreAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultRespList = new ArrayList<>();
        for (MerchantStoreAccountEntity merchantStoreAccountEntity : merchantStoreAccountEntityList) {
            merchantStoreAccountResultRespList.add(toMerchantStoreAccountResultResp(merchantStoreAccountEntity));
        }
        return merchantStoreAccountResultRespList;
    }

    public static MerchantStoreAccountResultResp toMerchantStoreAccountResultResp(MerchantStoreAccountEntity merchantStoreAccountEntity) {
        if (merchantStoreAccountEntity == null) {
            return null;
        }
        MerchantStoreAccountResultResp merchantStoreAccountResultResp = new MerchantStoreAccountResultResp();
        merchantStoreAccountResultResp.setId(merchantStoreAccountEntity.getId());
        merchantStoreAccountResultResp.setTenantId(merchantStoreAccountEntity.getTenantId());
        merchantStoreAccountResultResp.setStoreId(merchantStoreAccountEntity.getStoreId());
        merchantStoreAccountResultResp.setAccountName(merchantStoreAccountEntity.getAccountName());
        merchantStoreAccountResultResp.setPhone(merchantStoreAccountEntity.getPhone());
        merchantStoreAccountResultResp.setType(merchantStoreAccountEntity.getType());
        merchantStoreAccountResultResp.setRegisterTime(merchantStoreAccountEntity.getRegisterTime());
        merchantStoreAccountResultResp.setAuditTime(merchantStoreAccountEntity.getAuditTime());
        merchantStoreAccountResultResp.setOpenId(merchantStoreAccountEntity.getOpenId());
        merchantStoreAccountResultResp.setUnionId(merchantStoreAccountEntity.getUnionId());
        merchantStoreAccountResultResp.setStatus(merchantStoreAccountEntity.getStatus());
        merchantStoreAccountResultResp.setCreateTime(merchantStoreAccountEntity.getCreateTime());
        merchantStoreAccountResultResp.setUpdateTime(merchantStoreAccountEntity.getUpdateTime());
        merchantStoreAccountResultResp.setDeleteFlag(merchantStoreAccountEntity.getDeleteFlag());
        merchantStoreAccountResultResp.setLastLoginTime(merchantStoreAccountEntity.getLastLoginTime());
        merchantStoreAccountResultResp.setOaOpenId(merchantStoreAccountEntity.getOaOpenId());
        merchantStoreAccountResultResp.setMId(merchantStoreAccountEntity.getMId());
        merchantStoreAccountResultResp.setXmAccountId(merchantStoreAccountEntity.getXmAccountId());
        merchantStoreAccountResultResp.setUsername(merchantStoreAccountEntity.getUsername());
        return merchantStoreAccountResultResp;
    }


    public static List<MerchantStoreAccountPageResp> toMerchantStoreAccountPageRespList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountPageResp> merchantStoreAccountPageRespList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreAccountPageRespList.add(toMerchantStoreAccountPageResp(merchantStoreEntity));
        }
        return merchantStoreAccountPageRespList;
    }

    public static MerchantStoreAccountPageResp toMerchantStoreAccountPageResp(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStoreAccountPageResp merchantStoreAccountPageResp = new MerchantStoreAccountPageResp();
        merchantStoreAccountPageResp.setId(merchantStoreEntity.getAccountId());
        merchantStoreAccountPageResp.setStoreId(merchantStoreEntity.getId());
        merchantStoreAccountPageResp.setTenantId(merchantStoreEntity.getTenantId());
        merchantStoreAccountPageResp.setAccountName(merchantStoreEntity.getAccountName());
        merchantStoreAccountPageResp.setPhone(merchantStoreEntity.getPhone());
        merchantStoreAccountPageResp.setType(merchantStoreEntity.getAccountType());
        merchantStoreAccountPageResp.setStatus(merchantStoreEntity.getAccountStatus());
        merchantStoreAccountPageResp.setStoreName(merchantStoreEntity.getStoreName());
        return merchantStoreAccountPageResp;
    }


}
