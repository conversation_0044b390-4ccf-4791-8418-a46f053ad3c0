package net.xianmu.usercenter.inbound.businessInfo.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.businessInfo.service.BusinessInformationCommandService;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationCommandProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationCommandReq;
import net.xianmu.usercenter.client.common.enums.SystemOriginEnum;
import net.xianmu.usercenter.inbound.businessInfo.converter.BusinessInformationReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:48
 */
@DubboService
public class BusinessInformationCommandProviderImpl implements BusinessInformationCommandProvider {

    @Autowired
    private BusinessInformationCommandService commandService;

    @Override
    public DubboResponse insert(SystemOriginEnum systemOriginEnum, BusinessInformationCommandReq req) {
        commandService.insert(BusinessInformationReqConverter.toBusinessInformationCommandInput(req));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse update(SystemOriginEnum systemOriginEnum, BusinessInformationCommandReq req) {
        commandService.update(BusinessInformationReqConverter.toBusinessInformationCommandInput(req));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse updateWithNull(SystemOriginEnum systemOriginEnum, BusinessInformationCommandReq req) {
        commandService.updateWithNull(BusinessInformationReqConverter.toBusinessInformationCommandInput(req));
        return DubboResponse.getOK();
    }
}
