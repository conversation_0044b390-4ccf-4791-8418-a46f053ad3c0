package net.xianmu.usercenter.inbound.regional.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.regional.service.RegionalOrganizationQueryService;
import net.xianmu.usercenter.client.regional.provider.RegionalOrganizationQueryProvider;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.inbound.regional.converter.RegionalOrganizationConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-08-02 17:54:02
 */
@DubboService
public class RegionalOrganizationQueryProviderImpl implements RegionalOrganizationQueryProvider {

    @Autowired
    private RegionalOrganizationQueryService regionalOrganizationQueryService;

    @Override
    public DubboResponse<RegionalOrganizationResultResp> saasGetRegionalByTenantId(Long tenantId) {
        RegionalOrganizationEntity regionalOrganization = regionalOrganizationQueryService.saasGetRegionalByTenantId(tenantId);
        return DubboResponse.getOK(RegionalOrganizationConverter.toRegionalOrganizationResultResp(regionalOrganization));
    }

    @Override
    public DubboResponse<List<RegionalOrganizationResultResp>> saasGetRegionalByTenantIds(List<Long> list) {
        List<RegionalOrganizationEntity> entities = regionalOrganizationQueryService.saasGetRegionalByTenantId(list);
        return DubboResponse.getOK(RegionalOrganizationConverter.toRegionalOrganizationResultRespList(entities));
    }
}
