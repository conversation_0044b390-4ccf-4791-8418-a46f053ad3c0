package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantContactCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantContactConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@DubboService
public class MerchantContactCommandProviderImpl implements MerchantContactCommandProvider {
    @Autowired
    private MerchantContactCommandService merchantContactCommandService;

    @Override
    public DubboResponse<Long> create(SystemOriginEnum systemOriginEnum, MerchantContactCommandReq merchantContactCommandReq) {
        Long id = merchantContactCommandService.create(MerchantContactConverter.toMerchantContactCommandInput(merchantContactCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<Boolean> update(SystemOriginEnum systemOriginEnum, MerchantContactCommandReq merchantContactCommandReq) {
        Boolean success = merchantContactCommandService.update(MerchantContactConverter.toMerchantContactCommandInput(merchantContactCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> remove(SystemOriginEnum systemOriginEnum, MerchantContactCommandReq merchantContactCommandReq) {
        Boolean success = merchantContactCommandService.remove(MerchantContactConverter.toMerchantContactCommandInput(merchantContactCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> removeBatch(SystemOriginEnum systemOriginEnum, MerchantContactCommandReq merchantContactCommandReq) {
        Boolean success = merchantContactCommandService.removeBatch(MerchantContactConverter.toMerchantContactCommandInput(merchantContactCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(success);
    }
}
