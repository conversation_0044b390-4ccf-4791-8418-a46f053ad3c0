package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantAddressCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantAddressConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */
@DubboService
public class MerchantAddressCommandProviderImpl implements MerchantAddressCommandProvider {
    @Autowired
    private MerchantAddressCommandService merchantAddressCommandService;

    @Override
    public DubboResponse<Long> create(SystemOriginEnum systemOriginEnum, MerchantAddressCommandReq merchantAddressCommandReq) {
        Long addressId = merchantAddressCommandService.create(MerchantAddressConverter.toMerchantAddressCommandInput(merchantAddressCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(addressId);
    }

    @Override
    public DubboResponse<Boolean> update(SystemOriginEnum systemOriginEnum, MerchantAddressCommandReq merchantAddressCommandReq) {
        Boolean success = merchantAddressCommandService.update(MerchantAddressConverter.toMerchantAddressCommandInput(merchantAddressCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> remove(SystemOriginEnum systemOriginEnum, MerchantAddressCommandReq merchantAddressCommandReq) {
        Boolean success = merchantAddressCommandService.remove(MerchantAddressConverter.toMerchantAddressCommandInput(merchantAddressCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }
}
