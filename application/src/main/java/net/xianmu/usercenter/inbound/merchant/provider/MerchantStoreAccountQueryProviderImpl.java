package net.xianmu.usercenter.inbound.merchant.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreAccountQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountPageResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreAccountConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreAccountReqConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreAccountConverterV2;
import net.xianmu.usercenter.inbound.page.PageConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:01
 */
@DubboService
@Slf4j
public class MerchantStoreAccountQueryProviderImpl implements MerchantStoreAccountQueryProvider {
    @Autowired
    private MerchantStoreAccountQueryService queryService;


    @Override
    public DubboResponse<MerchantStoreAccountResultResp> getMerchantStoreAccountById(Long id) {
        return DubboResponse.getOK(MerchantStoreAccountConverterV2.toMerchantStoreAccountResultResp(queryService.getMerchantStoreAccountById(id)));
    }

    @Override
    public DubboResponse<List<MerchantStoreAccountResultResp>> getMerchantStoreAccountByIds(List<Long> idList) {
        return DubboResponse.getOK(MerchantStoreAccountConverterV2.toMerchantStoreAccountResultRespList(queryService.getMerchantStoreAccountsByIds(idList)));
    }

    @Override
    public DubboResponse<List<MerchantStoreAccountResultResp>> getMerchantStoreAccountsByPrimaryKeys(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        Long storeId = merchantStoreAccountQueryReq.getStoreId();
        List<Long> storeIdList = merchantStoreAccountQueryReq.getStoreIdList();
        Long mId = merchantStoreAccountQueryReq.getMId();
        List<Long> mIds = merchantStoreAccountQueryReq.getMIdList();
        String phone = merchantStoreAccountQueryReq.getPhone();
        List<Long> xmAccountIdList = merchantStoreAccountQueryReq.getXmAccountIdList();
        if (null == storeId && null == mId && CollUtil.isEmpty(mIds) && CollUtil.isEmpty(storeIdList) && CollUtil.isEmpty(xmAccountIdList) && StrUtil.isBlank(phone)) {
            log.warn("必要参数为空!!");
            return DubboResponse.getOK(Collections.emptyList());
        }

        return DubboResponse.getOK(MerchantStoreAccountConverterV2.toMerchantStoreAccountResultRespList(queryService.getMerchantStoreAccounts(MerchantStoreAccountReqConverter.toMerchantStoreAccountQueryInput(merchantStoreAccountQueryReq))));
    }

    @Override
    public DubboResponse<List<MerchantStoreAccountResultResp>> getMerchantStoreAccounts(MerchantStoreAccountQueryReq req) {
        return DubboResponse.getOK(MerchantStoreAccountConverterV2.toMerchantStoreAccountResultRespList(queryService.getMerchantStoreAccounts(MerchantStoreAccountReqConverter.toMerchantStoreAccountQueryInput(req))));
    }

    @Override
    public DubboResponse<List<MerchantStoreAccountResultResp>> getMerchantStoreAccountsWithFuzzy(MerchantStoreAccountQueryReq req) {
        return DubboResponse.getOK(MerchantStoreAccountConverterV2.toMerchantStoreAccountResultRespList(queryService.getMerchantStoreAccountsWithFuzzy(MerchantStoreAccountReqConverter.toMerchantStoreAccountQueryInput(req))));
    }

    @Override
    public DubboResponse<PageInfo<MerchantStoreAccountPageResp>> getAccountPageByPhone(MerchantStoreAccountPageReq req, PageQueryReq page) {
        PageInfo<MerchantStoreEntity> pageInfo = queryService.getAccountPage(MerchantStoreAccountReqConverter.toMerchantStoreAccountQueryInput(req), PageConverter.toPageQueryInput(page));
        return DubboResponse.getOK(PageInfoConverter.toPageResp(pageInfo, MerchantStoreAccountConverterV2::toMerchantStoreAccountPageResp));
    }


}
