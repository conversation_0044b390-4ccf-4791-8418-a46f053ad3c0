package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreGroupCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupBatchImportReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupBatchImportResp;
import net.xianmu.usercenter.common.dto.MerchantStoreGroupBatchImportDTO;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreGroupConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-06-01 17:36:39
 */
@DubboService
public class MerchantStoreGroupCommandProviderImpl implements MerchantStoreGroupCommandProvider {
    @Autowired
    private MerchantStoreGroupCommandService merchantStoreGroupCommandService;

    @Override
    public DubboResponse<Long> create(SystemOriginEnum systemOriginEnum, MerchantStoreGroupCommandReq merchantStoreGroupCommandReq) {
        Long id = merchantStoreGroupCommandService.create(MerchantStoreGroupConverter.toMerchantStoreGroupCommandInput(merchantStoreGroupCommandReq));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<List<MerchantStoreGroupBatchImportResp>> batchCreate(SystemOriginEnum systemOriginEnum, List<MerchantStoreGroupBatchImportReq> list, Long tenantId) {
        List<MerchantStoreGroupBatchImportDTO> errorList = merchantStoreGroupCommandService.batchCreate(MerchantStoreGroupConverter.toMerchantStoreGroupBatchImportDTOList(list), tenantId);
        return DubboResponse.getOK(MerchantStoreGroupConverter.toMerchantStoreGroupBatchImportRespList(errorList));
    }

    @Override
    public DubboResponse<Boolean> updateMerchantGroupWithRemove(SystemOriginEnum systemOriginEnum, MerchantStoreGroupCommandReq merchantStoreGroupCommandReq) {
        Boolean success = merchantStoreGroupCommandService.update(MerchantStoreGroupConverter.toMerchantStoreGroupCommandInput(merchantStoreGroupCommandReq));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> remove(SystemOriginEnum systemOriginEnum, MerchantStoreGroupCommandReq merchantStoreGroupCommandReq) {
        Boolean success = merchantStoreGroupCommandService.remove(MerchantStoreGroupConverter.toMerchantStoreGroupCommandInput(merchantStoreGroupCommandReq));
        return DubboResponse.getOK(success);
    }
}
