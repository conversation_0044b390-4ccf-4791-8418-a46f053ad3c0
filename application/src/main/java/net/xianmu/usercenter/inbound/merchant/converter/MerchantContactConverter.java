package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
public class MerchantContactConverter {


    private MerchantContactConverter() {
        // 无需实现
    }


    /**
     * ******************************* input *************************
     */

    public static MerchantContactQueryInput toMerchantContactQueryInput(MerchantContactQueryReq merchantContactQueryReq) {
        if (merchantContactQueryReq == null) {
            return null;
        }
        MerchantContactQueryInput merchantContactQueryInput = new MerchantContactQueryInput();
        merchantContactQueryInput.setId(merchantContactQueryReq.getId());
        merchantContactQueryInput.setTenantId(merchantContactQueryReq.getTenantId());
        merchantContactQueryInput.setAddressId(merchantContactQueryReq.getAddressId());
        merchantContactQueryInput.setName(merchantContactQueryReq.getName());
        merchantContactQueryInput.setPhone(merchantContactQueryReq.getPhone());
        merchantContactQueryInput.setDefaultFlag(merchantContactQueryReq.getDefaultFlag());
        merchantContactQueryInput.setAddressIdList(merchantContactQueryReq.getAddressIdList());
        return merchantContactQueryInput;
    }


    public static MerchantContactCommandInput toMerchantContactCommandInput(MerchantContactCommandReq merchantContactCommandReq, Integer systemOrigin) {
        if (merchantContactCommandReq == null) {
            return null;
        }
        MerchantContactCommandInput merchantContactCommandInput = new MerchantContactCommandInput();
        merchantContactCommandInput.setId(merchantContactCommandReq.getId());
        merchantContactCommandInput.setIdList(merchantContactCommandReq.getIdList());
        merchantContactCommandInput.setTenantId(merchantContactCommandReq.getTenantId());
        merchantContactCommandInput.setAddressId(merchantContactCommandReq.getAddressId());
        merchantContactCommandInput.setName(merchantContactCommandReq.getName());
        merchantContactCommandInput.setPhone(merchantContactCommandReq.getPhone());
        merchantContactCommandInput.setDefaultFlag(merchantContactCommandReq.getDefaultFlag());
        merchantContactCommandInput.setCreateTime(merchantContactCommandReq.getCreateTime());
        merchantContactCommandInput.setUpdateTime(merchantContactCommandReq.getUpdateTime());
        merchantContactCommandInput.setSystemOrigin(systemOrigin);
        return merchantContactCommandInput;
    }

    public static List<MerchantContactCommandInput> toMerchantContactCommandInputList(List<MerchantContactCommandReq> merchantContactCommandReqList, Integer systemOrigin) {
        if (merchantContactCommandReqList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactCommandInput> merchantContactCommandInputList = new ArrayList<>();
        for (MerchantContactCommandReq merchantContactCommandReq : merchantContactCommandReqList) {
            merchantContactCommandInputList.add(toMerchantContactCommandInput(merchantContactCommandReq, systemOrigin));
        }
        return merchantContactCommandInputList;
    }



    /**
     * ******************************* resp *************************
     */

    public static List<MerchantContactResultResp> toMerchantContactResultRespList(List<MerchantContactDTO> merchantContactDTOList) {
        if (merchantContactDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactResultResp> merchantContactResultRespList = new ArrayList<>();
        for (MerchantContactDTO merchantContactDTO : merchantContactDTOList) {
            merchantContactResultRespList.add(toMerchantContactResultResp(merchantContactDTO));
        }
        return merchantContactResultRespList;
    }

    public static MerchantContactResultResp toMerchantContactResultResp(MerchantContactDTO merchantContactDTO) {
        if (merchantContactDTO == null) {
            return null;
        }
        MerchantContactResultResp merchantContactResultResp = new MerchantContactResultResp();
        merchantContactResultResp.setId(merchantContactDTO.getId());
        merchantContactResultResp.setTenantId(merchantContactDTO.getTenantId());
        merchantContactResultResp.setAddressId(merchantContactDTO.getAddressId());
        merchantContactResultResp.setName(merchantContactDTO.getName());
        merchantContactResultResp.setPhone(merchantContactDTO.getPhone());
        merchantContactResultResp.setDefaultFlag(merchantContactDTO.getDefaultFlag());
        merchantContactResultResp.setCreateTime(merchantContactDTO.getCreateTime());
        merchantContactResultResp.setUpdateTime(merchantContactDTO.getUpdateTime());
        return merchantContactResultResp;
    }

}
