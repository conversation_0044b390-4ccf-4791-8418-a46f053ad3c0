package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreBatchCreateProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.common.dto.MerchantStoreBatchImportDTO;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/26 11:28
 */
@DubboService
public class MerchantStoreBatchCreateProviderImpl implements MerchantStoreBatchCreateProvider {

    @Resource
    private MerchantStoreCommandService merchantStoreCommandService;

    @Override
    public DubboResponse<List<MerchantStoreBatchImportResp>> createMerchantStoreInfoBatch(Integer systemOriginEnum, List<MerchantStoreDomainCommandReq> list) {
        List<MerchantStoreBatchImportDTO> dtoList = merchantStoreCommandService.createMerchantStoreInfoBatch(MerchantStoreReqConverter.toMerchantStoreDomainCommandInputList(list, systemOriginEnum));
        return DubboResponse.getOK(MerchantStoreConverter.toMerchantStoreBatchImportRespList(dtoList));
    }
}
