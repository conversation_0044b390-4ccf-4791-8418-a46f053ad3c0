package net.xianmu.usercenter.inbound.page;


import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.common.input.query.PageQueryInput;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PageConverter {

    public static List<PageQueryReq> toPageQueryReqList(List<PageQueryInput> pageQueryInputList) {
        if (pageQueryInputList == null) {
            return Collections.emptyList();
        }
        List<PageQueryReq> pageQueryReqList = new ArrayList<>();
        for (PageQueryInput pageQueryInput : pageQueryInputList) {
            pageQueryReqList.add(toPageQueryReq(pageQueryInput));
        }
        return pageQueryReqList;
    }

    public static PageQueryReq toPageQueryReq(PageQueryInput pageQueryInput) {
        if (pageQueryInput == null) {
            return null;
        }
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageSize(pageQueryInput.getPageSize());
        pageQueryReq.setPageIndex(pageQueryInput.getPageIndex());
        return pageQueryReq;
    }

    public static List<PageQueryInput> toPageQueryInputList(List<PageQueryReq> pageQueryReqList) {
        if (pageQueryReqList == null) {
            return Collections.emptyList();
        }
        List<PageQueryInput> pageQueryInputList = new ArrayList<>();
        for (PageQueryReq pageQueryReq : pageQueryReqList) {
            pageQueryInputList.add(toPageQueryInput(pageQueryReq));
        }
        return pageQueryInputList;
    }

    public static PageQueryInput toPageQueryInput(PageQueryReq pageQueryReq) {
        if (pageQueryReq == null) {
            return null;
        }
        PageQueryInput pageQueryInput = new PageQueryInput();
        pageQueryInput.setPageSize(pageQueryReq.getPageSize());
        pageQueryInput.setPageIndex(pageQueryReq.getPageIndex());
        return pageQueryInput;
    }
}
