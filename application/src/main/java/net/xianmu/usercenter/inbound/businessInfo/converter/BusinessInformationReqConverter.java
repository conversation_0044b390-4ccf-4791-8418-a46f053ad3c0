package net.xianmu.usercenter.inbound.businessInfo.converter;

import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationCommandReq;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 17:41
 */
public class BusinessInformationReqConverter {


    private BusinessInformationReqConverter() {
        // 无需实现
    }


    public static BusinessInformationQueryInput toBusinessInformationQueryInput(BusinessInformationQueryReq businessInformationQueryReq) {
        if (businessInformationQueryReq == null) {
            return null;
        }
        BusinessInformationQueryInput businessInformationQueryInput = new BusinessInformationQueryInput();
        businessInformationQueryInput.setId(businessInformationQueryReq.getId());
        businessInformationQueryInput.setTenantId(businessInformationQueryReq.getTenantId());
        businessInformationQueryInput.setBizId(businessInformationQueryReq.getBizId());
        businessInformationQueryInput.setType(businessInformationQueryReq.getType());
        businessInformationQueryInput.setBizIdList(businessInformationQueryReq.getBizIdList());
        return businessInformationQueryInput;
    }


    public static BusinessInformationCommandInput toBusinessInformationCommandInput(BusinessInformationCommandReq businessInformationCommandReq) {
        if (businessInformationCommandReq == null) {
            return null;
        }
        BusinessInformationCommandInput businessInformationCommandInput = new BusinessInformationCommandInput();
        businessInformationCommandInput.setTenantId(businessInformationCommandReq.getTenantId());
        businessInformationCommandInput.setBizId(businessInformationCommandReq.getBizId());
        businessInformationCommandInput.setType(businessInformationCommandReq.getType());
        businessInformationCommandInput.setCompanyName(businessInformationCommandReq.getCompanyName());
        businessInformationCommandInput.setCreditCode(businessInformationCommandReq.getCreditCode());
        businessInformationCommandInput.setProvince(businessInformationCommandReq.getProvince());
        businessInformationCommandInput.setCity(businessInformationCommandReq.getCity());
        businessInformationCommandInput.setArea(businessInformationCommandReq.getArea());
        businessInformationCommandInput.setAddress(businessInformationCommandReq.getAddress());
        businessInformationCommandInput.setCompanyPhone(businessInformationCommandReq.getCompanyPhone());
        businessInformationCommandInput.setCompanyAreaPhone(businessInformationCommandReq.getCompanyAreaPhone());
        businessInformationCommandInput.setBusinessLicense(businessInformationCommandReq.getBusinessLicense());
        businessInformationCommandInput.setContactName(businessInformationCommandReq.getContactName());
        return businessInformationCommandInput;
    }
}
