package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.common.dto.MerchantStoreBatchImportDTO;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:48
 */
@DubboService
public class MerchantStoreCommandProviderImpl implements MerchantStoreCommandProvider {

    @Autowired
    private MerchantStoreCommandService merchantStoreCommandService;

    @Override
    public DubboResponse<Long> createMerchantStoreInfo(SystemOriginEnum systemOriginEnum, MerchantStoreDomainCommandReq merchantStoreDomainCommandReq) {
        Long id = merchantStoreCommandService.createMerchantStoreInfo(MerchantStoreReqConverter.toMerchantStoreDomainCommandInput(merchantStoreDomainCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse createMerchantStoreInfoBatch(SystemOriginEnum systemOriginEnum, List<MerchantStoreDomainCommandReq> list) {
        List<MerchantStoreBatchImportDTO> dtoList = merchantStoreCommandService.createMerchantStoreInfoBatch(MerchantStoreReqConverter.toMerchantStoreDomainCommandInputList(list, systemOriginEnum.type));
        return DubboResponse.getOK(MerchantStoreConverter.toMerchantStoreBatchImportRespList(dtoList));
    }

    @Override
    public DubboResponse<Boolean> updateMerchantStoreInfo(SystemOriginEnum systemOriginEnum, MerchantStoreDomainCommandReq merchantStoreDomainCommandReq) {
        Boolean success = merchantStoreCommandService.updateMerchantStoreInfo(MerchantStoreReqConverter.toMerchantStoreDomainCommandInput(merchantStoreDomainCommandReq, systemOriginEnum.type));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> updateMerchantStore(SystemOriginEnum systemOriginEnum, MerchantStoreCommandReq merchantStoreCommandReq) {
        Boolean success = merchantStoreCommandService.updateMerchantStore(MerchantStoreReqConverter.toMerchantStoreCommandInput(merchantStoreCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }
}
