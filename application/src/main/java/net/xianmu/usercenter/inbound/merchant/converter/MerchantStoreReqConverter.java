package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.req.v2.merchant.MerchantBasicQueryReq;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreDomainCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreReqConverter {


    private MerchantStoreReqConverter() {
        // 无需实现
    }

    public static MerchantStoreQueryInput toMerchantStoreQueryInput(MerchantStoreQueryReq merchantStoreQueryReq) {
        if (merchantStoreQueryReq == null) {
            return null;
        }
        MerchantStoreQueryInput merchantStoreQueryInput = new MerchantStoreQueryInput();
        merchantStoreQueryInput.setStoreId(merchantStoreQueryReq.getStoreId());
        merchantStoreQueryInput.setTenantId(merchantStoreQueryReq.getTenantId());
        merchantStoreQueryInput.setStoreName(merchantStoreQueryReq.getStoreName());
        merchantStoreQueryInput.setType(merchantStoreQueryReq.getType());
        merchantStoreQueryInput.setStatus(merchantStoreQueryReq.getStatus());
        merchantStoreQueryInput.setStoreIdList(merchantStoreQueryReq.getStoreIdList());
        merchantStoreQueryInput.setPhonePrefix(merchantStoreQueryReq.getPhone());
        merchantStoreQueryInput.setStoreNo(merchantStoreQueryReq.getStoreNo());
        merchantStoreQueryInput.setMId(merchantStoreQueryReq.getMId());
        merchantStoreQueryInput.setAreaNo(merchantStoreQueryReq.getAreaNo());
        merchantStoreQueryInput.setSize(merchantStoreQueryReq.getSize());
        merchantStoreQueryInput.setDirect(merchantStoreQueryReq.getDirect());
        merchantStoreQueryInput.setMIds(merchantStoreQueryReq.getMIds());
        merchantStoreQueryInput.setAreaNos(merchantStoreQueryReq.getAreaNos());
        merchantStoreQueryInput.setChannelCode(merchantStoreQueryReq.getChannelCode());
        merchantStoreQueryInput.setStoreNoList(merchantStoreQueryReq.getStoreNoList());
        merchantStoreQueryInput.setExactStoreName(merchantStoreQueryReq.getExactStoreName());
        merchantStoreQueryInput.setExactStoreNameList(merchantStoreQueryReq.getExactStoreNameList());
        merchantStoreQueryInput.setProvince(merchantStoreQueryReq.getProvince());
        merchantStoreQueryInput.setCity(merchantStoreQueryReq.getCity());
        merchantStoreQueryInput.setArea(merchantStoreQueryReq.getArea());
        merchantStoreQueryInput.setStartAuditTime(merchantStoreQueryReq.getStartAuditTime());
        merchantStoreQueryInput.setStartRegisterTime(merchantStoreQueryReq.getStartRegisterTime());
        merchantStoreQueryInput.setEndAuditTime(merchantStoreQueryReq.getEndAuditTime());
        merchantStoreQueryInput.setEndRegisterTime(merchantStoreQueryReq.getEndRegisterTime());
        merchantStoreQueryInput.setQueryManageAccount(merchantStoreQueryReq.isQueryManageAccount());
        merchantStoreQueryInput.setPageIndex(merchantStoreQueryReq.getPageIndex());
        merchantStoreQueryInput.setPageSize(merchantStoreQueryReq.getPageSize());
        merchantStoreQueryInput.setSortList(merchantStoreQueryReq.getSortList());
        merchantStoreQueryInput.setEnableOfflinePayment (merchantStoreQueryReq.getEnableOfflinePayment());

        return merchantStoreQueryInput;
    }

    public static MerchantStoreQueryInput basicToMerchantStoreQueryInput(MerchantBasicQueryReq basicQueryReq) {
        if (basicQueryReq == null) {
            return null;
        }
        MerchantStoreQueryInput merchantStoreQueryInput = new MerchantStoreQueryInput();
        merchantStoreQueryInput.setStoreId(basicQueryReq.getStoreId());
        merchantStoreQueryInput.setStoreIdList(basicQueryReq.getStoreIdList());
        merchantStoreQueryInput.setTenantId(basicQueryReq.getTenantId());
        merchantStoreQueryInput.setStoreName(basicQueryReq.getStoreName());
        merchantStoreQueryInput.setExactStoreName(basicQueryReq.getExactStoreName());
        merchantStoreQueryInput.setExactStoreNameList(basicQueryReq.getExactStoreNameList());
        merchantStoreQueryInput.setStatus(basicQueryReq.getStatus());
        merchantStoreQueryInput.setType(basicQueryReq.getType());
        merchantStoreQueryInput.setStoreNo(basicQueryReq.getStoreNo());
        merchantStoreQueryInput.setStoreNoList(basicQueryReq.getStoreNoList());
        merchantStoreQueryInput.setPhone(basicQueryReq.getPhone());
        merchantStoreQueryInput.setPhonePrefix(basicQueryReq.getPhonePrefix());
        merchantStoreQueryInput.setMId(basicQueryReq.getMId());
        merchantStoreQueryInput.setAreaNo(basicQueryReq.getAreaNo());
        merchantStoreQueryInput.setMIds(basicQueryReq.getMIds());
        merchantStoreQueryInput.setAreaNos(basicQueryReq.getAreaNos());
        merchantStoreQueryInput.setChannelCode(basicQueryReq.getChannelCode());
        merchantStoreQueryInput.setStartRegisterTime(basicQueryReq.getStartRegisterTime());
        merchantStoreQueryInput.setEndRegisterTime(basicQueryReq.getEndRegisterTime());
        merchantStoreQueryInput.setStartAuditTime(basicQueryReq.getStartAuditTime());
        merchantStoreQueryInput.setEndAuditTime(basicQueryReq.getEndAuditTime());
        merchantStoreQueryInput.setQueryManageAccount(basicQueryReq.isQueryManageAccount());
        merchantStoreQueryInput.setPageIndex(basicQueryReq.getPageIndex());
        merchantStoreQueryInput.setPageSize(basicQueryReq.getPageSize());
        merchantStoreQueryInput.setSortList(basicQueryReq.getSortList());
        return merchantStoreQueryInput;
    }


    public static MerchantStorePageQueryInput toMerchantStorePageQueryInput(MerchantStorePageQueryReq merchantStorePageQueryReq) {
        if (merchantStorePageQueryReq == null) {
            return null;
        }
        MerchantStorePageQueryInput merchantStorePageQueryInput = new MerchantStorePageQueryInput();
        merchantStorePageQueryInput.setPageIndex(merchantStorePageQueryReq.getPageIndex());
        merchantStorePageQueryInput.setPageSize(merchantStorePageQueryReq.getPageSize());
        merchantStorePageQueryInput.setId(merchantStorePageQueryReq.getId());
        merchantStorePageQueryInput.setStoreNo(merchantStorePageQueryReq.getStoreNo());
        merchantStorePageQueryInput.setTenantId(merchantStorePageQueryReq.getTenantId());
        merchantStorePageQueryInput.setStoreName(merchantStorePageQueryReq.getStoreName());
        merchantStorePageQueryInput.setType(merchantStorePageQueryReq.getType());
        merchantStorePageQueryInput.setTypeList(merchantStorePageQueryReq.getTypeList());
        merchantStorePageQueryInput.setStatus(merchantStorePageQueryReq.getStatus());
        merchantStorePageQueryInput.setPhone(merchantStorePageQueryReq.getPhone());
        merchantStorePageQueryInput.setBillSwitch(merchantStorePageQueryReq.getBillSwitch());
        merchantStorePageQueryInput.setStoreIds(merchantStorePageQueryReq.getStoreIds());
        merchantStorePageQueryInput.setSupplyStatus(merchantStorePageQueryReq.getSupplyStatus());
        merchantStorePageQueryInput.setSupplyStoreIds(merchantStorePageQueryReq.getSupplyStoreIds());
        merchantStorePageQueryInput.setStartTime(merchantStorePageQueryReq.getStartTime());
        merchantStorePageQueryInput.setEndTime(merchantStorePageQueryReq.getEndTime());
        merchantStorePageQueryInput.setNoMatchingStoreIds(merchantStorePageQueryReq.getNoMatchingStoreIds());
        merchantStorePageQueryInput.setProvince(merchantStorePageQueryReq.getProvince());
        merchantStorePageQueryInput.setCity(merchantStorePageQueryReq.getCity());
        merchantStorePageQueryInput.setArea(merchantStorePageQueryReq.getArea());
        merchantStorePageQueryInput.setGroupId(merchantStorePageQueryReq.getGroupId());
        merchantStorePageQueryInput.setMId(merchantStorePageQueryReq.getMId());
        merchantStorePageQueryInput.setAreaNo(merchantStorePageQueryReq.getAreaNo());
        merchantStorePageQueryInput.setSize(merchantStorePageQueryReq.getSize());
        merchantStorePageQueryInput.setDirect(merchantStorePageQueryReq.getDirect());
        merchantStorePageQueryInput.setMIds(merchantStorePageQueryReq.getMIds());
        merchantStorePageQueryInput.setAreaNos(merchantStorePageQueryReq.getAreaNos());
        merchantStorePageQueryInput.setTenantIds(merchantStorePageQueryReq.getTenantIds());
        merchantStorePageQueryInput.setAdminId(merchantStorePageQueryReq.getAdminId());
        merchantStorePageQueryInput.setExcludeStatusList(merchantStorePageQueryReq.getExcludeStatusList());
        merchantStorePageQueryInput.setPlaceOrderPermissionExpiryTime (merchantStorePageQueryReq.getPlaceOrderPermissionExpiryTime ());
        merchantStorePageQueryInput.setPlaceOrderEnableFlag(merchantStorePageQueryReq.getPlaceOrderEnableFlag ());
        merchantStorePageQueryInput.setEnableOfflinePayment (merchantStorePageQueryReq.getEnableOfflinePayment ());
        merchantStorePageQueryInput.setBalanceAuthority(merchantStorePageQueryReq.getBalanceAuthority());
        return merchantStorePageQueryInput;
    }


    public static List<MerchantStoreDomainCommandInput> toMerchantStoreDomainCommandInputList(List<MerchantStoreDomainCommandReq> list, Integer systemOrigin) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreDomainCommandInput> merchantStoreResultRespList = new ArrayList<>();
        for (MerchantStoreDomainCommandReq req : list) {
            merchantStoreResultRespList.add(toMerchantStoreDomainCommandInput(req, systemOrigin));
        }
        return merchantStoreResultRespList;
    }


    public static MerchantStoreDomainCommandInput toMerchantStoreDomainCommandInput(MerchantStoreDomainCommandReq merchantStoreDomainCommandReq, Integer systemOrigin) {
        if (merchantStoreDomainCommandReq == null) {
            return null;
        }
        MerchantStoreDomainCommandInput merchantStoreDomainCommandInput = new MerchantStoreDomainCommandInput();
        merchantStoreDomainCommandInput.setStoreId(merchantStoreDomainCommandReq.getStoreId());
        merchantStoreDomainCommandInput.setGroupId(merchantStoreDomainCommandReq.getGroupId());
        merchantStoreDomainCommandInput.setTenantId(merchantStoreDomainCommandReq.getTenantId());
        merchantStoreDomainCommandInput.setAuditFlag(merchantStoreDomainCommandReq.getAuditFlag());
        merchantStoreDomainCommandInput.setBatchCreateRowNum(merchantStoreDomainCommandReq.getBatchCreateRowNum());
        merchantStoreDomainCommandInput.setMerchantStore(toMerchantStoreCommandInput(merchantStoreDomainCommandReq.getMerchantStore(), systemOrigin));
        merchantStoreDomainCommandInput.setMerchantAddressList(MerchantAddressConverter.toMerchantAddressCommandInputList(merchantStoreDomainCommandReq.getMerchantAddressList(), systemOrigin));
        merchantStoreDomainCommandInput.setMerchantStoreAccountList(MerchantStoreAccountReqConverter.toMerchantStoreAccountCommandInputList(merchantStoreDomainCommandReq.getMerchantStoreAccountList(), systemOrigin));
        merchantStoreDomainCommandInput.setSystemOrigin(systemOrigin);
        return merchantStoreDomainCommandInput;
    }


    public static MerchantStoreCommandInput toMerchantStoreCommandInput(MerchantStoreCommandReq merchantStoreCommandReq, Integer systemOrigin) {
        if (merchantStoreCommandReq == null) {
            return null;
        }
        MerchantStoreCommandInput merchantStoreCommandInput = new MerchantStoreCommandInput();
        merchantStoreCommandInput.setId(merchantStoreCommandReq.getId());
        merchantStoreCommandInput.setTenantId(merchantStoreCommandReq.getTenantId());
        merchantStoreCommandInput.setStoreName(merchantStoreCommandReq.getStoreName());
        merchantStoreCommandInput.setType(merchantStoreCommandReq.getType());
        merchantStoreCommandInput.setRegisterTime(merchantStoreCommandReq.getRegisterTime());
        merchantStoreCommandInput.setStatus(merchantStoreCommandReq.getStatus());
        merchantStoreCommandInput.setAuditRemark(merchantStoreCommandReq.getAuditRemark());
        merchantStoreCommandInput.setAuditTime(merchantStoreCommandReq.getAuditTime());
        merchantStoreCommandInput.setBillSwitch(merchantStoreCommandReq.getBillSwitch());
        merchantStoreCommandInput.setOnlinePayment(merchantStoreCommandReq.getOnlinePayment());
        merchantStoreCommandInput.setBalanceAuthority(merchantStoreCommandReq.getBalanceAuthority());
        merchantStoreCommandInput.setStoreNo(merchantStoreCommandReq.getStoreNo());
        merchantStoreCommandInput.setRemark(merchantStoreCommandReq.getRemark());
        merchantStoreCommandInput.setSystemOrigin(systemOrigin);
        merchantStoreCommandInput.setRegionalId(merchantStoreCommandReq.getRegionalId());
        merchantStoreCommandInput.setPlaceOrderPermissionTimeLimited(merchantStoreCommandReq.getPlaceOrderPermissionTimeLimited ());
        merchantStoreCommandInput.setPlaceOrderPermissionExpiryTime(merchantStoreCommandReq.getPlaceOrderPermissionExpiryTime ());
        merchantStoreCommandInput.setEnableOfflinePayment (merchantStoreCommandReq.getEnableOfflinePayment ());
        merchantStoreCommandInput.setNonCashAuthority (merchantStoreCommandReq.getNonCashAuthority ());
        return merchantStoreCommandInput;
    }
}
