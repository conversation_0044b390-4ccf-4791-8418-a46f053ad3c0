package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/8/4 16:40
 */
public class MerchantStoreGroupConverterV2 {


    private MerchantStoreGroupConverterV2() {
        // 无需实现
    }



    public static List<MerchantStoreGroupPageResultResp> toMerchantStoreGroupPageResultRespList(List<MerchantStoreGroupEntity> merchantStoreGroupEntityList) {
        if (merchantStoreGroupEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupPageResultResp> merchantStoreGroupPageResultRespList = new ArrayList<>();
        for (MerchantStoreGroupEntity merchantStoreGroupEntity : merchantStoreGroupEntityList) {
            merchantStoreGroupPageResultRespList.add(toMerchantStoreGroupPageResultResp(merchantStoreGroupEntity));
        }
        return merchantStoreGroupPageResultRespList;
    }

    public static MerchantStoreGroupPageResultResp toMerchantStoreGroupPageResultResp(MerchantStoreGroupEntity merchantStoreGroupEntity) {
        if (merchantStoreGroupEntity == null) {
            return null;
        }
        MerchantStoreGroupPageResultResp merchantStoreGroupPageResultResp = new MerchantStoreGroupPageResultResp();
        merchantStoreGroupPageResultResp.setStoreNum(merchantStoreGroupEntity.getStoreNum());
        merchantStoreGroupPageResultResp.setCreateTime(merchantStoreGroupEntity.getCreateTime());
        merchantStoreGroupPageResultResp.setUpdateTime(merchantStoreGroupEntity.getUpdateTime());
        merchantStoreGroupPageResultResp.setType(merchantStoreGroupEntity.getType());
        merchantStoreGroupPageResultResp.setMerchantStoreGroupId(merchantStoreGroupEntity.getId());
        merchantStoreGroupPageResultResp.setMerchantStoreGroupName(merchantStoreGroupEntity.getName());
// Not mapped TO fields:
// Not mapped FROM fields:
// id
// tenantId
// name
// storeId
        return merchantStoreGroupPageResultResp;
    }
}
