package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantDeliveryAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantContactConverter;
import org.springframework.data.redis.connection.convert.ListConverter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public class MerchantAddressConverterV2 {


    private MerchantAddressConverterV2() {
        // 无需实现
    }

    public static List<MerchantAddressDomainResp> toMerchantAddressDomainRespList(List<MerchantAddressEntity> merchantAddressEntityList) {
        if (merchantAddressEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressDomainResp> merchantAddressDomainRespList = new ArrayList<>();
        for (MerchantAddressEntity merchantAddressEntity : merchantAddressEntityList) {
            merchantAddressDomainRespList.add(toMerchantAddressDomainResp(merchantAddressEntity));
        }
        return merchantAddressDomainRespList;
    }

    public static MerchantAddressDomainResp toMerchantAddressDomainResp(MerchantAddressEntity merchantAddressEntity) {
        if (merchantAddressEntity == null) {
            return null;
        }
        MerchantAddressDomainResp merchantAddressDomainResp = new MerchantAddressDomainResp();
        merchantAddressDomainResp.setId(merchantAddressEntity.getId());
        merchantAddressDomainResp.setTenantId(merchantAddressEntity.getTenantId());
        merchantAddressDomainResp.setStoreId(merchantAddressEntity.getStoreId());
        merchantAddressDomainResp.setProvince(merchantAddressEntity.getProvince());
        merchantAddressDomainResp.setCity(merchantAddressEntity.getCity());
        merchantAddressDomainResp.setArea(merchantAddressEntity.getArea());
        merchantAddressDomainResp.setAddress(merchantAddressEntity.getAddress());
        merchantAddressDomainResp.setHouseNumber(merchantAddressEntity.getHouseNumber());
        merchantAddressDomainResp.setPoiNote(merchantAddressEntity.getPoiNote());
        merchantAddressDomainResp.setCreateTime(merchantAddressEntity.getCreateTime());
        merchantAddressDomainResp.setUpdateTime(merchantAddressEntity.getUpdateTime());
        merchantAddressDomainResp.setDefaultFlag(merchantAddressEntity.getDefaultFlag());
        merchantAddressDomainResp.setStatus(merchantAddressEntity.getStatus());
        merchantAddressDomainResp.setXmContactId(merchantAddressEntity.getXmContactId());
        merchantAddressDomainResp.setMId(merchantAddressEntity.getMId());
        merchantAddressDomainResp.setRemark(merchantAddressEntity.getRemark());
        merchantAddressDomainResp.setAddressRemark(merchantAddressEntity.getAddressRemark());
        merchantAddressDomainResp.setContactList(MerchantContactConverterV2.toMerchantContactResultRespList(merchantAddressEntity.getContactList()));
// Not mapped FROM fields:
// contactName
// contactPhone
// deliveryAddress
        return merchantAddressDomainResp;
    }



    public static List<MerchantAddressResultResp> toMerchantAddressRespList(List<MerchantAddressEntity> merchantAddressEntityList) {
        if (merchantAddressEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressResultResp> merchantAddressDomainRespList = new ArrayList<>();
        for (MerchantAddressEntity merchantAddressEntity : merchantAddressEntityList) {
            merchantAddressDomainRespList.add(toMerchantAddressResp(merchantAddressEntity));
        }
        return merchantAddressDomainRespList;
    }

    public static MerchantAddressResultResp toMerchantAddressResp(MerchantAddressEntity merchantAddressEntity) {
        if (merchantAddressEntity == null) {
            return null;
        }
        MerchantAddressResultResp merchantAddressDomainResp = new MerchantAddressResultResp();
        merchantAddressDomainResp.setId(merchantAddressEntity.getId());
        merchantAddressDomainResp.setTenantId(merchantAddressEntity.getTenantId());
        merchantAddressDomainResp.setStoreId(merchantAddressEntity.getStoreId());
        merchantAddressDomainResp.setProvince(merchantAddressEntity.getProvince());
        merchantAddressDomainResp.setCity(merchantAddressEntity.getCity());
        merchantAddressDomainResp.setArea(merchantAddressEntity.getArea());
        merchantAddressDomainResp.setAddress(merchantAddressEntity.getAddress());
        merchantAddressDomainResp.setHouseNumber(merchantAddressEntity.getHouseNumber());
        merchantAddressDomainResp.setPoiNote(merchantAddressEntity.getPoiNote());
        merchantAddressDomainResp.setCreateTime(merchantAddressEntity.getCreateTime());
        merchantAddressDomainResp.setUpdateTime(merchantAddressEntity.getUpdateTime());
        merchantAddressDomainResp.setDefaultFlag(merchantAddressEntity.getDefaultFlag());
        merchantAddressDomainResp.setStatus(merchantAddressEntity.getStatus());
        merchantAddressDomainResp.setXmContactId(merchantAddressEntity.getXmContactId());
        merchantAddressDomainResp.setMId(merchantAddressEntity.getMId());
        merchantAddressDomainResp.setRemark(merchantAddressEntity.getRemark());
        merchantAddressDomainResp.setAddressRemark(merchantAddressEntity.getAddressRemark());
// Not mapped FROM fields:
// contactName
// contactPhone
// deliveryAddress
        return merchantAddressDomainResp;
    }


}
