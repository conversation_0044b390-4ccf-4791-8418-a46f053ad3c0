package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.api.tenant.dto.MerchantDTO;
import net.xianmu.usercenter.client.tenant.req.MerchantCommandReq;
import net.xianmu.usercenter.client.tenant.req.MerchantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.common.input.command.MerchantCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-25 10:47:01
 * @version 1.0
 *
 */
public class MerchantConverter {


    private MerchantConverter() {
        // 无需实现
    }

    public static List<MerchantResultResp> toMerchantResultRespList(List<MerchantEntity> entityList) {
        if (entityList == null) {
            return Collections.emptyList();
        }
        List<MerchantResultResp> merchantResultRespList = new ArrayList<>();
        for (MerchantEntity entity : entityList) {
            merchantResultRespList.add(toMerchantResultResp(entity));
        }
        return merchantResultRespList;
    }

    public static MerchantResultResp toMerchantResultResp(MerchantEntity entity) {
        if (entity == null) {
            return null;
        }
        MerchantResultResp merchantResultResp = new MerchantResultResp();
        merchantResultResp.setId(entity.getId());
        merchantResultResp.setTenantId(entity.getTenantId());
        merchantResultResp.setMerchantName(entity.getMerchantName());
        merchantResultResp.setLogoImage(entity.getLogoImage());
        merchantResultResp.setBackgroundImage(entity.getBackgroundImage());
        merchantResultResp.setCreateTime(entity.getCreateTime());
        merchantResultResp.setUpdateTime(entity.getUpdateTime());
        return merchantResultResp;
    }



    /**
     * ******************** MerchantCommandInput ***********************
     */


    public static MerchantCommandInput toMerchantCommandInput(MerchantCommandReq merchantCommandReq) {
        if (merchantCommandReq == null) {
            return null;
        }
        MerchantCommandInput merchantCommandInput = new MerchantCommandInput();
        merchantCommandInput.setId(merchantCommandReq.getId());
        merchantCommandInput.setTenantId(merchantCommandReq.getTenantId());
        merchantCommandInput.setMerchantName(merchantCommandReq.getMerchantName());
        merchantCommandInput.setLogoImage(merchantCommandReq.getLogoImage());
        merchantCommandInput.setBackgroundImage(merchantCommandReq.getBackgroundImage());
        merchantCommandInput.setCreateTime(merchantCommandReq.getCreateTime());
        merchantCommandInput.setUpdateTime(merchantCommandReq.getUpdateTime());
        return merchantCommandInput;
    }

    public static MerchantQueryInput toMerchantQueryInput(MerchantQueryReq merchantQueryReq) {
        MerchantQueryInput merchantQueryInput = new MerchantQueryInput();
        merchantQueryInput.setTenantId(merchantQueryReq.getTenantId());
        merchantQueryInput.setTenantIdList(merchantQueryReq.getTenantIdList());
        return merchantQueryInput;
    }
}
