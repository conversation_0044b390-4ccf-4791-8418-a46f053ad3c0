package net.xianmu.usercenter.inbound.common.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.usercenter.application.datatransfer.MerchantAddressTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreTransServiceImpl;
import net.xianmu.usercenter.common.dto.XmDataFixTaskParamDTO;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton 鲜沐门店数据补偿（已废弃）
 * @date 2023/7/17 15:07
 */
@Component
@Slf4j
@Deprecated
public class XmMerchantCompensateProcessor extends XianMuJavaProcessor {

    @Resource
    private MerchantStoreAccountQueryRepository accountQueryRepository;
    @Resource
    private MerchantAddressQueryRepository merchantAddressQueryRepository;
    @Resource
    private MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService;
    @Resource
    private MerchantAddressTransServiceImpl merchantAddressTransService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        try {
            log.info("---------补偿鲜沐账户、地址的定时任务开始--------");
            // 捞取需要补偿的账户数据,每次20条
            List<MerchantStoreAccountEntity> accountEntities = accountQueryRepository.selectXmErrorAccount();
            List<Long> accountIds = accountEntities.stream().map(MerchantStoreAccountEntity::getXmAccountId).collect(Collectors.toList());
            log.info("本次待补偿的账户数据,account:{}", JSON.toJSONString(accountIds));
            if (CollUtil.isNotEmpty(accountIds)) {
                merchantStoreAccountTransService.refreshAccountData(accountIds);
            }

            // 捞取需要补偿的地址数据，每次20条
            List<MerchantAddressEntity> addressEntities = merchantAddressQueryRepository.selectXmErrorAddress();
            List<Long> xmContactIds = addressEntities.stream().map(MerchantAddressEntity::getXmContactId).collect(Collectors.toList());
            log.info("本次待补偿的地址数据,xmContactIds:{}", JSON.toJSONString(xmContactIds));
            if (CollUtil.isNotEmpty(addressEntities)) {
                merchantAddressTransService.refreshAddressData(xmContactIds);
            }
            log.info("---------补偿鲜沐账户、地址的定时任务结束--------");
        } catch (Exception e) {
            log.error("定时任务执行失败!", e);
        }
        return new ProcessResult(true);
    }
}
