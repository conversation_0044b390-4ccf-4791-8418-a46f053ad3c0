package net.xianmu.usercenter.inbound.tenant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.tenant.service.TenantCommandService;
import net.xianmu.usercenter.client.common.enums.SystemOriginEnum;
import net.xianmu.usercenter.client.tenant.provider.TenantCommandProvider;
import net.xianmu.usercenter.client.tenant.req.TenantCommandReq;
import net.xianmu.usercenter.inbound.tenant.converter.TenantReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:48
 */
@DubboService
public class TenantCommandProviderImpl implements TenantCommandProvider {

    @Autowired
    private TenantCommandService tenantCommandService;

    @Override
    public DubboResponse<Long> createTenantInfo(SystemOriginEnum systemOriginEnum, TenantCommandReq tenantCommandReq) {
        final Long tenantId = tenantCommandService.createTenantInfo(TenantReqConverter.toTenantCommandInput(tenantCommandReq));
        return DubboResponse.getOK(tenantId);
    }

    @Override
    public DubboResponse update(SystemOriginEnum systemOriginEnum, TenantCommandReq tenantCommandReq) {
        tenantCommandService.update(TenantReqConverter.toTenantCommandInput(tenantCommandReq));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse batchUpdateOp(SystemOriginEnum systemOriginEnum, List<Long> ids, String opUname, Long opUid) {
        tenantCommandService.batchUpdateOp(ids, opUname, opUid);
        return DubboResponse.getOK();
    }
}
