package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.api.tenant.dto.TenantAccountDTO;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 15:04
 */
public class TenantAccountConverter {


    private TenantAccountConverter() {
        // 无需实现
    }

    public static List<TenantAccountResultResp> toTenantAccountResultRespList(List<TenantAccountDTO> tenantAccountDTOList) {
        if (tenantAccountDTOList == null) {
            return Collections.emptyList();
        }
        List<TenantAccountResultResp> tenantAccountResultRespList = new ArrayList<>();
        for (TenantAccountDTO tenantAccountDTO : tenantAccountDTOList) {
            tenantAccountResultRespList.add(toTenantAccountResultResp(tenantAccountDTO));
        }
        return tenantAccountResultRespList;
    }

    public static TenantAccountResultResp toTenantAccountResultResp(TenantAccountDTO tenantAccountDTO) {
        if (tenantAccountDTO == null) {
            return null;
        }
        TenantAccountResultResp tenantAccountResultResp = new TenantAccountResultResp();
        tenantAccountResultResp.setId(tenantAccountDTO.getId());
        tenantAccountResultResp.setAuthUserId(tenantAccountDTO.getAuthUserId());
        tenantAccountResultResp.setTenantId(tenantAccountDTO.getTenantId());
        tenantAccountResultResp.setPhone(tenantAccountDTO.getPhone());
        tenantAccountResultResp.setNickname(tenantAccountDTO.getNickname());
        tenantAccountResultResp.setProfilePicture(tenantAccountDTO.getProfilePicture());
        tenantAccountResultResp.setStatus(tenantAccountDTO.getStatus());
        tenantAccountResultResp.setOperatorTime(tenantAccountDTO.getOperatorTime());
        tenantAccountResultResp.setOperatorPhone(tenantAccountDTO.getOperatorPhone());
        tenantAccountResultResp.setUpdateTime(tenantAccountDTO.getUpdateTime());
        tenantAccountResultResp.setCreateTime(tenantAccountDTO.getCreateTime());
        tenantAccountResultResp.setDeletedFlag(tenantAccountDTO.getDeletedFlag());
        tenantAccountResultResp.setEmail(tenantAccountDTO.getEmail());
        tenantAccountResultResp.setUpdater(tenantAccountDTO.getUpdater());
        return tenantAccountResultResp;
    }

    public static List<TenantAccountDTO> toTenantAccountDTOList(List<TenantAccountResultResp> tenantAccountResultRespList) {
        if (tenantAccountResultRespList == null) {
            return Collections.emptyList();
        }
        List<TenantAccountDTO> tenantAccountDTOList = new ArrayList<>();
        for (TenantAccountResultResp tenantAccountResultResp : tenantAccountResultRespList) {
            tenantAccountDTOList.add(toTenantAccountDTO(tenantAccountResultResp));
        }
        return tenantAccountDTOList;
    }

    public static TenantAccountDTO toTenantAccountDTO(TenantAccountResultResp tenantAccountResultResp) {
        if (tenantAccountResultResp == null) {
            return null;
        }
        TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
        tenantAccountDTO.setId(tenantAccountResultResp.getId());
        tenantAccountDTO.setAuthUserId(tenantAccountResultResp.getAuthUserId());
        tenantAccountDTO.setTenantId(tenantAccountResultResp.getTenantId());
        tenantAccountDTO.setPhone(tenantAccountResultResp.getPhone());
        tenantAccountDTO.setNickname(tenantAccountResultResp.getNickname());
        tenantAccountDTO.setProfilePicture(tenantAccountResultResp.getProfilePicture());
        tenantAccountDTO.setStatus(tenantAccountResultResp.getStatus());
        tenantAccountDTO.setOperatorTime(tenantAccountResultResp.getOperatorTime());
        tenantAccountDTO.setOperatorPhone(tenantAccountResultResp.getOperatorPhone());
        tenantAccountDTO.setUpdateTime(tenantAccountResultResp.getUpdateTime());
        tenantAccountDTO.setCreateTime(tenantAccountResultResp.getCreateTime());
        tenantAccountDTO.setDeletedFlag(tenantAccountResultResp.getDeletedFlag());
        return tenantAccountDTO;
    }
}
