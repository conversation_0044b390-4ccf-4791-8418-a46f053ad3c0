package net.xianmu.usercenter.inbound.tenant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.tenant.service.MerchantQueryService;
import net.xianmu.usercenter.client.tenant.provider.MerchantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.MerchantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.inbound.tenant.converter.MerchantConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/25 11:23
 */
@DubboService
public class MerchantQueryProviderImpl implements MerchantQueryProvider {

    @Autowired
    private MerchantQueryService merchantQueryService;

    @Override
    public DubboResponse<MerchantResultResp> getMerchantByTenantId(Long tenantId) {
        return DubboResponse.getOK(MerchantConverter.toMerchantResultResp(merchantQueryService.getMerchantByTenantId(tenantId)));
    }

    @Override
    public DubboResponse<List<MerchantResultResp>> getTenantConfigs(MerchantQueryReq merchantQueryReq) {
        return DubboResponse.getOK(MerchantConverter.toMerchantResultRespList(merchantQueryService.getTenantConfigs(MerchantConverter.toMerchantQueryInput(merchantQueryReq))));
    }

}
