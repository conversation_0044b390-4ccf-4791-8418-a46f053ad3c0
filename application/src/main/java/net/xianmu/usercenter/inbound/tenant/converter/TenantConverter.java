package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.dto.TenantDTO;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:02
 */
public class TenantConverter {


    private TenantConverter() {
        // 无需实现
    }


    public static List<TenantResultResp> toTenantResultRespList(List<TenantDTO> tenantDTOList) {
        if (tenantDTOList == null) {
            return Collections.emptyList();
        }
        List<TenantResultResp> tenantResultRespList = new ArrayList<>();
        for (TenantDTO tenantDTO : tenantDTOList) {
            tenantResultRespList.add(toTenantResultResp(tenantDTO));
        }
        return tenantResultRespList;
    }

    public static TenantResultResp toTenantResultResp(TenantDTO tenantDTO) {
        if (tenantDTO == null) {
            return null;
        }
        TenantResultResp tenantResultResp = new TenantResultResp();
        tenantResultResp.setId(tenantDTO.getId());
        tenantResultResp.setPhone(tenantDTO.getPhone());
        tenantResultResp.setPassword(tenantDTO.getPassword());
        tenantResultResp.setTenantName(tenantDTO.getTenantName());
        tenantResultResp.setType(tenantDTO.getType());
        tenantResultResp.setStatus(tenantDTO.getStatus());
        tenantResultResp.setCreateTime(tenantDTO.getCreateTime());
        tenantResultResp.setUpdateTime(tenantDTO.getUpdateTime());
        tenantResultResp.setAdminId(tenantDTO.getAdminId());
        tenantResultResp.setOpUid(tenantDTO.getOpUid());
        tenantResultResp.setOpUname(tenantDTO.getOpUname());
        tenantResultResp.setOperator(tenantDTO.getOperator());
        tenantResultResp.setBelongDB(tenantDTO.getBelongDB());
        tenantResultResp.setProfitSharingSwitch(tenantDTO.getProfitSharingSwitch());
        tenantResultResp.setOnlinePayChannel(tenantDTO.getOnlinePayChannel());
        tenantResultResp.setEmail(tenantDTO.getEmail());
        tenantResultResp.setAccountLoginType(tenantDTO.getAccountLoginType());
        return tenantResultResp;
    }

    /**
     *  *************************** TenantAndBusinessInfoResultResp ***********************************
     */

    public static List<TenantAndBusinessInfoResultResp> toTenantAndBusinessInfoResultRespList(List<TenantAndBusinessDTO> tenantAndBusinessDTOList) {
        if (tenantAndBusinessDTOList == null) {
            return Collections.emptyList();
        }
        List<TenantAndBusinessInfoResultResp> tenantAndBusinessInfoResultRespList = new ArrayList<>();
        for (TenantAndBusinessDTO tenantAndBusinessDTO : tenantAndBusinessDTOList) {
            tenantAndBusinessInfoResultRespList.add(toTenantAndBusinessInfoResultResp(tenantAndBusinessDTO));
        }
        return tenantAndBusinessInfoResultRespList;
    }

    public static TenantAndBusinessInfoResultResp toTenantAndBusinessInfoResultResp(TenantAndBusinessDTO tenantAndBusinessDTO) {
        if (tenantAndBusinessDTO == null) {
            return null;
        }
        TenantAndBusinessInfoResultResp tenantAndBusinessInfoResultResp = new TenantAndBusinessInfoResultResp();
        tenantAndBusinessInfoResultResp.setTenantId(tenantAndBusinessDTO.getTenantId());
        tenantAndBusinessInfoResultResp.setTenantName(tenantAndBusinessDTO.getTenantName());
        tenantAndBusinessInfoResultResp.setPhone(tenantAndBusinessDTO.getPhone());
        tenantAndBusinessInfoResultResp.setCompanyName(tenantAndBusinessDTO.getCompanyName());
        tenantAndBusinessInfoResultResp.setCreditCode(tenantAndBusinessDTO.getCreditCode());
        tenantAndBusinessInfoResultResp.setTenantCompanyId(tenantAndBusinessDTO.getTenantCompanyId());
        tenantAndBusinessInfoResultResp.setAdminId(tenantAndBusinessDTO.getAdminId());
        tenantAndBusinessInfoResultResp.setCreateTime(tenantAndBusinessDTO.getCreateTime());
        tenantAndBusinessInfoResultResp.setUpdateTime(tenantAndBusinessDTO.getUpdateTime());
        tenantAndBusinessInfoResultResp.setOpUname(tenantAndBusinessDTO.getOpUname());
        tenantAndBusinessInfoResultResp.setContactName(tenantAndBusinessDTO.getContactName());
        return tenantAndBusinessInfoResultResp;
    }

}
