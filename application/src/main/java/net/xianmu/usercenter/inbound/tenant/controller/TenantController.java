package net.xianmu.usercenter.inbound.tenant.controller;

import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.api.tenant.service.TenantQueryService;
import net.xianmu.usercenter.application.datatransfer.RegionalOrganizationTransServiceImpl;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 13:51
 */
@RestController
public class TenantController {
    @Autowired
    private TenantQueryService queryService;
    @Autowired
    private RegionalOrganizationTransServiceImpl regionalOrganizationTransService;



}
