package net.xianmu.usercenter.inbound.regional.provider;

import net.xianmu.usercenter.api.regional.service.RegionalOrganizationCommandService;
import net.xianmu.usercenter.client.regional.provider.RegionalOrganizationCommandProvider;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @version 1.0
 * @date 2023-08-02 17:54:02
 */
@DubboService
public class RegionalOrganizationCommandProviderImpl implements RegionalOrganizationCommandProvider {
    @Autowired
    private RegionalOrganizationCommandService regionalOrganizationCommandService;
}
