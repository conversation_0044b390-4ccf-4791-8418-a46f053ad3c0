package net.xianmu.usercenter.inbound.merchant.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountToAuthServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 15:07
 */
@Component
@Slf4j
public class AuthDataTransferProcessor extends XianMuJavaProcessor {

    private static final String DATA_TYPE_XM = "xm";

    @Resource
    private MerchantStoreAccountToAuthServiceImpl merchantStoreAccountToAuthServiceImpl;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        try {
            String instanceParameterStr = context.getInstanceParameters();
            log.info("同步账户数据到auth的定时任务开始执行, instanceParameters: 【{}】", JSON.toJSONString(instanceParameterStr));
            long start = System.currentTimeMillis();
           JSONObject map = JSON.parseObject(instanceParameterStr);
            final String dataType = map.getString("dataType");
            if(DATA_TYPE_XM.equals(dataType)) {
                log.info("开始同步鲜沐数据");
                merchantStoreAccountToAuthServiceImpl.xmTransferToAuthTask();
            } else {
                log.info("开始同步saas数据");
                merchantStoreAccountToAuthServiceImpl.saasTransferToAuthTask();
            }
            log.info("同步鲜沐用户的定时任务执行完成!!耗时：{}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("定时任务执行失败!", e);
        }
        return new ProcessResult(true);
    }
}
