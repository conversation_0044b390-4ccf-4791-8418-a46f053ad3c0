package net.xianmu.usercenter.inbound.regional.converter;

import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-08-02 17:54:02
 * @version 1.0
 *
 */
public class RegionalOrganizationConverter {


    private RegionalOrganizationConverter() {
        // 无需实现
    }

    public static List<RegionalOrganizationEntity> toRegionalOrganizationEntityList(List<RegionalOrganizationResultResp> regionalOrganizationResultRespList) {
        if (regionalOrganizationResultRespList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganizationEntity> regionalOrganizationEntityList = new ArrayList<>();
        for (RegionalOrganizationResultResp regionalOrganizationResultResp : regionalOrganizationResultRespList) {
            regionalOrganizationEntityList.add(toRegionalOrganizationEntity(regionalOrganizationResultResp));
        }
        return regionalOrganizationEntityList;
    }

    public static RegionalOrganizationEntity toRegionalOrganizationEntity(RegionalOrganizationResultResp regionalOrganizationResultResp) {
        if (regionalOrganizationResultResp == null) {
            return null;
        }
        RegionalOrganizationEntity regionalOrganizationEntity = new RegionalOrganizationEntity();
        regionalOrganizationEntity.setId(regionalOrganizationResultResp.getId());
        regionalOrganizationEntity.setTenantId(regionalOrganizationResultResp.getTenantId());
        regionalOrganizationEntity.setPhone(regionalOrganizationResultResp.getPhone());
        regionalOrganizationEntity.setOrganizationName(regionalOrganizationResultResp.getOrganizationName());
        regionalOrganizationEntity.setSource(regionalOrganizationResultResp.getSource());
        regionalOrganizationEntity.setSize(regionalOrganizationResultResp.getSize());
        regionalOrganizationEntity.setStatus(regionalOrganizationResultResp.getStatus());
        regionalOrganizationEntity.setAdminId(regionalOrganizationResultResp.getAdminId());
        regionalOrganizationEntity.setCreateTime(regionalOrganizationResultResp.getCreateTime());
        regionalOrganizationEntity.setUpdateTime(regionalOrganizationResultResp.getUpdateTime());
        regionalOrganizationEntity.setCreator(regionalOrganizationResultResp.getCreator());
        regionalOrganizationEntity.setUpdater(regionalOrganizationResultResp.getUpdater());
        return regionalOrganizationEntity;
    }

    public static List<RegionalOrganizationResultResp> toRegionalOrganizationResultRespList(List<RegionalOrganizationEntity> regionalOrganizationEntityList) {
        if (regionalOrganizationEntityList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganizationResultResp> regionalOrganizationResultRespList = new ArrayList<>();
        for (RegionalOrganizationEntity regionalOrganizationEntity : regionalOrganizationEntityList) {
            regionalOrganizationResultRespList.add(toRegionalOrganizationResultResp(regionalOrganizationEntity));
        }
        return regionalOrganizationResultRespList;
    }

    public static RegionalOrganizationResultResp toRegionalOrganizationResultResp(RegionalOrganizationEntity regionalOrganizationEntity) {
        if (regionalOrganizationEntity == null) {
            return null;
        }
        RegionalOrganizationResultResp regionalOrganizationResultResp = new RegionalOrganizationResultResp();
        regionalOrganizationResultResp.setId(regionalOrganizationEntity.getId());
        regionalOrganizationResultResp.setTenantId(regionalOrganizationEntity.getTenantId());
        regionalOrganizationResultResp.setPhone(regionalOrganizationEntity.getPhone());
        regionalOrganizationResultResp.setOrganizationName(regionalOrganizationEntity.getOrganizationName());
        regionalOrganizationResultResp.setSource(regionalOrganizationEntity.getSource());
        regionalOrganizationResultResp.setSize(regionalOrganizationEntity.getSize());
        regionalOrganizationResultResp.setStatus(regionalOrganizationEntity.getStatus());
        regionalOrganizationResultResp.setAdminId(regionalOrganizationEntity.getAdminId());
        regionalOrganizationResultResp.setCreateTime(regionalOrganizationEntity.getCreateTime());
        regionalOrganizationResultResp.setUpdateTime(regionalOrganizationEntity.getUpdateTime());
        regionalOrganizationResultResp.setCreator(regionalOrganizationEntity.getCreator());
        regionalOrganizationResultResp.setUpdater(regionalOrganizationEntity.getUpdater());
        return regionalOrganizationResultResp;
    }
}
