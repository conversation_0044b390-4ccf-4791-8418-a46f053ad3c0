package net.xianmu.usercenter.inbound.merchant.provider.v2.merchat;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.v2.merchant.MerchantStoreQueryProviderV2;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.req.v2.merchant.MerchantBasicQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreConverterV2;
import net.xianmu.usercenter.inbound.page.PageConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:01
 */
@DubboService
@Slf4j
public class MerchantStoreV2QueryProviderImpl implements MerchantStoreQueryProviderV2 {
    @Autowired
    private MerchantStoreQueryService queryService;


    @Override
    public DubboResponse<List<MerchantStoreResultResp>> queryMerchantStoresByKeywords(MerchantBasicQueryReq merchantBasicQueryReq) {
        MerchantStoreQueryInput input = MerchantStoreReqConverter.basicToMerchantStoreQueryInput(merchantBasicQueryReq);
        // 参数校验
        MerchantStoreQueryInput.paramValidate(input, 200);
        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreResultRespList(queryService.getMerchantStores(input)));
    }
}
