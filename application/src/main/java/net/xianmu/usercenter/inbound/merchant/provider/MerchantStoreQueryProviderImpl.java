package net.xianmu.usercenter.inbound.merchant.provider;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreConverterV2;
import net.xianmu.usercenter.inbound.page.PageConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:01
 */
@DubboService
@Slf4j
public class MerchantStoreQueryProviderImpl implements MerchantStoreQueryProvider {
    @Autowired
    private MerchantStoreQueryService queryService;


    @Override
    public DubboResponse<MerchantStoreResultResp> getMerchantStoreById(Long id) {
        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreResultResp(queryService.getMerchantStoreById(id)));
    }

    @Override
    public DubboResponse<List<MerchantStoreResultResp>> getMerchantStoreByIds(List<Long> idList) {
        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreResultRespList(queryService.getMerchantStoresByIds(idList)));
    }

    @Override
    @Deprecated
    public DubboResponse<List<MerchantStoreResultResp>> getMerchantStoresByPrimaryKeys(MerchantStoreQueryReq merchantStoreQueryReq) {
        boolean paramExist = ValidateUtil.keyParamExist(merchantStoreQueryReq, "storeId", "storeIdList", "mId", "mIds", "phone", "storeNo", "storeNoList", "channelCode", "exactStoreName", "storeName", "exactStoreNameList");
        if(!paramExist) {
            log.warn("请求核心参数缺失!");
            return DubboResponse.getOK(Collections.emptyList());
        }
        MerchantStoreQueryInput input = MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq);

        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreResultRespList(queryService.getMerchantStores(input)));
    }

    @Override
    @Deprecated
    public DubboResponse<List<MerchantStoreResultResp>> getMerchantStores(MerchantStoreQueryReq merchantStoreQueryReq) {
        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreResultRespList(queryService.getMerchantStores(MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq))));
    }



    @Override
    public DubboResponse<PageInfo<MerchantStoreAndAddressResultResp>> getMerchantStoreAndAddressPage(MerchantStorePageQueryReq merchantStorePageQueryReq, PageQueryReq pageQueryReq) {
        final PageInfo<MerchantStoreDTO> page = queryService.getMerchantStoreAndAddressPage(MerchantStoreReqConverter.toMerchantStorePageQueryInput(merchantStorePageQueryReq), PageConverter.toPageQueryInput(pageQueryReq));
        return DubboResponse.getOK(PageInfoConverter.toPageResp(page, MerchantStoreConverter::toMerchantStoreAndAddressResultResp));
    }

    @Override
    public DubboResponse<PageInfo<MerchantStorePageResultResp>> getMerchantStorePage(MerchantStorePageQueryReq merchantStorePageQueryReq, PageQueryReq pageQueryReq) {
        final PageInfo<MerchantStoreDTO> page = queryService.selectMerchantStorePage(MerchantStoreReqConverter.toMerchantStorePageQueryInput(merchantStorePageQueryReq), PageConverter.toPageQueryInput(pageQueryReq));
        return DubboResponse.getOK(PageInfoConverter.toPageResp(page, MerchantStoreConverter::toMerchantStorePageResultResp));
    }
}
