package net.xianmu.usercenter.inbound.merchant.provider;

import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreGroupDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreGroupQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreGroupConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreGroupConverterV2;
import net.xianmu.usercenter.inbound.page.PageConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 11:11
 */
@DubboService
public class MerchantStoreGroupQueryProviderImpl implements MerchantStoreGroupQueryProvider {

    @Autowired
    private MerchantStoreGroupQueryService merchantStoreGroupQueryService;

    @Override
    public DubboResponse<List<MerchantStoreGroupResultResp>> getGroupByStoreIds(Long tenantId, List<Long> storeIdList) {
        return DubboResponse.getOK(MerchantStoreGroupConverter.toMerchantStoreGroupResultRespList(merchantStoreGroupQueryService.getGroupByStoreIds(tenantId, storeIdList)));

    }

    @Override
    public DubboResponse<List<MerchantStoreGroupResultResp>> getGroupByStoreGroupIds(Long tenantId, List<Long> groupIdList) {
        return DubboResponse.getOK(MerchantStoreGroupConverter.toMerchantStoreGroupResultRespList(merchantStoreGroupQueryService.getGroupByGroupIds(tenantId, groupIdList)));
    }

    @Override
    public DubboResponse<List<MerchantStoreGroupPageResultResp>> getMerchantStoreGroups(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq) {
        return DubboResponse.getOK(MerchantStoreGroupConverter.toMerchantStoreGroupPageResultRespList(merchantStoreGroupQueryService.getMerchantStoreGroups(MerchantStoreGroupConverter.toMerchantStoreGroupQueryInput(merchantStoreGroupQueryReq))));
    }

    @Override
    public DubboResponse<List<MerchantStoreGroupPageResultResp>> getGroupsWithStoreCount(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq) {
        return DubboResponse.getOK(MerchantStoreGroupConverterV2.toMerchantStoreGroupPageResultRespList(merchantStoreGroupQueryService.getGroupsWithStoreCount(MerchantStoreGroupConverter.toMerchantStoreGroupQueryInput(merchantStoreGroupQueryReq))));
    }

    @Override
    public DubboResponse<PageInfo<MerchantStoreGroupPageResultResp>> getMerchantStoreGroupPage(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq, PageQueryReq pageQueryReq) {
        final PageInfo<MerchantStoreGroupDTO> merchantStoreGroupPage = merchantStoreGroupQueryService.getMerchantStoreGroupPage(MerchantStoreGroupConverter.toMerchantStoreGroupQueryInput(merchantStoreGroupQueryReq), PageConverter.toPageQueryInput(pageQueryReq));
        return DubboResponse.getOK(PageInfoConverter.toPageResp(merchantStoreGroupPage, MerchantStoreGroupConverter::toMerchantStoreGroupPageResultResp));
    }
}