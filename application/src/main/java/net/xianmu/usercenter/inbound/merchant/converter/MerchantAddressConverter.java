package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantDeliveryAddressResultResp;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public class MerchantAddressConverter {


    private MerchantAddressConverter() {
        // 无需实现
    }

    public static List<MerchantAddressResultResp> toMerchantAddressResultRespList(List<MerchantAddressDTO> merchantAddressDTOList) {
        if (merchantAddressDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressResultResp> merchantAddressResultRespList = new ArrayList<>();
        for (MerchantAddressDTO merchantAddressDTO : merchantAddressDTOList) {
            merchantAddressResultRespList.add(toMerchantAddressResultResp(merchantAddressDTO));
        }
        return merchantAddressResultRespList;
    }

    public static MerchantAddressResultResp toMerchantAddressResultResp(MerchantAddressDTO merchantAddressDTO) {
        if (merchantAddressDTO == null) {
            return null;
        }
        MerchantAddressResultResp merchantAddressResultResp = new MerchantAddressResultResp();
        merchantAddressResultResp.setId(merchantAddressDTO.getId());
        merchantAddressResultResp.setTenantId(merchantAddressDTO.getTenantId());
        merchantAddressResultResp.setStoreId(merchantAddressDTO.getStoreId());
        merchantAddressResultResp.setProvince(merchantAddressDTO.getProvince());
        merchantAddressResultResp.setCity(merchantAddressDTO.getCity());
        merchantAddressResultResp.setArea(merchantAddressDTO.getArea());
        merchantAddressResultResp.setAddress(merchantAddressDTO.getAddress());
        merchantAddressResultResp.setHouseNumber(merchantAddressDTO.getHouseNumber());
        merchantAddressResultResp.setPoiNote(merchantAddressDTO.getPoiNote());
        merchantAddressResultResp.setCreateTime(merchantAddressDTO.getCreateTime());
        merchantAddressResultResp.setUpdateTime(merchantAddressDTO.getUpdateTime());
        merchantAddressResultResp.setDefaultFlag(merchantAddressDTO.getDefaultFlag());
        merchantAddressResultResp.setStatus(merchantAddressDTO.getStatus());
        return merchantAddressResultResp;
    }


    public static List<MerchantDeliveryAddressResultResp> toMerchantDeliveryAddressResultRespList(List<MerchantAddressDTO> merchantAddressDTOList) {
        if (merchantAddressDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantDeliveryAddressResultResp> merchantAddressResultRespList = new ArrayList<>();
        for (MerchantAddressDTO merchantAddressDTO : merchantAddressDTOList) {
            merchantAddressResultRespList.add(toMerchantDeliveryAddressResultResp(merchantAddressDTO));
        }
        return merchantAddressResultRespList;
    }

    public static MerchantDeliveryAddressResultResp toMerchantDeliveryAddressResultResp(MerchantAddressDTO merchantAddressDTO) {
        if (merchantAddressDTO == null) {
            return null;
        }
        MerchantDeliveryAddressResultResp resp = new MerchantDeliveryAddressResultResp();
        resp.setId(merchantAddressDTO.getId());
        resp.setTenantId(merchantAddressDTO.getTenantId());
        resp.setStoreId(merchantAddressDTO.getStoreId());
        resp.setProvince(merchantAddressDTO.getProvince());
        resp.setCity(merchantAddressDTO.getCity());
        resp.setArea(merchantAddressDTO.getArea());
        resp.setAddress(merchantAddressDTO.getAddress());
        resp.setHouseNumber(merchantAddressDTO.getHouseNumber());
        resp.setPoiNote(merchantAddressDTO.getPoiNote());
        resp.setContactName(merchantAddressDTO.getContactName());
        resp.setContactPhone(merchantAddressDTO.getContactPhone());
        resp.setDefaultFlag(merchantAddressDTO.getDefaultFlag());
        resp.setStatus(merchantAddressDTO.getStatus());
        resp.setDeliveryAddress(merchantAddressDTO.getDeliveryAddress());
        return resp;
    }


    /**
     * ************************* MerchantAddressQueryInput ******************************
     */


    public static MerchantAddressQueryInput toMerchantAddressQueryInput(MerchantAddressQueryReq merchantAddressQueryReq) {
        if (merchantAddressQueryReq == null) {
            return null;
        }
        MerchantAddressQueryInput merchantAddressQueryInput = new MerchantAddressQueryInput();
        merchantAddressQueryInput.setId(merchantAddressQueryReq.getId());
        merchantAddressQueryInput.setTenantId(merchantAddressQueryReq.getTenantId());
        merchantAddressQueryInput.setStoreId(merchantAddressQueryReq.getStoreId());
        merchantAddressQueryInput.setStoreIdList(merchantAddressQueryReq.getStoreIdList());
        merchantAddressQueryInput.setProvince(merchantAddressQueryReq.getProvince());
        merchantAddressQueryInput.setCity(merchantAddressQueryReq.getCity());
        merchantAddressQueryInput.setCityList(merchantAddressQueryReq.getCityList());
        merchantAddressQueryInput.setArea(merchantAddressQueryReq.getArea());
        merchantAddressQueryInput.setAreaList(merchantAddressQueryReq.getAreaList());
        merchantAddressQueryInput.setDefaultFlag(merchantAddressQueryReq.getDefaultFlag());
        merchantAddressQueryInput.setStatus(merchantAddressQueryReq.getStatus());
        merchantAddressQueryInput.setXmContactId(merchantAddressQueryReq.getXmContactId());
        merchantAddressQueryInput.setMId(merchantAddressQueryReq.getMId());
        merchantAddressQueryInput.setXmContactIdList(merchantAddressQueryReq.getXmContactIdList());
        merchantAddressQueryInput.setPageIndex(merchantAddressQueryReq.getPageIndex());
        merchantAddressQueryInput.setPageSize(merchantAddressQueryReq.getPageSize());
        return merchantAddressQueryInput;
    }



    public static List<MerchantAddressCommandInput> toMerchantAddressCommandInputList(List<MerchantAddressCommandReq> list, Integer systemOrigin) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressCommandInput> resultList = new ArrayList<>();
        for (MerchantAddressCommandReq dto : list) {
            resultList.add(toMerchantAddressCommandInput(dto, systemOrigin));
        }
        return resultList;
    }

    public static MerchantAddressCommandInput toMerchantAddressCommandInput(MerchantAddressCommandReq merchantAddressCommandReq, Integer systemOrigin) {
        if (merchantAddressCommandReq == null) {
            return null;
        }
        MerchantAddressCommandInput merchantAddressCommandInput = new MerchantAddressCommandInput();
        merchantAddressCommandInput.setId(merchantAddressCommandReq.getId());
        merchantAddressCommandInput.setTenantId(merchantAddressCommandReq.getTenantId());
        merchantAddressCommandInput.setStoreId(merchantAddressCommandReq.getStoreId());
        merchantAddressCommandInput.setProvince(merchantAddressCommandReq.getProvince());
        merchantAddressCommandInput.setCity(merchantAddressCommandReq.getCity());
        merchantAddressCommandInput.setArea(merchantAddressCommandReq.getArea());
        merchantAddressCommandInput.setAddress(merchantAddressCommandReq.getAddress());
        merchantAddressCommandInput.setHouseNumber(merchantAddressCommandReq.getHouseNumber());
        merchantAddressCommandInput.setPoiNote(merchantAddressCommandReq.getPoiNote());
        merchantAddressCommandInput.setCreateTime(merchantAddressCommandReq.getCreateTime());
        merchantAddressCommandInput.setUpdateTime(merchantAddressCommandReq.getUpdateTime());
        merchantAddressCommandInput.setDefaultFlag(merchantAddressCommandReq.getDefaultFlag());
        merchantAddressCommandInput.setStatus(merchantAddressCommandReq.getStatus());
        merchantAddressCommandInput.setSystemOrigin(systemOrigin);
        merchantAddressCommandInput.setAddressRemark(merchantAddressCommandReq.getAddressRemark());
        merchantAddressCommandInput.setMerchantContactList(MerchantContactConverter.toMerchantContactCommandInputList(merchantAddressCommandReq.getMerchantContactList(), systemOrigin));
        return merchantAddressCommandInput;
    }


}
