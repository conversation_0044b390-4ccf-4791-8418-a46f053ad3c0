package net.xianmu.usercenter.inbound.merchant.provider;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreQueryService;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreConverterV2;
import net.xianmu.usercenter.inbound.page.PageConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton 门店拓展属性查询接口
 * @date 2023/5/5 11:01
 */
@Slf4j
@DubboService
public class MerchantExtendQueryProviderImpl implements MerchantExtendQueryProvider {
    @Autowired
    private MerchantStoreQueryService merchantStoreQueryService;


    @Override
    @Deprecated
    public DubboResponse<List<MerchantStoreAndExtendResp>> getMerchantStoreAndExtends(MerchantStoreQueryReq merchantStoreQueryReq) {
        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreAndExtendRespList(merchantStoreQueryService.getMerchantStoreAndExtends(MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq))));
    }

    @Override
    public DubboResponse<List<MerchantStoreAndExtendResp>> getMerchantStoreAndExtendsByPrimaryKeys(MerchantStoreQueryReq merchantStoreQueryReq) {
        ValidateUtil.paramValidate(merchantStoreQueryReq, "tenantId");
        boolean paramExist = ValidateUtil.keyParamExist(merchantStoreQueryReq, "storeId", "storeIdList", "mId", "mIds", "phone", "storeNo", "storeNoList", "channelCode", "exactStoreName", "storeName", "exactStoreNameList");
        if(!paramExist) {
            log.warn("请求核心参数缺失!");
            return DubboResponse.getOK(Collections.emptyList());
        }

        return DubboResponse.getOK(MerchantStoreConverterV2.toMerchantStoreAndExtendRespList(merchantStoreQueryService.getMerchantStoreAndExtends(MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq))));
    }

    @Override
    public DubboResponse<List<Long>> getMidList(MerchantStoreQueryReq merchantStoreQueryReq) {
        return DubboResponse.getOK(merchantStoreQueryService.selectMIdListByCondition(MerchantStoreReqConverter.toMerchantStoreQueryInput(merchantStoreQueryReq)));
    }


    @Override
    public DubboResponse<PageInfo<XmMerchantStorePageResp>> getMerchantStorePageForXM(MerchantStorePageQueryReq queryReq) {
        PageInfo<MerchantStoreEntity> page = merchantStoreQueryService.selectMerchantStorePageForXM(MerchantStoreReqConverter.toMerchantStorePageQueryInput(queryReq));
        return DubboResponse.getOK(PageInfoConverter.toPageResp(page, MerchantStoreConverterV2::toXmMerchantStorePageResp));
    }
}
