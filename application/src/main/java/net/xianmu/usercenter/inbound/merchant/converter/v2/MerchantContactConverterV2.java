package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
public class MerchantContactConverterV2 {


    private MerchantContactConverterV2() {
        // 无需实现
    }

    public static List<MerchantContactResultResp> toMerchantContactResultRespList(List<MerchantContactEntity> merchantContactEntityList) {
        if (merchantContactEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactResultResp> merchantContactResultRespList = new ArrayList<>();
        for (MerchantContactEntity merchantContactEntity : merchantContactEntityList) {
            merchantContactResultRespList.add(toMerchantContactResultResp(merchantContactEntity));
        }
        return merchantContactResultRespList;
    }

    public static MerchantContactResultResp toMerchantContactResultResp(MerchantContactEntity merchantContactEntity) {
        if (merchantContactEntity == null) {
            return null;
        }
        MerchantContactResultResp merchantContactResultResp = new MerchantContactResultResp();
        merchantContactResultResp.setId(merchantContactEntity.getId());
        merchantContactResultResp.setTenantId(merchantContactEntity.getTenantId());
        merchantContactResultResp.setAddressId(merchantContactEntity.getAddressId());
        merchantContactResultResp.setName(merchantContactEntity.getName());
        merchantContactResultResp.setPhone(merchantContactEntity.getPhone());
        merchantContactResultResp.setDefaultFlag(merchantContactEntity.getDefaultFlag());
        merchantContactResultResp.setCreateTime(merchantContactEntity.getCreateTime());
        merchantContactResultResp.setUpdateTime(merchantContactEntity.getUpdateTime());
        return merchantContactResultResp;
    }

    public static List<MerchantContactEntity> toMerchantContactEntityList(List<MerchantContactResultResp> merchantContactResultRespList) {
        if (merchantContactResultRespList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactEntity> merchantContactEntityList = new ArrayList<>();
        for (MerchantContactResultResp merchantContactResultResp : merchantContactResultRespList) {
            merchantContactEntityList.add(toMerchantContactEntity(merchantContactResultResp));
        }
        return merchantContactEntityList;
    }

    public static MerchantContactEntity toMerchantContactEntity(MerchantContactResultResp merchantContactResultResp) {
        if (merchantContactResultResp == null) {
            return null;
        }
        MerchantContactEntity merchantContactEntity = new MerchantContactEntity();
        merchantContactEntity.setId(merchantContactResultResp.getId());
        merchantContactEntity.setTenantId(merchantContactResultResp.getTenantId());
        merchantContactEntity.setAddressId(merchantContactResultResp.getAddressId());
        merchantContactEntity.setName(merchantContactResultResp.getName());
        merchantContactEntity.setPhone(merchantContactResultResp.getPhone());
        merchantContactEntity.setDefaultFlag(merchantContactResultResp.getDefaultFlag());
        merchantContactEntity.setCreateTime(merchantContactResultResp.getCreateTime());
        merchantContactEntity.setUpdateTime(merchantContactResultResp.getUpdateTime());
        return merchantContactEntity;
    }
}
