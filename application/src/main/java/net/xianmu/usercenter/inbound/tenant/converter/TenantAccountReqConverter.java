package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountListQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 15:32
 */
public class TenantAccountReqConverter {


    private TenantAccountReqConverter() {
        // 无需实现
    }

    public static TenantAccountListQueryInput toTenantAccountListQueryInput(TenantAccountListQueryReq tenantAccountListQueryReq) {
        if (tenantAccountListQueryReq == null) {
            return null;
        }
        TenantAccountListQueryInput tenantAccountListQueryInput = new TenantAccountListQueryInput();
        tenantAccountListQueryInput.setNickId(tenantAccountListQueryReq.getNickId());
        tenantAccountListQueryInput.setPhone(tenantAccountListQueryReq.getPhone());
        tenantAccountListQueryInput.setNickName(tenantAccountListQueryReq.getNickName());
        tenantAccountListQueryInput.setStatus(tenantAccountListQueryReq.getStatus());
        tenantAccountListQueryInput.setRoleId(tenantAccountListQueryReq.getRoleId());
        tenantAccountListQueryInput.setTenantId(tenantAccountListQueryReq.getTenantId());
        tenantAccountListQueryInput.setAuthUserIds(tenantAccountListQueryReq.getAuthUserIds());
        tenantAccountListQueryInput.setUsername(tenantAccountListQueryReq.getUsername());
        return tenantAccountListQueryInput;
    }


    public static TenantAccountListQueryInput toTenantAccountListQueryInput(TenantAccountQueryReq queryReq) {
        if (queryReq == null) {
            return null;
        }
        TenantAccountListQueryInput tenantAccountListQueryInput = new TenantAccountListQueryInput();
        tenantAccountListQueryInput.setPhone(queryReq.getPhone());
        tenantAccountListQueryInput.setStatus(queryReq.getStatus());
        tenantAccountListQueryInput.setTenantId(queryReq.getTenantId());
        tenantAccountListQueryInput.setNickName(queryReq.getNickName());
        tenantAccountListQueryInput.setDeletedFlag(queryReq.getDeletedFlag());
        tenantAccountListQueryInput.setIdList(queryReq.getIdList());
        tenantAccountListQueryInput.setEmail(queryReq.getEmail());
        return tenantAccountListQueryInput;
    }


    public static TenantAccountCommandInput toTenantAccountCommandInput(TenantAccountCommandReq tenantAccountCommandReq) {
        if (tenantAccountCommandReq == null) {
            return null;
        }
        TenantAccountCommandInput tenantAccountCommandInput = new TenantAccountCommandInput();
        tenantAccountCommandInput.setId(tenantAccountCommandReq.getId());
        tenantAccountCommandInput.setAuthUserId(tenantAccountCommandReq.getAuthUserId());
        tenantAccountCommandInput.setTenantId(tenantAccountCommandReq.getTenantId());
        tenantAccountCommandInput.setPhone(tenantAccountCommandReq.getPhone());
        tenantAccountCommandInput.setLoginPassword(tenantAccountCommandReq.getLoginPassword());
        tenantAccountCommandInput.setNickname(tenantAccountCommandReq.getNickname());
        tenantAccountCommandInput.setProfilePicture(tenantAccountCommandReq.getProfilePicture());
        tenantAccountCommandInput.setRoleIds(tenantAccountCommandReq.getRoleIds());
        tenantAccountCommandInput.setStatus(tenantAccountCommandReq.getStatus());
        tenantAccountCommandInput.setOperatorTime(tenantAccountCommandReq.getOperatorTime());
        tenantAccountCommandInput.setOperatorPhone(tenantAccountCommandReq.getOperatorPhone());
        tenantAccountCommandInput.setSystemOrigin(tenantAccountCommandReq.getSystemOrigin());
        tenantAccountCommandInput.setEmail(tenantAccountCommandReq.getEmail());
        tenantAccountCommandInput.setUpdater(tenantAccountCommandReq.getUpdater());
        return tenantAccountCommandInput;
    }
}
