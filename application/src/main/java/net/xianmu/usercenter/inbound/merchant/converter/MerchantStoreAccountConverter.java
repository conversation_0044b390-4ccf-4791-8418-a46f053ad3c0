package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreAccountDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreAccountConverter {


    private MerchantStoreAccountConverter() {
        // 无需实现
    }



    public static List<MerchantStoreAccountResultResp> toMerchantStoreAccountResultRespList(List<MerchantStoreAccountDTO> merchantStoreAccountDTOList) {
        if (merchantStoreAccountDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultRespList = new ArrayList<>();
        for (MerchantStoreAccountDTO merchantStoreAccountDTO : merchantStoreAccountDTOList) {
            merchantStoreAccountResultRespList.add(toMerchantStoreAccountResultResp(merchantStoreAccountDTO));
        }
        return merchantStoreAccountResultRespList;
    }

    public static MerchantStoreAccountResultResp toMerchantStoreAccountResultResp(MerchantStoreAccountDTO merchantStoreAccountDTO) {
        if (merchantStoreAccountDTO == null) {
            return null;
        }
        MerchantStoreAccountResultResp merchantStoreAccountResultResp = new MerchantStoreAccountResultResp();
        merchantStoreAccountResultResp.setId(merchantStoreAccountDTO.getId());
        merchantStoreAccountResultResp.setTenantId(merchantStoreAccountDTO.getTenantId());
        merchantStoreAccountResultResp.setStoreId(merchantStoreAccountDTO.getStoreId());
        merchantStoreAccountResultResp.setAccountName(merchantStoreAccountDTO.getAccountName());
        merchantStoreAccountResultResp.setPhone(merchantStoreAccountDTO.getPhone());
        merchantStoreAccountResultResp.setType(merchantStoreAccountDTO.getType());
        merchantStoreAccountResultResp.setRegisterTime(merchantStoreAccountDTO.getRegisterTime());
        merchantStoreAccountResultResp.setAuditTime(merchantStoreAccountDTO.getAuditTime());
        merchantStoreAccountResultResp.setOpenId(merchantStoreAccountDTO.getOpenId());
        merchantStoreAccountResultResp.setUnionId(merchantStoreAccountDTO.getUnionId());
        merchantStoreAccountResultResp.setStatus(merchantStoreAccountDTO.getStatus());
        merchantStoreAccountResultResp.setCreateTime(merchantStoreAccountDTO.getCreateTime());
        merchantStoreAccountResultResp.setUpdateTime(merchantStoreAccountDTO.getUpdateTime());
        merchantStoreAccountResultResp.setLastLoginTime(merchantStoreAccountDTO.getLastLoginTime());
        merchantStoreAccountResultResp.setDeleteFlag(merchantStoreAccountDTO.getDeleteFlag());
        merchantStoreAccountResultResp.setOaOpenId(merchantStoreAccountDTO.getOaOpenId());
        merchantStoreAccountResultResp.setXmAccountId(merchantStoreAccountDTO.getXmAccountId());
        return merchantStoreAccountResultResp;
    }
}
