package net.xianmu.usercenter.inbound.merchant.provider;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantStorePropertiesExtDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantStorePropertiesExtQueryService;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreQueryService;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreExtResp;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStorePropertiesExtConverter;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreReqConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreConverterV2;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton 门店拓展属性查询接口
 * @date 2023/5/5 11:01
 */
@Slf4j
@DubboService
public class MerchantStorePropertiesExtQueryProviderImpl implements MerchantStoreExtQueryProvider {

    @Autowired
    private MerchantStorePropertiesExtQueryService queryService;

    @Override
    public DubboResponse<List<MerchantStoreExtResp>> getMerchantStoreExtByQuery(MerchantStoreExtQueryReq merchantStoreExtQueryReq) {
       if (CollectionUtils.isEmpty(merchantStoreExtQueryReq.getMIds())){
           throw new BizException("参数mids不能为空");
       }
        if (StringUtils.isEmpty(merchantStoreExtQueryReq.getProKey())){
            throw new BizException("参数proKey不能为空");
        }
        List<MerchantStorePropertiesExtDTO> merchantStorePropertiesExtDTOS = queryService.queryByMidsKey(merchantStoreExtQueryReq.getMIds(), merchantStoreExtQueryReq.getProKey());
        List<MerchantStoreExtResp> collect = merchantStorePropertiesExtDTOS.stream().map(MerchantStorePropertiesExtConverter::convertMerchantStoreExtResp).collect(Collectors.toList());
        return DubboResponse.getOK(collect);
    }
}
