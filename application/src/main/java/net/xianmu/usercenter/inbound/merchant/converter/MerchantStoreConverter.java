package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.common.dto.MerchantStoreBatchImportDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreConverter {


    private MerchantStoreConverter() {
        // 无需实现
    }

    public static List<MerchantStoreResultResp> toMerchantStoreResultRespList(List<MerchantStoreDTO> merchantStoreDTOList) {
        if (merchantStoreDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreResultResp> merchantStoreResultRespList = new ArrayList<>();
        for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOList) {
            merchantStoreResultRespList.add(toMerchantStoreResultResp(merchantStoreDTO));
        }
        return merchantStoreResultRespList;
    }

    public static MerchantStoreResultResp toMerchantStoreResultResp(MerchantStoreDTO merchantStoreDTO) {
        if (merchantStoreDTO == null) {
            return null;
        }
        MerchantStoreResultResp merchantStoreResultResp = new MerchantStoreResultResp();
        merchantStoreResultResp.setId(merchantStoreDTO.getId());
        merchantStoreResultResp.setTenantId(merchantStoreDTO.getTenantId());
        merchantStoreResultResp.setStoreName(merchantStoreDTO.getStoreName());
        merchantStoreResultResp.setType(merchantStoreDTO.getType());
        merchantStoreResultResp.setRegisterTime(merchantStoreDTO.getRegisterTime());
        merchantStoreResultResp.setStatus(merchantStoreDTO.getStatus());
        merchantStoreResultResp.setAuditRemark(merchantStoreDTO.getAuditRemark());
        merchantStoreResultResp.setRemark(merchantStoreDTO.getRemark());
        merchantStoreResultResp.setAuditTime(merchantStoreDTO.getAuditTime());
        merchantStoreResultResp.setCreateTime(merchantStoreDTO.getCreateTime());
        merchantStoreResultResp.setUpdateTime(merchantStoreDTO.getUpdateTime());
        merchantStoreResultResp.setBillSwitch(merchantStoreDTO.getBillSwitch());
        merchantStoreResultResp.setOnlinePayment(merchantStoreDTO.getOnlinePayment());
        merchantStoreResultResp.setStoreNo(merchantStoreDTO.getStoreNo());
        merchantStoreResultResp.setBalanceAuthority(merchantStoreDTO.getBalanceAuthority());
        return merchantStoreResultResp;
    }


    public static List<MerchantStoreAndAddressResultResp> toMerchantStoreAndAddressResultRespList(List<MerchantStoreDTO> merchantStoreDTOList) {
        if (merchantStoreDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAndAddressResultResp> merchantStoreAndAddressResultRespList = new ArrayList<>();
        for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOList) {
            merchantStoreAndAddressResultRespList.add(toMerchantStoreAndAddressResultResp(merchantStoreDTO));
        }
        return merchantStoreAndAddressResultRespList;
    }

    public static MerchantStoreAndAddressResultResp toMerchantStoreAndAddressResultResp(MerchantStoreDTO merchantStoreDTO) {
        if (merchantStoreDTO == null) {
            return null;
        }
        MerchantStoreAndAddressResultResp merchantStoreAndAddressResultResp = new MerchantStoreAndAddressResultResp();
        merchantStoreAndAddressResultResp.setId(merchantStoreDTO.getId());
        merchantStoreAndAddressResultResp.setTenantId(merchantStoreDTO.getTenantId());
        merchantStoreAndAddressResultResp.setStoreName(merchantStoreDTO.getStoreName());
        merchantStoreAndAddressResultResp.setType(merchantStoreDTO.getType());
        merchantStoreAndAddressResultResp.setRegisterTime(merchantStoreDTO.getRegisterTime());
        merchantStoreAndAddressResultResp.setStatus(merchantStoreDTO.getStatus());
        merchantStoreAndAddressResultResp.setAuditRemark(merchantStoreDTO.getAuditRemark());
        merchantStoreAndAddressResultResp.setRemark(merchantStoreDTO.getRemark());
        merchantStoreAndAddressResultResp.setAuditTime(merchantStoreDTO.getAuditTime());
        merchantStoreAndAddressResultResp.setCreateTime(merchantStoreDTO.getCreateTime());
        merchantStoreAndAddressResultResp.setUpdateTime(merchantStoreDTO.getUpdateTime());
        merchantStoreAndAddressResultResp.setBillSwitch(merchantStoreDTO.getBillSwitch());
        merchantStoreAndAddressResultResp.setOnlinePayment(merchantStoreDTO.getOnlinePayment());
        merchantStoreAndAddressResultResp.setBalanceAuthority(merchantStoreDTO.getBalanceAuthority());
        merchantStoreAndAddressResultResp.setStoreNo(merchantStoreDTO.getStoreNo());
        merchantStoreAndAddressResultResp.setProvince(merchantStoreDTO.getProvince());
        merchantStoreAndAddressResultResp.setCity(merchantStoreDTO.getCity());
        merchantStoreAndAddressResultResp.setArea(merchantStoreDTO.getArea());
        merchantStoreAndAddressResultResp.setAddress(merchantStoreDTO.getAddress());
        merchantStoreAndAddressResultResp.setHouseNumber(merchantStoreDTO.getHouseNumber());
        merchantStoreAndAddressResultResp.setDeliveryAddress(merchantStoreDTO.getDeliveryAddress());
        return merchantStoreAndAddressResultResp;
    }


    public static MerchantStorePageResultResp toMerchantStorePageResultResp(MerchantStoreDTO merchantStoreDTO) {
        if (merchantStoreDTO == null) {
            return null;
        }
        MerchantStorePageResultResp resp = new MerchantStorePageResultResp();
        resp.setId(merchantStoreDTO.getId());
        resp.setTenantId(merchantStoreDTO.getTenantId());
        resp.setStoreName(merchantStoreDTO.getStoreName());
        resp.setType(merchantStoreDTO.getType());
        resp.setRegisterTime(merchantStoreDTO.getRegisterTime());
        resp.setStatus(merchantStoreDTO.getStatus());
        resp.setAuditRemark(merchantStoreDTO.getAuditRemark());
        resp.setRemark(merchantStoreDTO.getRemark());
        resp.setAuditTime(merchantStoreDTO.getAuditTime());
        resp.setCreateTime(merchantStoreDTO.getCreateTime());
        resp.setUpdateTime(merchantStoreDTO.getUpdateTime());
        resp.setBillSwitch(merchantStoreDTO.getBillSwitch());
        resp.setOnlinePayment(merchantStoreDTO.getOnlinePayment());
        resp.setBalanceAuthority(merchantStoreDTO.getBalanceAuthority());
        resp.setStoreNo(merchantStoreDTO.getStoreNo());
        resp.setPhone(merchantStoreDTO.getPhone());
        resp.setPlaceOrderPermissionTimeLimited(merchantStoreDTO.getPlaceOrderPermissionTimeLimited ());
        resp.setPlaceOrderPermissionExpiryTime(merchantStoreDTO.getPlaceOrderPermissionExpiryTime ());
        resp.setEnableOfflinePayment (merchantStoreDTO.getEnableOfflinePayment ());
        return resp;
    }


    public static List<MerchantStoreBatchImportResp> toMerchantStoreBatchImportRespList(List<MerchantStoreBatchImportDTO> merchantStoreBatchImportDTOList) {
        if (merchantStoreBatchImportDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreBatchImportResp> merchantStoreBatchImportRespList = new ArrayList<>();
        for (MerchantStoreBatchImportDTO merchantStoreBatchImportDTO : merchantStoreBatchImportDTOList) {
            merchantStoreBatchImportRespList.add(toMerchantStoreBatchImportResp(merchantStoreBatchImportDTO));
        }
        return merchantStoreBatchImportRespList;
    }

    public static MerchantStoreBatchImportResp toMerchantStoreBatchImportResp(MerchantStoreBatchImportDTO merchantStoreBatchImportDTO) {
        if (merchantStoreBatchImportDTO == null) {
            return null;
        }
        MerchantStoreBatchImportResp merchantStoreBatchImportResp = new MerchantStoreBatchImportResp();
        merchantStoreBatchImportResp.setRowNum(merchantStoreBatchImportDTO.getRowNum());
        merchantStoreBatchImportResp.setStoreName(merchantStoreBatchImportDTO.getStoreName());
        merchantStoreBatchImportResp.setStoreId(merchantStoreBatchImportDTO.getStoreId());
        merchantStoreBatchImportResp.setErrorDesc(merchantStoreBatchImportDTO.getErrorDesc());
        merchantStoreBatchImportResp.setTenantId(merchantStoreBatchImportDTO.getTenantId());
        return merchantStoreBatchImportResp;
    }


}
