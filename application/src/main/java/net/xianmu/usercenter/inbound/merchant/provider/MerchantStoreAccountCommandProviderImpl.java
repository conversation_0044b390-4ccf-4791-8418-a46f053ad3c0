package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreAccountCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantStoreAccountReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-05-31 11:24:03
 */
@DubboService
public class MerchantStoreAccountCommandProviderImpl implements MerchantStoreAccountCommandProvider {
    @Autowired
    private MerchantStoreAccountCommandService merchantStoreAccountCommandService;

    @Override
    public DubboResponse<Long> create(SystemOriginEnum systemOriginEnum, MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        Long id = merchantStoreAccountCommandService.create(MerchantStoreAccountReqConverter.toMerchantStoreAccountCommandInput(merchantStoreAccountCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<Boolean> update(SystemOriginEnum systemOriginEnum, MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        Boolean success = merchantStoreAccountCommandService.update(MerchantStoreAccountReqConverter.toMerchantStoreAccountCommandInput(merchantStoreAccountCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> remove(SystemOriginEnum systemOriginEnum, MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        Boolean success = merchantStoreAccountCommandService.remove(MerchantStoreAccountReqConverter.toMerchantStoreAccountCommandInput(merchantStoreAccountCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> removeBatch(SystemOriginEnum systemOriginEnum, MerchantStoreAccountCommandReq merchantStoreAccountCommandReq) {
        Boolean success = merchantStoreAccountCommandService.removeBatch(MerchantStoreAccountReqConverter.toMerchantStoreAccountCommandInput(merchantStoreAccountCommandReq, systemOriginEnum.getType()));
        return DubboResponse.getOK(success);
    }

    @Override
    public DubboResponse<Boolean> updateStatusBatch(SystemOriginEnum systemOriginEnum, List<Long> idList, Integer status) {
        Boolean success = merchantStoreAccountCommandService.updateStatusBatch(idList, status);
        return DubboResponse.getOK(success);
    }
}
