package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStoreGroupDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupBatchImportReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreGroupQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.common.dto.MerchantStoreGroupBatchImportDTO;
import net.xianmu.usercenter.common.input.command.MerchantStoreGroupCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
public class MerchantStoreGroupConverter {


    private MerchantStoreGroupConverter() {
        // 无需实现
    }

    public static List<MerchantStoreGroupResultResp> toMerchantStoreGroupResultRespList(List<MerchantStoreGroupDTO> merchantStoreGroupDTOList) {
        if (merchantStoreGroupDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupResultResp> merchantStoreGroupResultRespList = new ArrayList<>();
        for (MerchantStoreGroupDTO merchantStoreGroupDTO : merchantStoreGroupDTOList) {
            merchantStoreGroupResultRespList.add(toMerchantStoreGroupResultResp(merchantStoreGroupDTO));
        }
        return merchantStoreGroupResultRespList;
    }

    public static MerchantStoreGroupResultResp toMerchantStoreGroupResultResp(MerchantStoreGroupDTO merchantStoreGroupDTO) {
        if (merchantStoreGroupDTO == null) {
            return null;
        }
        MerchantStoreGroupResultResp merchantStoreGroupResultResp = new MerchantStoreGroupResultResp();
        merchantStoreGroupResultResp.setStoreId(merchantStoreGroupDTO.getStoreId());
        merchantStoreGroupResultResp.setMerchantStoreGroupName(merchantStoreGroupDTO.getMerchantStoreGroupName());
        merchantStoreGroupResultResp.setMerchantStoreGroupId(merchantStoreGroupDTO.getMerchantStoreGroupId());
        return merchantStoreGroupResultResp;
    }




    public static List<MerchantStoreGroupPageResultResp> toMerchantStoreGroupPageResultRespList(List<MerchantStoreGroupDTO> merchantStoreGroupDTOList) {
        if (merchantStoreGroupDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupPageResultResp> merchantStoreGroupPageResultRespList = new ArrayList<>();
        for (MerchantStoreGroupDTO merchantStoreGroupDTO : merchantStoreGroupDTOList) {
            merchantStoreGroupPageResultRespList.add(toMerchantStoreGroupPageResultResp(merchantStoreGroupDTO));
        }
        return merchantStoreGroupPageResultRespList;
    }

    public static MerchantStoreGroupPageResultResp toMerchantStoreGroupPageResultResp(MerchantStoreGroupDTO merchantStoreGroupDTO) {
        if (merchantStoreGroupDTO == null) {
            return null;
        }
        MerchantStoreGroupPageResultResp merchantStoreGroupPageResultResp = new MerchantStoreGroupPageResultResp();
        merchantStoreGroupPageResultResp.setMerchantStoreGroupName(merchantStoreGroupDTO.getMerchantStoreGroupName());
        merchantStoreGroupPageResultResp.setMerchantStoreGroupId(merchantStoreGroupDTO.getMerchantStoreGroupId());
        merchantStoreGroupPageResultResp.setStoreNum(merchantStoreGroupDTO.getStoreNum());
        merchantStoreGroupPageResultResp.setCreateTime(merchantStoreGroupDTO.getCreateTime());
        merchantStoreGroupPageResultResp.setType(merchantStoreGroupDTO.getType());
        merchantStoreGroupPageResultResp.setUpdateTime(merchantStoreGroupDTO.getUpdateTime());
        return merchantStoreGroupPageResultResp;
    }




    /**
     * *************************** Input **************************
     */

    public static MerchantStoreGroupQueryInput toMerchantStoreGroupQueryInput(MerchantStoreGroupQueryReq merchantStoreGroupQueryReq) {
        if (merchantStoreGroupQueryReq == null) {
            return null;
        }
        MerchantStoreGroupQueryInput merchantStoreGroupQueryInput = new MerchantStoreGroupQueryInput();
        merchantStoreGroupQueryInput.setId(merchantStoreGroupQueryReq.getId());
        merchantStoreGroupQueryInput.setMerchantStoreGroupName(merchantStoreGroupQueryReq.getMerchantStoreGroupName());
        merchantStoreGroupQueryInput.setType(merchantStoreGroupQueryReq.getType());
        merchantStoreGroupQueryInput.setTenantId(merchantStoreGroupQueryReq.getTenantId());
        merchantStoreGroupQueryInput.setIdList(merchantStoreGroupQueryReq.getIdList());
        return merchantStoreGroupQueryInput;
    }


    public static MerchantStoreGroupCommandInput toMerchantStoreGroupCommandInput(MerchantStoreGroupCommandReq merchantStoreGroupCommandReq) {
        if (merchantStoreGroupCommandReq == null) {
            return null;
        }
        MerchantStoreGroupCommandInput merchantStoreGroupCommandInput = new MerchantStoreGroupCommandInput();
        merchantStoreGroupCommandInput.setId(merchantStoreGroupCommandReq.getId());
        merchantStoreGroupCommandInput.setTenantId(merchantStoreGroupCommandReq.getTenantId());
        merchantStoreGroupCommandInput.setName(merchantStoreGroupCommandReq.getName());
        merchantStoreGroupCommandInput.setType(merchantStoreGroupCommandReq.getType());
        merchantStoreGroupCommandInput.setStoreIdList(merchantStoreGroupCommandReq.getStoreIdList());
        return merchantStoreGroupCommandInput;
    }


    public static List<MerchantStoreGroupBatchImportResp> toMerchantStoreGroupBatchImportRespList(List<MerchantStoreGroupBatchImportDTO> merchantStoreGroupBatchImportDTOList) {
        if (merchantStoreGroupBatchImportDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupBatchImportResp> merchantStoreGroupBatchImportReqList = new ArrayList<>();
        for (MerchantStoreGroupBatchImportDTO merchantStoreGroupBatchImportDTO : merchantStoreGroupBatchImportDTOList) {
            merchantStoreGroupBatchImportReqList.add(toMerchantStoreGroupBatchImportResp(merchantStoreGroupBatchImportDTO));
        }
        return merchantStoreGroupBatchImportReqList;
    }

    public static MerchantStoreGroupBatchImportResp toMerchantStoreGroupBatchImportResp(MerchantStoreGroupBatchImportDTO merchantStoreGroupBatchImportDTO) {
        if (merchantStoreGroupBatchImportDTO == null) {
            return null;
        }
        MerchantStoreGroupBatchImportResp merchantStoreGroupBatchImportReq = new MerchantStoreGroupBatchImportResp();
        merchantStoreGroupBatchImportReq.setName(merchantStoreGroupBatchImportDTO.getName());
        merchantStoreGroupBatchImportReq.setStoreName(merchantStoreGroupBatchImportDTO.getStoreName());
        merchantStoreGroupBatchImportReq.setError(merchantStoreGroupBatchImportDTO.getError());
        return merchantStoreGroupBatchImportReq;
    }

    public static List<MerchantStoreGroupBatchImportDTO> toMerchantStoreGroupBatchImportDTOList(List<MerchantStoreGroupBatchImportReq> merchantStoreGroupBatchImportReqList) {
        if (merchantStoreGroupBatchImportReqList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupBatchImportDTO> merchantStoreGroupBatchImportDTOList = new ArrayList<>();
        for (MerchantStoreGroupBatchImportReq merchantStoreGroupBatchImportReq : merchantStoreGroupBatchImportReqList) {
            merchantStoreGroupBatchImportDTOList.add(toMerchantStoreGroupBatchImportDTO(merchantStoreGroupBatchImportReq));
        }
        return merchantStoreGroupBatchImportDTOList;
    }

    public static MerchantStoreGroupBatchImportDTO toMerchantStoreGroupBatchImportDTO(MerchantStoreGroupBatchImportReq merchantStoreGroupBatchImportReq) {
        if (merchantStoreGroupBatchImportReq == null) {
            return null;
        }
        MerchantStoreGroupBatchImportDTO merchantStoreGroupBatchImportDTO = new MerchantStoreGroupBatchImportDTO();
        merchantStoreGroupBatchImportDTO.setName(merchantStoreGroupBatchImportReq.getName());
        merchantStoreGroupBatchImportDTO.setStoreName(merchantStoreGroupBatchImportReq.getStoreName());
// Not mapped TO fields:
// error
        return merchantStoreGroupBatchImportDTO;
    }
}
