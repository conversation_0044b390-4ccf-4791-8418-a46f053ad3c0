package net.xianmu.usercenter.inbound.tenant.provider;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.tenant.service.MerchantCommandService;
import net.xianmu.usercenter.client.tenant.provider.MerchantCommandProvider;
import net.xianmu.usercenter.client.tenant.req.MerchantCommandReq;
import net.xianmu.usercenter.inbound.tenant.converter.MerchantConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @version 1.0
 * @date 2023-05-26 18:35:35
 */

@DubboService
public class MerchantCommandProviderImpl implements MerchantCommandProvider {
    @Autowired
    private MerchantCommandService merchantCommandService;

    @Override
    public DubboResponse create(SystemOriginEnum systemOriginEnum, MerchantCommandReq merchantCommandReq) {
        Long id = merchantCommandService.create(MerchantConverter.toMerchantCommandInput(merchantCommandReq));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse update(SystemOriginEnum systemOriginEnum, MerchantCommandReq merchantCommandReq) {
        Boolean success = merchantCommandService.update(MerchantConverter.toMerchantCommandInput(merchantCommandReq));
        return DubboResponse.getOK(success);
    }
}
