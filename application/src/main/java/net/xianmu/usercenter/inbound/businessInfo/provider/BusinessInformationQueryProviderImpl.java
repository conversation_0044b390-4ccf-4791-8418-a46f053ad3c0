package net.xianmu.usercenter.inbound.businessInfo.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.businessInfo.dto.BusinessInformationDTO;
import net.xianmu.usercenter.api.businessInfo.service.BusinessInformationQueryService;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationQueryProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.inbound.businessInfo.converter.BusinessInformationConverter;
import net.xianmu.usercenter.inbound.businessInfo.converter.BusinessInformationReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:01
 */
@DubboService
public class BusinessInformationQueryProviderImpl implements BusinessInformationQueryProvider {
    @Autowired
    private BusinessInformationQueryService queryService;


    @Override
    public DubboResponse<BusinessInformationResultResp> getBusinessInfoByBizIdAndType(BusinessInformationQueryReq businessInformationQueryReq) {
        final BusinessInformationDTO dto = queryService.getBusinessInfoByBizIdAndType(BusinessInformationReqConverter.toBusinessInformationQueryInput(businessInformationQueryReq));
        return DubboResponse.getOK(BusinessInformationConverter.toBusinessInformationResultResp(dto));
    }

    @Override
    public DubboResponse<List<BusinessInformationResultResp>> getBusinessInfos(BusinessInformationQueryReq businessInformationQueryReq) {
        final List<BusinessInformationDTO> businessInfos = queryService.getBusinessInfos(BusinessInformationReqConverter.toBusinessInformationQueryInput(businessInformationQueryReq));
        return DubboResponse.getOK(BusinessInformationConverter.toBusinessInformationResultRespList(businessInfos));
    }
}
