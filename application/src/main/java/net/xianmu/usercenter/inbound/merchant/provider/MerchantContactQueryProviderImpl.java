package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantContactQueryService;
import net.xianmu.usercenter.client.merchant.provider.MerchantContactQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantContactConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@DubboService
public class MerchantContactQueryProviderImpl implements MerchantContactQueryProvider {
    @Autowired
    private MerchantContactQueryService merchantContactQueryService;

    @Override
    public DubboResponse<List<MerchantContactResultResp>> getMerchantContacts(MerchantContactQueryReq merchantContactQueryReq) {
        final List<MerchantContactDTO> merchantContacts = merchantContactQueryService.getMerchantContacts(MerchantContactConverter.toMerchantContactQueryInput(merchantContactQueryReq));
        return DubboResponse.getOK(MerchantContactConverter.toMerchantContactResultRespList(merchantContacts));
    }

    @Override
    public DubboResponse<List<MerchantContactResultResp>> getMerchantContactsByStoreId(Long tenantId, Long storeId) {
        List<MerchantContactDTO> contacts = merchantContactQueryService.getMerchantContactsByStoreId(tenantId, storeId);
        return DubboResponse.getOK(MerchantContactConverter.toMerchantContactResultRespList(contacts));

    }
}
