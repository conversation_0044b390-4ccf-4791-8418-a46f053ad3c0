package net.xianmu.usercenter.inbound.tenant.converter;

import net.xianmu.usercenter.client.tenant.req.TenantCommandReq;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.domain.tenant.valueobject.TenantPrivilegesValueObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 17:15
 */
public class TenantReqConverter {

    private TenantReqConverter() {
        // 无需实现
    }

    /**
     *  *************************** TenantQueryInput ***********************************
     */

    public static TenantQueryInput toTenantQueryInput(TenantQueryReq tenantQueryReq) {
        if (tenantQueryReq == null) {
            return null;
        }
        TenantQueryInput tenantQueryInput = new TenantQueryInput();
        tenantQueryInput.setId(tenantQueryReq.getId());
        tenantQueryInput.setTenantIdList(tenantQueryReq.getTenantIdList());
        tenantQueryInput.setTenantName(tenantQueryReq.getTenantName());
        tenantQueryInput.setType(tenantQueryReq.getType());
        tenantQueryInput.setStatus(tenantQueryReq.getStatus());
        tenantQueryInput.setPhone(tenantQueryReq.getPhone());
        tenantQueryInput.setAdminId(tenantQueryReq.getAdminId());
        tenantQueryInput.setSortType(tenantQueryReq.getSortType());
        tenantQueryInput.setSortWord(tenantQueryReq.getSortWord());
        tenantQueryInput.setProfitSharingSwitch(tenantQueryReq.getProfitSharingSwitch());
        tenantQueryInput.setOnlinePayChannel(tenantQueryReq.getOnlinePayChannel());
        tenantQueryInput.setCompanyName(tenantQueryReq.getCompanyName());
        tenantQueryInput.setContactName(tenantQueryReq.getContactName());
        return tenantQueryInput;
    }


    /**
     *  *************************** TenantCommandInput ***********************************
     */

    public static TenantCommandInput toTenantCommandInput(TenantCommandReq tenantCommandReq) {
        if (tenantCommandReq == null) {
            return null;
        }
        TenantCommandInput tenantCommandInput = new TenantCommandInput();
        tenantCommandInput.setTenantId(tenantCommandReq.getTenantId());
        tenantCommandInput.setAdminId(tenantCommandReq.getAdminId());
        tenantCommandInput.setLoginPhone(tenantCommandReq.getLoginPhone());
        tenantCommandInput.setLoginPassword(tenantCommandReq.getLoginPassword());
        tenantCommandInput.setMerchantName(tenantCommandReq.getMerchantName());
        tenantCommandInput.setCompanyName(tenantCommandReq.getCompanyName());
        tenantCommandInput.setCreditCode(tenantCommandReq.getCreditCode());
        tenantCommandInput.setBusinessLicense(tenantCommandReq.getBusinessLicense());
        tenantCommandInput.setProvince(tenantCommandReq.getProvince());
        tenantCommandInput.setCity(tenantCommandReq.getCity());
        tenantCommandInput.setArea(tenantCommandReq.getArea());
        tenantCommandInput.setAddress(tenantCommandReq.getAddress());
        tenantCommandInput.setCompanyPhone(tenantCommandReq.getCompanyPhone());
        tenantCommandInput.setCompanyAreaPhone(tenantCommandReq.getCompanyAreaPhone());
        tenantCommandInput.setProfitSharingSwitch(tenantCommandReq.getProfitSharingSwitch());
        tenantCommandInput.setOnlinePayChannel(tenantCommandReq.getOnlinePayChannel());
        tenantCommandInput.setOpUid(tenantCommandReq.getOpUid());
        tenantCommandInput.setOpUname(tenantCommandReq.getOpUname());
        tenantCommandInput.setOpAuthUserId(tenantCommandReq.getOpAuthUserId());
        tenantCommandInput.setSystemOrigin(tenantCommandReq.getSystemOrigin());
        tenantCommandInput.setTenantPrivilegesList(toTenantPrivilegesValueObjectList(tenantCommandReq.getTenantPrivilegesList()));
        tenantCommandInput.setContactName(tenantCommandReq.getContactName());
        tenantCommandInput.setType(tenantCommandReq.getType());
        tenantCommandInput.setEmail(tenantCommandReq.getEmail());
        tenantCommandInput.setAccountLoginType(tenantCommandReq.getAccountLoginType());
        return tenantCommandInput;
    }


    public static List<TenantPrivilegesValueObject> toTenantPrivilegesValueObjectList(List<net.xianmu.usercenter.client.tenant.valueobject.TenantPrivilegesValueObject> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<TenantPrivilegesValueObject> tenantPrivilegesValueObjectList = new ArrayList<>();
        for (net.xianmu.usercenter.client.tenant.valueobject.TenantPrivilegesValueObject tenantPrivilegesValueObject : list) {
            tenantPrivilegesValueObjectList.add(toTenantPrivilegesValueObject(tenantPrivilegesValueObject));
        }
        return tenantPrivilegesValueObjectList;
    }

    public static TenantPrivilegesValueObject toTenantPrivilegesValueObject(net.xianmu.usercenter.client.tenant.valueobject.TenantPrivilegesValueObject valueObject) {
        if (valueObject == null) {
            return null;
        }
        TenantPrivilegesValueObject tenantPrivilegesValueObject = new TenantPrivilegesValueObject();
        tenantPrivilegesValueObject.setMenuId(valueObject.getMenuId());
        tenantPrivilegesValueObject.setExpireTime(valueObject.getExpireTime());
        return tenantPrivilegesValueObject;
    }
}
