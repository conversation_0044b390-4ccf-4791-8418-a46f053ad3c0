package net.xianmu.usercenter.inbound.merchant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.api.merchant.service.MerchantAddressQueryService;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantDeliveryAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.inbound.merchant.converter.MerchantAddressConverter;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantAddressConverterV2;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */
@DubboService
public class MerchantAddressQueryProviderImpl implements MerchantAddressQueryProvider {

    @Autowired
    private MerchantAddressQueryService merchantAddressQueryService;

    @Override
    public DubboResponse<List<MerchantAddressResultResp>> getMerchantAddressList(MerchantAddressQueryReq merchantAddressQueryReq) {
        List<MerchantAddressEntity> merchantAddressList = merchantAddressQueryService.getMerchantAddressList(MerchantAddressConverter.toMerchantAddressQueryInput(merchantAddressQueryReq));
        return DubboResponse.getOK(MerchantAddressConverterV2.toMerchantAddressRespList(merchantAddressList));
    }

    @Override
    public DubboResponse<List<MerchantAddressDomainResp>> getAddressAndContacts(MerchantAddressQueryReq merchantAddressQueryReq) {
        List<MerchantAddressEntity> merchantAddressList = merchantAddressQueryService.getAddressAndContacts(MerchantAddressConverter.toMerchantAddressQueryInput(merchantAddressQueryReq));
        return DubboResponse.getOK(MerchantAddressConverterV2.toMerchantAddressDomainRespList(merchantAddressList));
    }


    @Override
    public DubboResponse<List<String>> getConcatAddress(Long tenantId) {
        return DubboResponse.getOK(merchantAddressQueryService.getConcatAddress(tenantId));
    }
}
