package net.xianmu.usercenter.inbound.businessInfo.converter;

import net.xianmu.usercenter.api.businessInfo.dto.BusinessInformationDTO;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 17:01:58
 * @version 1.0
 *
 */
public class BusinessInformationConverter {


    private BusinessInformationConverter() {
        // 无需实现
    }

    public static List<BusinessInformationResultResp> toBusinessInformationResultRespList(List<BusinessInformationDTO> businessInformationDTOList) {
        if (businessInformationDTOList == null) {
            return Collections.emptyList();
        }
        List<BusinessInformationResultResp> businessInformationResultRespList = new ArrayList<>();
        for (BusinessInformationDTO businessInformationDTO : businessInformationDTOList) {
            businessInformationResultRespList.add(toBusinessInformationResultResp(businessInformationDTO));
        }
        return businessInformationResultRespList;
    }

    public static BusinessInformationResultResp toBusinessInformationResultResp(BusinessInformationDTO businessInformationDTO) {
        if (businessInformationDTO == null) {
            return null;
        }
        BusinessInformationResultResp businessInformationResultResp = new BusinessInformationResultResp();
        businessInformationResultResp.setId(businessInformationDTO.getId());
        businessInformationResultResp.setTenantId(businessInformationDTO.getTenantId());
        businessInformationResultResp.setBizId(businessInformationDTO.getBizId());
        businessInformationResultResp.setType(businessInformationDTO.getType());
        businessInformationResultResp.setCompanyName(businessInformationDTO.getCompanyName());
        businessInformationResultResp.setCreditCode(businessInformationDTO.getCreditCode());
        businessInformationResultResp.setProvince(businessInformationDTO.getProvince());
        businessInformationResultResp.setCity(businessInformationDTO.getCity());
        businessInformationResultResp.setArea(businessInformationDTO.getArea());
        businessInformationResultResp.setAddress(businessInformationDTO.getAddress());
        businessInformationResultResp.setPhone(businessInformationDTO.getPhone());
        businessInformationResultResp.setBusinessLicense(businessInformationDTO.getBusinessLicense());
        businessInformationResultResp.setCreateTime(businessInformationDTO.getCreateTime());
        businessInformationResultResp.setUpdateTime(businessInformationDTO.getUpdateTime());
        businessInformationResultResp.setContactName(businessInformationDTO.getContactName());
        return businessInformationResultResp;
    }

    public static List<BusinessInformationDTO> toBusinessInformationDTOList(List<BusinessInformationResultResp> businessInformationResultRespList) {
        if (businessInformationResultRespList == null) {
            return Collections.emptyList();
        }
        List<BusinessInformationDTO> businessInformationDTOList = new ArrayList<>();
        for (BusinessInformationResultResp businessInformationResultResp : businessInformationResultRespList) {
            businessInformationDTOList.add(toBusinessInformationDTO(businessInformationResultResp));
        }
        return businessInformationDTOList;
    }

    public static BusinessInformationDTO toBusinessInformationDTO(BusinessInformationResultResp businessInformationResultResp) {
        if (businessInformationResultResp == null) {
            return null;
        }
        BusinessInformationDTO businessInformationDTO = new BusinessInformationDTO();
        businessInformationDTO.setId(businessInformationResultResp.getId());
        businessInformationDTO.setTenantId(businessInformationResultResp.getTenantId());
        businessInformationDTO.setBizId(businessInformationResultResp.getBizId());
        businessInformationDTO.setType(businessInformationResultResp.getType());
        businessInformationDTO.setCompanyName(businessInformationResultResp.getCompanyName());
        businessInformationDTO.setCreditCode(businessInformationResultResp.getCreditCode());
        businessInformationDTO.setProvince(businessInformationResultResp.getProvince());
        businessInformationDTO.setCity(businessInformationResultResp.getCity());
        businessInformationDTO.setArea(businessInformationResultResp.getArea());
        businessInformationDTO.setAddress(businessInformationResultResp.getAddress());
        businessInformationDTO.setPhone(businessInformationResultResp.getPhone());
        businessInformationDTO.setBusinessLicense(businessInformationResultResp.getBusinessLicense());
        businessInformationDTO.setCreateTime(businessInformationResultResp.getCreateTime());
        businessInformationDTO.setUpdateTime(businessInformationResultResp.getUpdateTime());
        return businessInformationDTO;
    }
}
