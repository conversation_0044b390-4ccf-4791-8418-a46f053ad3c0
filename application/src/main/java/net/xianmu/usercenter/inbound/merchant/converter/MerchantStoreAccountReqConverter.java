package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreAccountReqConverter {


    private MerchantStoreAccountReqConverter() {
        // 无需实现
    }


    public static MerchantStoreAccountQueryInput toMerchantStoreAccountQueryInput(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        if (merchantStoreAccountQueryReq == null) {
            return null;
        }
        MerchantStoreAccountQueryInput merchantStoreAccountQueryInput = new MerchantStoreAccountQueryInput();
        merchantStoreAccountQueryInput.setTenantId(merchantStoreAccountQueryReq.getTenantId());
        merchantStoreAccountQueryInput.setStoreId(merchantStoreAccountQueryReq.getStoreId());
        merchantStoreAccountQueryInput.setStoreIdList(merchantStoreAccountQueryReq.getStoreIdList());
        merchantStoreAccountQueryInput.setPhone(merchantStoreAccountQueryReq.getPhone());
        merchantStoreAccountQueryInput.setType(merchantStoreAccountQueryReq.getType());
        merchantStoreAccountQueryInput.setStatus(merchantStoreAccountQueryReq.getStatus());
        merchantStoreAccountQueryInput.setDeleteFlag(merchantStoreAccountQueryReq.getDeleteFlag());
        merchantStoreAccountQueryInput.setMId(merchantStoreAccountQueryReq.getMId());
        merchantStoreAccountQueryInput.setMIdList(merchantStoreAccountQueryReq.getMIdList());
        merchantStoreAccountQueryInput.setXmAccountIdList(merchantStoreAccountQueryReq.getXmAccountIdList());
        return merchantStoreAccountQueryInput;
    }


    public static MerchantStoreAccountQueryInput toMerchantStoreAccountQueryInput(MerchantStoreAccountPageReq merchantStoreAccountQueryReq) {
        if (merchantStoreAccountQueryReq == null) {
            return null;
        }
        MerchantStoreAccountQueryInput merchantStoreAccountQueryInput = new MerchantStoreAccountQueryInput();
        merchantStoreAccountQueryInput.setTenantId(merchantStoreAccountQueryReq.getTenantId());
        merchantStoreAccountQueryInput.setStoreId(merchantStoreAccountQueryReq.getStoreId());
        merchantStoreAccountQueryInput.setPhone(merchantStoreAccountQueryReq.getPhone());
        merchantStoreAccountQueryInput.setType(merchantStoreAccountQueryReq.getType());
        merchantStoreAccountQueryInput.setStatus(merchantStoreAccountQueryReq.getStatus());
        merchantStoreAccountQueryInput.setDeleteFlag(merchantStoreAccountQueryReq.getDeleteFlag());
        merchantStoreAccountQueryInput.setStoreName(merchantStoreAccountQueryReq.getStoreName());
        merchantStoreAccountQueryInput.setMId(merchantStoreAccountQueryReq.getMId());
        merchantStoreAccountQueryInput.setMIdList(merchantStoreAccountQueryReq.getMIdList());
        return merchantStoreAccountQueryInput;
    }


    public static List<MerchantStoreAccountCommandInput> toMerchantStoreAccountCommandInputList(List<MerchantStoreAccountCommandReq> list, Integer systemOrigin) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountCommandInput> resultList = new ArrayList<>();
        for (MerchantStoreAccountCommandReq dto : list) {
            resultList.add(toMerchantStoreAccountCommandInput(dto, systemOrigin));
        }
        return resultList;
    }

    public static MerchantStoreAccountCommandInput toMerchantStoreAccountCommandInput(MerchantStoreAccountCommandReq merchantStoreAccountCommandReq, Integer systemOrigin) {
        if (merchantStoreAccountCommandReq == null) {
            return null;
        }
        MerchantStoreAccountCommandInput merchantStoreAccountCommandInput = new MerchantStoreAccountCommandInput();
        merchantStoreAccountCommandInput.setId(merchantStoreAccountCommandReq.getId());
        merchantStoreAccountCommandInput.setIdList(merchantStoreAccountCommandReq.getIdList());
        merchantStoreAccountCommandInput.setTenantId(merchantStoreAccountCommandReq.getTenantId());
        merchantStoreAccountCommandInput.setStoreId(merchantStoreAccountCommandReq.getStoreId());
        merchantStoreAccountCommandInput.setAccountName(merchantStoreAccountCommandReq.getAccountName());
        merchantStoreAccountCommandInput.setPhone(merchantStoreAccountCommandReq.getPhone());
        merchantStoreAccountCommandInput.setType(merchantStoreAccountCommandReq.getType());
        merchantStoreAccountCommandInput.setRegisterTime(merchantStoreAccountCommandReq.getRegisterTime());
        merchantStoreAccountCommandInput.setAuditTime(merchantStoreAccountCommandReq.getAuditTime());
        merchantStoreAccountCommandInput.setOpenId(merchantStoreAccountCommandReq.getOpenId());
        merchantStoreAccountCommandInput.setUnionId(merchantStoreAccountCommandReq.getUnionId());
        merchantStoreAccountCommandInput.setCreateTime(merchantStoreAccountCommandReq.getCreateTime());
        merchantStoreAccountCommandInput.setUpdateTime(merchantStoreAccountCommandReq.getUpdateTime());
        merchantStoreAccountCommandInput.setStatus(merchantStoreAccountCommandReq.getStatus());
        merchantStoreAccountCommandInput.setLastLoginTime(merchantStoreAccountCommandReq.getLastLoginTime());
        merchantStoreAccountCommandInput.setDeleteFlag(merchantStoreAccountCommandReq.getDeleteFlag());
        merchantStoreAccountCommandInput.setSystemOrigin(systemOrigin);
        merchantStoreAccountCommandInput.setOaOpenId(merchantStoreAccountCommandReq.getOaOpenId());
        merchantStoreAccountCommandInput.setUsername(merchantStoreAccountCommandReq.getUsername());
        return merchantStoreAccountCommandInput;
    }
}
