package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.client.merchant.req.MerchantStoreChangeLogQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.common.input.query.MerchantStoreChangeLogQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/10/27 17:29
 */
public class MerchantStoreChangeLogConverter {


    private MerchantStoreChangeLogConverter() {
        // 无需实现
    }

    public static List<MerchantStoreChangeLogResultResp> toMerchantStoreChangeLogResultRespList(List<MerchantStoreChangeLogEntity> merchantStoreChangeLogEntityList) {
        if (merchantStoreChangeLogEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreChangeLogResultResp> merchantStoreChangeLogResultRespList = new ArrayList<>();
        for (MerchantStoreChangeLogEntity merchantStoreChangeLogEntity : merchantStoreChangeLogEntityList) {
            merchantStoreChangeLogResultRespList.add(toMerchantStoreChangeLogResultResp(merchantStoreChangeLogEntity));
        }
        return merchantStoreChangeLogResultRespList;
    }

    public static MerchantStoreChangeLogResultResp toMerchantStoreChangeLogResultResp(MerchantStoreChangeLogEntity merchantStoreChangeLogEntity) {
        if (merchantStoreChangeLogEntity == null) {
            return null;
        }
        MerchantStoreChangeLogResultResp merchantStoreChangeLogResultResp = new MerchantStoreChangeLogResultResp();
        merchantStoreChangeLogResultResp.setTenantId(merchantStoreChangeLogEntity.getTenantId());
        merchantStoreChangeLogResultResp.setStoreId(merchantStoreChangeLogEntity.getStoreId());
        merchantStoreChangeLogResultResp.setInviterChannelCode(merchantStoreChangeLogEntity.getInviterChannelCode());
        merchantStoreChangeLogResultResp.setMerchantChannelCode(merchantStoreChangeLogEntity.getMerchantChannelCode());
        merchantStoreChangeLogResultResp.setOpName(merchantStoreChangeLogEntity.getOpName());
        merchantStoreChangeLogResultResp.setOpType(merchantStoreChangeLogEntity.getOpType());
        merchantStoreChangeLogResultResp.setOpRemark(merchantStoreChangeLogEntity.getOpRemark());
        merchantStoreChangeLogResultResp.setCreateTime(merchantStoreChangeLogEntity.getCreateTime());
        merchantStoreChangeLogResultResp.setUpdateTime(merchantStoreChangeLogEntity.getUpdateTime());
// Not mapped FROM fields:
// id
// regionalId
        return merchantStoreChangeLogResultResp;
    }

    public static List<MerchantStoreChangeLogEntity> toMerchantStoreChangeLogEntityList(List<MerchantStoreChangeLogResultResp> merchantStoreChangeLogResultRespList) {
        if (merchantStoreChangeLogResultRespList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreChangeLogEntity> merchantStoreChangeLogEntityList = new ArrayList<>();
        for (MerchantStoreChangeLogResultResp merchantStoreChangeLogResultResp : merchantStoreChangeLogResultRespList) {
            merchantStoreChangeLogEntityList.add(toMerchantStoreChangeLogEntity(merchantStoreChangeLogResultResp));
        }
        return merchantStoreChangeLogEntityList;
    }

    public static MerchantStoreChangeLogEntity toMerchantStoreChangeLogEntity(MerchantStoreChangeLogResultResp merchantStoreChangeLogResultResp) {
        if (merchantStoreChangeLogResultResp == null) {
            return null;
        }
        MerchantStoreChangeLogEntity merchantStoreChangeLogEntity = new MerchantStoreChangeLogEntity();
        merchantStoreChangeLogEntity.setTenantId(merchantStoreChangeLogResultResp.getTenantId());
        merchantStoreChangeLogEntity.setStoreId(merchantStoreChangeLogResultResp.getStoreId());
        merchantStoreChangeLogEntity.setInviterChannelCode(merchantStoreChangeLogResultResp.getInviterChannelCode());
        merchantStoreChangeLogEntity.setMerchantChannelCode(merchantStoreChangeLogResultResp.getMerchantChannelCode());
        merchantStoreChangeLogEntity.setOpName(merchantStoreChangeLogResultResp.getOpName());
        merchantStoreChangeLogEntity.setOpType(merchantStoreChangeLogResultResp.getOpType());
        merchantStoreChangeLogEntity.setOpRemark(merchantStoreChangeLogResultResp.getOpRemark());
        merchantStoreChangeLogEntity.setCreateTime(merchantStoreChangeLogResultResp.getCreateTime());
        merchantStoreChangeLogEntity.setUpdateTime(merchantStoreChangeLogResultResp.getUpdateTime());
// Not mapped TO fields:
// id
// regionalId
        return merchantStoreChangeLogEntity;
    }

    public static MerchantStoreChangeLogQueryInput toMerchantStoreChangeLogQueryInput(MerchantStoreChangeLogQueryReq req) {
        if (req == null) {
            return null;
        }
        MerchantStoreChangeLogQueryInput merchantStoreChangeLogQueryInput = new MerchantStoreChangeLogQueryInput();
        merchantStoreChangeLogQueryInput.setTenantId(req.getTenantId());
        merchantStoreChangeLogQueryInput.setStoreId(req.getStoreId());
        merchantStoreChangeLogQueryInput.setStoreIdList(req.getStoreIdList());
        merchantStoreChangeLogQueryInput.setOpType(req.getOpType());
        return merchantStoreChangeLogQueryInput;
    }


}
