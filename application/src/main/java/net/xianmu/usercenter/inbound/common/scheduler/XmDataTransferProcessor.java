package net.xianmu.usercenter.inbound.common.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.usercenter.api.common.DbTableDml;
import net.xianmu.usercenter.common.dto.XmDataTransferTaskParamDTO;
import net.xianmu.usercenter.common.dto.XmDataTransferTaskParamListDTO;
import net.xianmu.usercenter.inbound.common.mq.factory.DbTableDmlFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 15:07
 */
@Component
@Slf4j
public class XmDataTransferProcessor extends XianMuJavaProcessor {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        try {
            String instanceParameterStr = context.getInstanceParameters();
            log.info("同步鲜沐用户的定时任务开始执行, instanceParameters: 【{}】", JSON.toJSONString(instanceParameterStr));
            XmDataTransferTaskParamDTO instanceParameters = JSONObject.parseObject(instanceParameterStr, XmDataTransferTaskParamDTO.class);
            if (null == instanceParameters || CollUtil.isEmpty(instanceParameters.getParamList())) {
                log.info("暂无表需要同步！！");
                return new ProcessResult(true);
            }
            boolean isCreate = XmDataTransferTaskParamDTO.OP_TYPE_CREATE.equals(instanceParameters.getOpType());

            for (XmDataTransferTaskParamListDTO dto : instanceParameters.getParamList()) {
                String tableName = dto.getTableName();
                log.info("当前同步表：tableName：{}", tableName);
                long start = System.currentTimeMillis();
                DbTableDml creator = dbTableDmlFactory.creator(tableName);
                if (Objects.nonNull(creator)) {
                    creator.handleForTask(dto, isCreate);
                } else {
                    log.info("未在DbTableDmlFactory注册的table:{},请先注册后再做处理!", tableName);
                }
                log.info("tableName:{}表同步完成!!,耗时：{}ms", tableName, System.currentTimeMillis() - start);
            }
            log.info("同步鲜沐用户的定时任务执行完成!!");
        } catch (Exception e) {
            log.error("定时任务执行失败!", e);
        }
        return new ProcessResult(true);
    }
}
