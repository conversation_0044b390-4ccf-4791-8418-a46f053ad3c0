package net.xianmu.usercenter.inbound.merchant.provider;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStorePropertiesExtCommandService;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreExtCommandProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreExtCommandReq;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @descripton 门店拓展属性查询接口
 * @date 2023/5/5 11:01
 */
@Slf4j
@DubboService
public class MerchantStorePropertiesExtCommandProviderImpl implements MerchantStoreExtCommandProvider {

    @Autowired
    private MerchantStorePropertiesExtCommandService commandServiceService;

    @Override
    public DubboResponse<Boolean> addOrUpdateMerchantStoreExt(MerchantStoreExtCommandReq merchantStoreExtCommandReq) {
        return DubboResponse.getOK(commandServiceService.addOrUpdateExt(merchantStoreExtCommandReq.getMId(), merchantStoreExtCommandReq.getProKey() ,merchantStoreExtCommandReq.getProValue()));
    }
}
