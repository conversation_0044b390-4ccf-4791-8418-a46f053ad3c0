package net.xianmu.usercenter.inbound.merchant.converter;

import net.xianmu.usercenter.api.merchant.dto.MerchantStorePropertiesExtDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreExtResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;

public class MerchantStorePropertiesExtConverter {

    public MerchantStorePropertiesExtConverter(){

    }


    public static MerchantStoreExtResp convertMerchantStoreExtResp(MerchantStorePropertiesExtDTO dto){
        if (dto == null){
            return null;
        }
        MerchantStoreExtResp resp = new MerchantStoreExtResp();
        resp.setStoreId(dto.getStoreId());
        resp.setMId(dto.getMId());
        resp.setProKey(dto.getProKey());
        resp.setProValue(dto.getProValue());
        return resp;
    }

    public static MerchantStorePropertiesExtDTO convertMerchantStoreExtDto(MerchantStorePropertiesExtEntity ext){
        if (ext == null){
            return null;
        }
        MerchantStorePropertiesExtDTO dto = new MerchantStorePropertiesExtDTO();
        dto.setStoreId(ext.getStoreId());
        dto.setMId(ext.getMId());
        dto.setProKey(ext.getProKey());
        dto.setProValue(ext.getProValue());
        return dto;
    }
}
