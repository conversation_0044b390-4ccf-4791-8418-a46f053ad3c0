package net.xianmu.usercenter.inbound.merchant.converter.v2;

import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreConverterV2 {


    private MerchantStoreConverterV2() {
        // 无需实现
    }



    public static List<MerchantStoreResultResp> toMerchantStoreResultRespList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreResultResp> merchantStoreResultRespList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreResultRespList.add(toMerchantStoreResultResp(merchantStoreEntity));
        }
        return merchantStoreResultRespList;
    }

    public static MerchantStoreResultResp toMerchantStoreResultResp(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStoreResultResp merchantStoreResultResp = new MerchantStoreResultResp();
        merchantStoreResultResp.setId(merchantStoreEntity.getId());
        merchantStoreResultResp.setTenantId(merchantStoreEntity.getTenantId());
        merchantStoreResultResp.setStoreName(merchantStoreEntity.getStoreName());
        merchantStoreResultResp.setType(merchantStoreEntity.getType());
        merchantStoreResultResp.setRegisterTime(merchantStoreEntity.getRegisterTime());
        merchantStoreResultResp.setStatus(merchantStoreEntity.getStatus());
        merchantStoreResultResp.setAuditRemark(merchantStoreEntity.getAuditRemark());
        merchantStoreResultResp.setRemark(merchantStoreEntity.getRemark());
        merchantStoreResultResp.setAuditTime(merchantStoreEntity.getAuditTime());
        merchantStoreResultResp.setCreateTime(merchantStoreEntity.getCreateTime());
        merchantStoreResultResp.setUpdateTime(merchantStoreEntity.getUpdateTime());
        merchantStoreResultResp.setBillSwitch(merchantStoreEntity.getBillSwitch());
        merchantStoreResultResp.setOnlinePayment(merchantStoreEntity.getOnlinePayment());
        merchantStoreResultResp.setBalanceAuthority(merchantStoreEntity.getBalanceAuthority());
        merchantStoreResultResp.setStoreNo(merchantStoreEntity.getStoreNo());
        merchantStoreResultResp.setRegionalId(merchantStoreEntity.getRegionalId());
        merchantStoreResultResp.setBusinessType(merchantStoreEntity.getBusinessType());
        merchantStoreResultResp.setMId(merchantStoreEntity.getMId());
        merchantStoreResultResp.setChannelCode(merchantStoreEntity.getChannelCode());
        merchantStoreResultResp.setAreaNo(merchantStoreEntity.getAreaNo());
        merchantStoreResultResp.setPlaceOrderPermissionTimeLimited(merchantStoreEntity.getPlaceOrderPermissionTimeLimited ());
        merchantStoreResultResp.setPlaceOrderPermissionExpiryTime(merchantStoreEntity.getPlaceOrderPermissionExpiryTime ());
        merchantStoreResultResp.setEnableOfflinePayment (merchantStoreEntity.getEnableOfflinePayment());
        merchantStoreResultResp.setNonCashAuthority(merchantStoreEntity.getNonCashAuthority());
        return merchantStoreResultResp;

    }

    public static List<MerchantStoreAndExtendResp> toMerchantStoreAndExtendRespList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAndExtendResp> merchantStoreAndExtendRespList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreAndExtendRespList.add(toMerchantStoreAndExtendResp(merchantStoreEntity));
        }
        return merchantStoreAndExtendRespList;
    }

    public static MerchantStoreAndExtendResp toMerchantStoreAndExtendResp(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStoreAndExtendResp merchantStoreAndExtendResp = new MerchantStoreAndExtendResp();
        merchantStoreAndExtendResp.setPopView(merchantStoreEntity.getPopView());
        merchantStoreAndExtendResp.setChangePop(merchantStoreEntity.getChangePop());
        merchantStoreAndExtendResp.setFirstLoginPop(merchantStoreEntity.getFirstLoginPop());
        merchantStoreAndExtendResp.setDisplayButton(merchantStoreEntity.getDisplayButton());
        merchantStoreAndExtendResp.setPreRegisterFlag(merchantStoreEntity.getPreRegisterFlag());
        merchantStoreAndExtendResp.setMockLoginFlag(merchantStoreEntity.getMockLoginFlag());
        merchantStoreAndExtendResp.setProvince(merchantStoreEntity.getProvince());
        merchantStoreAndExtendResp.setCity(merchantStoreEntity.getCity());
        merchantStoreAndExtendResp.setArea(merchantStoreEntity.getArea());
        merchantStoreAndExtendResp.setId(merchantStoreEntity.getId());
        merchantStoreAndExtendResp.setTenantId(merchantStoreEntity.getTenantId());
        merchantStoreAndExtendResp.setStoreName(merchantStoreEntity.getStoreName());
        merchantStoreAndExtendResp.setType(merchantStoreEntity.getType());
        merchantStoreAndExtendResp.setRegisterTime(merchantStoreEntity.getRegisterTime());
        merchantStoreAndExtendResp.setStatus(merchantStoreEntity.getStatus());
        merchantStoreAndExtendResp.setAuditRemark(merchantStoreEntity.getAuditRemark());
        merchantStoreAndExtendResp.setRemark(merchantStoreEntity.getRemark());
        merchantStoreAndExtendResp.setAuditTime(merchantStoreEntity.getAuditTime());
        merchantStoreAndExtendResp.setCreateTime(merchantStoreEntity.getCreateTime());
        merchantStoreAndExtendResp.setUpdateTime(merchantStoreEntity.getUpdateTime());
        merchantStoreAndExtendResp.setBillSwitch(merchantStoreEntity.getBillSwitch());
        merchantStoreAndExtendResp.setOnlinePayment(merchantStoreEntity.getOnlinePayment());
        merchantStoreAndExtendResp.setBalanceAuthority(merchantStoreEntity.getBalanceAuthority());
        merchantStoreAndExtendResp.setStoreNo(merchantStoreEntity.getStoreNo());
        merchantStoreAndExtendResp.setRegionalId(merchantStoreEntity.getRegionalId());
        merchantStoreAndExtendResp.setBusinessType(merchantStoreEntity.getBusinessType());
        merchantStoreAndExtendResp.setMId(merchantStoreEntity.getMId());
        merchantStoreAndExtendResp.setChannelCode(merchantStoreEntity.getChannelCode());
        merchantStoreAndExtendResp.setAreaNo(merchantStoreEntity.getAreaNo());
        merchantStoreAndExtendResp.setPoiNote(merchantStoreEntity.getPoiNote());
        merchantStoreAndExtendResp.setAdminId(merchantStoreEntity.getAdminId());
        merchantStoreAndExtendResp.setSize(merchantStoreEntity.getSize());
        merchantStoreAndExtendResp.setOrganizationName(merchantStoreEntity.getOrganizationName());
        merchantStoreAndExtendResp.setPhone(merchantStoreEntity.getPhone());
        merchantStoreAndExtendResp.setAccountName(merchantStoreEntity.getAccountName());
        merchantStoreAndExtendResp.setOperateStatus(merchantStoreEntity.getOperateStatus());
        merchantStoreAndExtendResp.setBusinessLine(merchantStoreEntity.getBusinessLine());
// Not mapped TO fields:
// Not mapped FROM fields:
// phone
// accountId
// accountName
// accountType
// accountStatus
// lastLoginTime
// address
// houseNumber
// deliveryAddress
        return merchantStoreAndExtendResp;
    }




    public static List<XmMerchantStorePageResp> toXmMerchantStorePageRespList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<XmMerchantStorePageResp> xmMerchantStorePageRespList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            xmMerchantStorePageRespList.add(toXmMerchantStorePageResp(merchantStoreEntity));
        }
        return xmMerchantStorePageRespList;
    }

    public static XmMerchantStorePageResp toXmMerchantStorePageResp(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        XmMerchantStorePageResp xmMerchantStorePageResp = new XmMerchantStorePageResp();
        xmMerchantStorePageResp.setId(merchantStoreEntity.getId());
        xmMerchantStorePageResp.setTenantId(merchantStoreEntity.getTenantId());
        xmMerchantStorePageResp.setStoreName(merchantStoreEntity.getStoreName());
        xmMerchantStorePageResp.setType(merchantStoreEntity.getType());
        xmMerchantStorePageResp.setRegisterTime(merchantStoreEntity.getRegisterTime());
        xmMerchantStorePageResp.setStatus(merchantStoreEntity.getStatus());
        xmMerchantStorePageResp.setAuditTime(merchantStoreEntity.getAuditTime());
        xmMerchantStorePageResp.setCreateTime(merchantStoreEntity.getCreateTime());
        xmMerchantStorePageResp.setUpdateTime(merchantStoreEntity.getUpdateTime());
        xmMerchantStorePageResp.setBusinessType(merchantStoreEntity.getBusinessType());
        xmMerchantStorePageResp.setRegionalId(merchantStoreEntity.getRegionalId());
        xmMerchantStorePageResp.setMId(merchantStoreEntity.getMId());
        xmMerchantStorePageResp.setChannelCode(merchantStoreEntity.getChannelCode());
        xmMerchantStorePageResp.setAreaNo(merchantStoreEntity.getAreaNo());
        xmMerchantStorePageResp.setProvince(merchantStoreEntity.getProvince());
        xmMerchantStorePageResp.setCity(merchantStoreEntity.getCity());
        xmMerchantStorePageResp.setArea(merchantStoreEntity.getArea());
        xmMerchantStorePageResp.setPoiNote(merchantStoreEntity.getPoiNote());
        xmMerchantStorePageResp.setAdminId(merchantStoreEntity.getAdminId());
        xmMerchantStorePageResp.setSize(merchantStoreEntity.getSize());
        xmMerchantStorePageResp.setOperateStatus(merchantStoreEntity.getOperateStatus());
// Not mapped TO fields:
// Not mapped FROM fields:
// auditRemark
// remark
// billSwitch
// onlinePayment
// storeNo
// balanceAuthority
// phone
// accountId
// accountName
// accountType
// accountStatus
// lastLoginTime
// address
// houseNumber
// deliveryAddress
// popView
// changePop
// firstLoginPop
// displayButton
// preRegisterFlag
// mockLoginFlag
        return xmMerchantStorePageResp;
    }
}
