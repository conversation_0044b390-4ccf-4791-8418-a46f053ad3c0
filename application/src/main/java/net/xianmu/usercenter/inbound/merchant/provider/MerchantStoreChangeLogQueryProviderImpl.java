package net.xianmu.usercenter.inbound.merchant.provider;


import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.merchant.service.MerchantStoreChangeLogQueryService;

import net.xianmu.usercenter.client.merchant.provider.MerchantStoreChangeLogQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreChangeLogQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.inbound.merchant.converter.v2.MerchantStoreChangeLogConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *
 * @date 2023-10-27 14:57:37
 * @version 1.0
 *
 */
@DubboService
public class MerchantStoreChangeLogQueryProviderImpl implements MerchantStoreChangeLogQueryProvider {

    @Autowired
    private MerchantStoreChangeLogQueryService merchantStoreChangeLogQueryService;

    @Override
    public DubboResponse<List<MerchantStoreChangeLogResultResp>> getMerchantStoreChangeLogs(MerchantStoreChangeLogQueryReq req) {
        List<MerchantStoreChangeLogEntity> merchantChangeLogs = merchantStoreChangeLogQueryService.getMerchantChangeLogs(MerchantStoreChangeLogConverter.toMerchantStoreChangeLogQueryInput(req));
        return DubboResponse.getOK(MerchantStoreChangeLogConverter.toMerchantStoreChangeLogResultRespList(merchantChangeLogs));
    }
}
