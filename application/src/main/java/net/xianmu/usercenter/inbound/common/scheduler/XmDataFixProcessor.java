package net.xianmu.usercenter.inbound.common.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.usercenter.application.datatransfer.MerchantAddressTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreAccountTransServiceImpl;
import net.xianmu.usercenter.application.datatransfer.MerchantStoreTransServiceImpl;
import net.xianmu.usercenter.common.dto.XmDataFixTaskParamDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton 修复鲜沐数据
 * @date 2023/7/17 15:07
 */
@Component
@Slf4j
public class XmDataFixProcessor extends XianMuJavaProcessor {

    @Resource
    private MerchantStoreTransServiceImpl merchantStoreTransService;
    @Resource
    private MerchantStoreAccountTransServiceImpl merchantStoreAccountTransService;
    @Resource
    private MerchantAddressTransServiceImpl merchantAddressTransService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        try {
            String instanceParameterStr = context.getInstanceParameters();
            log.info("更新鲜沐数据定时任务开始执行, instanceParameters: 【{}】", JSON.toJSONString(instanceParameterStr));
            XmDataFixTaskParamDTO instanceParameters = JSONObject.parseObject(instanceParameterStr, XmDataFixTaskParamDTO.class);
            if (null == instanceParameters) {
                log.info("任务参数为空！！");
                return new ProcessResult(true);
            }
            String opType = instanceParameters.getOpType();
            if(null == opType) {
                log.info("任务类型为空！！");
                return new ProcessResult(true);
            }
            if(XmDataFixTaskParamDTO.pull_black.equals(opType)) {
                merchantStoreTransService.reTransferPullBlackData();
            } else if(XmDataFixTaskParamDTO.refresh_merchant_data.equals(opType)) {
                Map<String, Object> paramMap = instanceParameters.getParamMap();
                List<Long> idList = JSON.parseArray(JSON.toJSONString(paramMap.get("idList")), Long.class);
                merchantStoreTransService.refreshMerchantData(idList);
            } else if(XmDataFixTaskParamDTO.refresh_account_data.equals(opType)) {
                Map<String, Object> paramMap = instanceParameters.getParamMap();
                List<Long> idList = JSON.parseArray(JSON.toJSONString(paramMap.get("idList")), Long.class);
                merchantStoreAccountTransService.refreshAccountData(idList);
            } else if(XmDataFixTaskParamDTO.refresh_address_data.equals(opType)) {
                Map<String, Object> paramMap = instanceParameters.getParamMap();
                List<Long> idList = JSON.parseArray(JSON.toJSONString(paramMap.get("idList")), Long.class);
                merchantAddressTransService.refreshAddressData(idList);
            } else if(XmDataFixTaskParamDTO.delete_merchant_data.equals(opType)) {
                Map<String, Object> paramMap = instanceParameters.getParamMap();
                List<Long> idList = JSON.parseArray(JSON.toJSONString(paramMap.get("idList")), Long.class);
                merchantStoreTransService.deleteMerchantData(idList);
            }else if(XmDataFixTaskParamDTO.delete_account_data.equals(opType)) {
                Map<String, Object> paramMap = instanceParameters.getParamMap();
                List<Long> idList = JSON.parseArray(JSON.toJSONString(paramMap.get("idList")), Long.class);
                merchantStoreAccountTransService.deleteMerchantData(idList);
            }else {
                log.info("任务类型匹配不到任务！！");
            }



            log.info("更新鲜沐数据定时任务执行完成!!");
        } catch (Exception e) {
            log.error("定时任务执行失败!", e);
        }
        return new ProcessResult(true);
    }
}
