package net.xianmu.usercenter.inbound.common.mq.listener;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.usercenter.api.common.DbTableDml;
import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.inbound.common.mq.factory.DbTableDmlFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton binlog顺序消费
 * @date 2023/6/26 16:20
 */
@Component
@Slf4j
@Lazy
@MqOrderlyListener(topic = "mysql-binlog-orderly",tag = "admin || merchant || contact || merchant_sub_account", consumerGroup = "GID_binlog_user_center", maxReconsumeTimes = 2, suspendCurrentQueueTimeMillis = 1000L, maxReconsumeTimesWarnLog = 2)
public class BinLogOrderlyListener extends AbstractMqListener<DtsModel> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModel dtsModel) {
        log.info("rocketmq 收到消息，事件类型：{}，recordId/msg-key：{}， 表：{}.{}",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());

        DbTableDml creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            creator.handle(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:{},请先注册后再做处理!", dtsModel.getTable());
        }
    }
}
