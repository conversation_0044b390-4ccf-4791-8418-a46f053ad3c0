package net.xianmu.usercenter.inbound.tenant.provider;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.api.tenant.service.TenantAccountCommandService;
import net.xianmu.usercenter.client.common.enums.SystemOriginEnum;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountCommandProvider;
import net.xianmu.usercenter.client.tenant.req.TenantAccountCommandReq;
import net.xianmu.usercenter.inbound.tenant.converter.TenantAccountReqConverter;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 11:22
 */
@DubboService
public class TenantAccountCommandProviderImpl implements TenantAccountCommandProvider {

    @Autowired
    private TenantAccountCommandService tenantAccountCommandService;

    @Override
    public DubboResponse create(SystemOriginEnum systemOriginEnum, TenantAccountCommandReq req) {
        return DubboResponse.getOK(tenantAccountCommandService.create(TenantAccountReqConverter.toTenantAccountCommandInput(req)));
    }

    @Override
    public DubboResponse updateByTenantIds(SystemOriginEnum systemOriginEnum, List<Long> list, TenantAccountCommandReq tenantAccountCommandReq) {
        tenantAccountCommandService.updateByTenantIdsAndPhone(list, TenantAccountReqConverter.toTenantAccountCommandInput(tenantAccountCommandReq));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse updateByTenantIdsAndAuthId(net.xianmu.common.enums.base.auth.SystemOriginEnum systemOriginEnum, List<Long> tenantIdList, TenantAccountCommandReq tenantAccountCommandReq) {
        tenantAccountCommandService.updateByTenantIdsAndAuthId(tenantIdList, TenantAccountReqConverter.toTenantAccountCommandInput(tenantAccountCommandReq));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse remove(SystemOriginEnum systemOriginEnum, TenantAccountCommandReq req) {
        tenantAccountCommandService.remove(TenantAccountReqConverter.toTenantAccountCommandInput(req));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse updatePassword(SystemOriginEnum systemOriginEnum, TenantAccountCommandReq req) {
        tenantAccountCommandService.updatePassword(TenantAccountReqConverter.toTenantAccountCommandInput(req));
        return DubboResponse.getOK();
    }
}
