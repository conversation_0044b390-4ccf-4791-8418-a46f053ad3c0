package net.xianmu.usercenter.api.merchant.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:32
 */
@Data
public class MerchantStoreGroupDTO {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 分组名称
     */
    private String merchantStoreGroupName;

    /**
     * 分组id
     */
    private Long merchantStoreGroupId;

    /**
     * 当前分组下的门店数
     */
    private Integer storeNum;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 0、非默认分组 1、默认分组
     */
    private Integer type;
}
