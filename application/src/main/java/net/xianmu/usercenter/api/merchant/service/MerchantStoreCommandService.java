package net.xianmu.usercenter.api.merchant.service;

import net.xianmu.usercenter.common.dto.MerchantStoreBatchImportDTO;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantStoreDomainCommandInput;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:14
 */
public interface MerchantStoreCommandService {

    Long createMerchantStoreInfo(MerchantStoreDomainCommandInput input);

    Boolean updateMerchantStoreInfo(MerchantStoreDomainCommandInput input);

    Boolean updateMerchantStore(MerchantStoreCommandInput input);

    List<MerchantStoreBatchImportDTO> createMerchantStoreInfoBatch(List<MerchantStoreDomainCommandInput> list);
}
