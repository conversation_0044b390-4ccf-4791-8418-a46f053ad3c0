package net.xianmu.usercenter.api.tenant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.tenant.dto.TenantAndBusinessDTO;
import net.xianmu.usercenter.api.tenant.dto.TenantDTO;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:08
 */
public interface TenantQueryService {

    /**
     * 根据id查询租户信息
     * @param
     * @return
     */
     TenantDTO getTenantById(Long id);


    /**
     * 根据id列表查询租户列表
     *
     * @param
     * @return
     */
    List<TenantDTO> getTenantsByIds(List<Long> idList);

    /**
     * 根据指定参数查询租户列表(带分页)
     * @param
     * @return
     */
    PageInfo<TenantAndBusinessEntity> getTenantsPage(TenantQueryInput req, PageQueryInput pageQueryInput);

    /**
     * 根据指定参数查询租户列表
     * 1.如果只查品牌信息注意指定type
     * 2.名称支持模糊匹配
     * @param
     * @return
     */
    List<TenantDTO> getTenants(TenantQueryInput req);

    /**
     * 根据指定参数查询租户以及工商信息
     * 名称支持模糊匹配
     * @param
     * @return
     */
    List<TenantAndBusinessDTO> getTenantAndCompanyList(TenantQueryInput req);

    /**
     * 根据租户id查询租户以及工商信息
     * @param
     * @return
     */
    TenantAndBusinessDTO getTenantAndCompany(Long tenantId);

    /**
     * 根据id列表查询租户以及工商信息
     * @param idList
     * @return
     */
    List<TenantAndBusinessDTO> getTenantAndCompanyByIds(List<Long> idList);


    Integer getStoreCount(MerchantStoreQueryInput req);
}
