package net.xianmu.usercenter.api.tenant.service;

import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/25 11:14
 */
public interface MerchantQueryService {

    MerchantEntity getMerchantByTenantId(Long tenantId);

    List<MerchantEntity> getTenantConfigs(MerchantQueryInput input);
}
