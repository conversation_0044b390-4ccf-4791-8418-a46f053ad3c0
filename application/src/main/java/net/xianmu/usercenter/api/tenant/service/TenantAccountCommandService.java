package net.xianmu.usercenter.api.tenant.service;

import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TenantAccountCommandService {


    /**
     * 创建账户
     */
    Long create(TenantAccountCommandInput req);


    /**
     * 移除账户
     */
    void remove(TenantAccountCommandInput req);


    /**
     * 根据手机号修改指定tenantId的账户信息
     */
    void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccountCommandInput req);


    /**
     * 根据authId修改指定tenantId的账户信息
     */
    void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountCommandInput req);

    /**
     * 修改密码
     */
    void updatePassword(TenantAccountCommandInput req);
}
