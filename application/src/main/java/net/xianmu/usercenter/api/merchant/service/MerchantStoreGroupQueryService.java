package net.xianmu.usercenter.api.merchant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreGroupDTO;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:35
 */
public interface MerchantStoreGroupQueryService {


    /**
     * 根据门店id列表查询分组信息
     * tenantId 用于数据隔离，可为空。为空时不做条件筛选
     * @param
     * @return
     */
    List<MerchantStoreGroupDTO> getGroupByStoreIds(Long tenantId, List<Long> storeIdList);

    /**
     * 根据门店id列表查询分组信息
     * tenantId 用于数据隔离，可为空。为空时不做条件筛选
     * @param
     * @return
     */
    List<MerchantStoreGroupDTO> getGroupByGroupIds(Long tenantId, List<Long> groupIdList);


    List<MerchantStoreGroupEntity> getGroupsWithStoreCount(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput);

    List<MerchantStoreGroupDTO> getMerchantStoreGroups(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput);

    PageInfo<MerchantStoreGroupDTO> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput, PageQueryInput pageQueryInput);
}
