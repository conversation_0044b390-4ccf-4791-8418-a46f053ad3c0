package net.xianmu.usercenter.api.merchant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.api.merchant.dto.MerchantStorePropertiesExtDTO;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-05-08 14:08:17
 */
public interface MerchantStorePropertiesExtQueryService {

    /**
     * 根据id查询门店信息
     *
     * @param
     * @return
     */
    List<MerchantStorePropertiesExtDTO> queryByMidsKey(List<Long> mIds, String proKey);

}