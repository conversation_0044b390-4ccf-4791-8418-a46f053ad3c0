package net.xianmu.usercenter.api.businessInfo.service;

import net.xianmu.usercenter.api.businessInfo.dto.BusinessInformationDTO;
import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;

import java.util.List;

/**
 *
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public interface BusinessInformationQueryService {

    /**
     * 根据业务id+类型查询工商信息
     * 如查租户工商信息：bizId = tenantId，type = 0,查门店工商信息 bizId = storeId,type = 1
     * @param req
     * @return
     */
    BusinessInformationDTO getBusinessInfoByBizIdAndType(BusinessInformationQueryInput input);


    /**
     * 根据指定参数查询列表
     * @param req
     * @return
     */
    List<BusinessInformationDTO> getBusinessInfos(BusinessInformationQueryInput input);

}