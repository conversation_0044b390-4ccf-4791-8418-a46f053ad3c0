package net.xianmu.usercenter.api.merchant.service;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:14
 */
public interface MerchantStoreAccountCommandService {

    Long create(MerchantStoreAccountCommandInput input);

    Boolean update(MerchantStoreAccountCommandInput input);

    Boolean remove(MerchantStoreAccountCommandInput input);

    Boolean removeBatch(MerchantStoreAccountCommandInput input);

    Boolean updateStatusBatch(List<Long> idList, Integer status);
}
