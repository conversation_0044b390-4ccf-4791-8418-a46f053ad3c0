package net.xianmu.usercenter.api.regional.service;

import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;

import java.util.List;

/**
 *
 * @date 2023-07-06 16:46:41
 * @version 1.0
 *
 */
public interface RegionalOrganizationQueryService {

    /**
     * saas 根据租户id查询区域组织
     * @param tenantId
     * @return
     */
    RegionalOrganizationEntity saasGetRegionalByTenantId(Long tenantId);

    /**
     * saas 根据租户id查询区域组织
     * @param tenantId
     * @return
     */
    List<RegionalOrganizationEntity> saasGetRegionalByTenantId(List<Long> tenantIds);

}