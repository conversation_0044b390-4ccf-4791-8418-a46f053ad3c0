package net.xianmu.usercenter.api.merchant.service;

import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;

import java.util.List;

/**
 *
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public interface MerchantAddressQueryService {

    /**
     * 根据指定参数查询列表
     */
    List<MerchantAddressEntity> getMerchantAddressList(MerchantAddressQueryInput req);

    List<MerchantAddressEntity> getAddressAndContacts(MerchantAddressQueryInput req);

    List<String> getConcatAddress(Long tenantId);


}