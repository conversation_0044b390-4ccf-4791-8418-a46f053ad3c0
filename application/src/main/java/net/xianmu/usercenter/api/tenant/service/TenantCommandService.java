package net.xianmu.usercenter.api.tenant.service;

import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:56
 */
public interface TenantCommandService {

    /**
     * 新增品牌（涉及租户、工商、品牌下的门店等信息）
     */
    Long createTenantInfo(TenantCommandInput input);

    /**
     * 编辑租户信息
     */
    void update(TenantCommandInput input);

    /**
     * 批量修改操作人
     * @param
     * @return
     */
    void batchUpdateOp(List<Long> ids, String opUname, Long opUid);
}
