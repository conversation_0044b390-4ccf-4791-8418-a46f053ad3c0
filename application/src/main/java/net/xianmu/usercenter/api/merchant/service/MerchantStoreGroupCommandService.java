package net.xianmu.usercenter.api.merchant.service;

import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.usercenter.common.dto.MerchantStoreGroupBatchImportDTO;
import net.xianmu.usercenter.common.input.command.MerchantStoreGroupCommandInput;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:35
 */
public interface MerchantStoreGroupCommandService {

    Long create(MerchantStoreGroupCommandInput input);

    Boolean update(MerchantStoreGroupCommandInput input);

    Boolean remove(MerchantStoreGroupCommandInput input);

    List<MerchantStoreGroupBatchImportDTO> batchCreate(List<MerchantStoreGroupBatchImportDTO> list, Long tenantId);

}
