package net.xianmu.usercenter.api.businessInfo.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/4/25 11:27
 */
@Data
public class BusinessInformationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long bizId;

    /**
     * 类型：0-品牌用户，1-单店用户
     */
    private Integer type;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String phone;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
