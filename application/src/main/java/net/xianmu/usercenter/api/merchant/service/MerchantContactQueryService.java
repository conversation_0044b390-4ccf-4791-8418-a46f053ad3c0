package net.xianmu.usercenter.api.merchant.service;

import net.xianmu.usercenter.api.merchant.dto.MerchantAddressDTO;
import net.xianmu.usercenter.api.merchant.dto.MerchantContactDTO;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;

import java.util.List;

/**
 *
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
public interface MerchantContactQueryService {

    /**
     * 根据指定参数查询列表
     */
    List<MerchantContactDTO> getMerchantContacts(MerchantContactQueryInput req);


    /**
     * 根据指定参数查询列表
     */
    List<MerchantContactDTO> getMerchantContactsByStoreId(Long tenantId, Long storeId);

}