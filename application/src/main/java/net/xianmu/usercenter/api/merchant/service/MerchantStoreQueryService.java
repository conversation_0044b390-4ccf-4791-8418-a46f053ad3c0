package net.xianmu.usercenter.api.merchant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreDTO;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.List;

/**
 * @version 1.0
 * @date 2023-05-08 14:08:17
 */
public interface MerchantStoreQueryService {

    /**
     * 根据id查询门店信息
     *
     * @param
     * @return
     */
    MerchantStoreEntity getMerchantStoreById(Long id);

    /**
     * 根据id列表查询门店信息列表
     *
     * @param
     * @return
     */
    List<MerchantStoreEntity> getMerchantStoresByIds(List<Long> idList);


    /**
     * 根据指定参数查询门店列表
     * 1.支持店铺名称模糊匹配
     *
     * @param
     * @return
     */
    List<MerchantStoreEntity> getMerchantStores(MerchantStoreQueryInput req);

    PageInfo<MerchantStoreDTO> getMerchantStoreAndAddressPage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput);

    PageInfo<MerchantStoreDTO> selectMerchantStorePage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput);



    /**
     * 根据指定参数查询门店&拓展属性列表
     *
     */
    List<MerchantStoreEntity> getMerchantStoreAndExtends(MerchantStoreQueryInput req);

    List<Long> selectMIdListByCondition(MerchantStoreQueryInput req);


    PageInfo<MerchantStoreEntity> selectMerchantStorePageForXM(MerchantStorePageQueryInput input);
}