package net.xianmu.usercenter.api.merchant.dto;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
@Data
public class MerchantAddressDTO {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 们拍好
	 */
	private String houseNumber;

	/**
	 * 商家腾讯地图坐标
	 */
	private String poiNote;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


	/**
	 * 是否是默认地址：0、否 1、是
	 */
	private Integer defaultFlag;

	/**
	 * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
	 */
	private Integer status;

	/**
	 * 联系人名称
	 */
	private String contactName;

	/**
	 * 联系人电话
	 */
	private String contactPhone;

	/**
	 * 完整收货地址
	 */
	private String deliveryAddress;

	
}