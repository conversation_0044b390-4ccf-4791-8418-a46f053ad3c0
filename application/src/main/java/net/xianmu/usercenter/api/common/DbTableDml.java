package net.xianmu.usercenter.api.common;


import net.xianmu.usercenter.common.dto.DtsModel;
import net.xianmu.usercenter.common.dto.XmDataTransferTaskParamDTO;
import net.xianmu.usercenter.common.dto.XmDataTransferTaskParamListDTO;

/**
 * <AUTHOR>
 * @Description 表订阅公共类,所有新订阅表均需实现该接口
 * @date 2022/4/22 10:36
 */
public interface DbTableDml {

    /**
     * 表DML操作后的业务逻辑,均通过此接口方法实现
     * @param dtsModel DML操作变动内容
     */
    void handle(DtsModel dtsModel);

    /**
     * 基于起始批次初始化任务用
     * @param dto
     * @param isCreate
     * @throws InterruptedException
     */
    void handleForTask(XmDataTransferTaskParamListDTO dto, boolean isCreate) throws InterruptedException;
}
