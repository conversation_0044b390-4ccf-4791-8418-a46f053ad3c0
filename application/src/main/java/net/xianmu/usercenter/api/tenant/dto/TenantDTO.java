package net.xianmu.usercenter.api.tenant.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 11:12
 */
@Data
public class TenantDTO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型：0-供应商,1-品牌方,2-帆台
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 大客户Id
     */
    private Long adminId;

    /**
     * 操作人id
     */
    private Long opUid;

    /**
     * 操作人名称
     */
    private String opUname;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 归属DB
     */
    private String belongDB;

    /**
     * 微信分账开关
     */
    private Integer profitSharingSwitch;

    /**
     * 分账渠道 0 微信 1 汇付
     */
    private Integer onlinePayChannel;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;
}
