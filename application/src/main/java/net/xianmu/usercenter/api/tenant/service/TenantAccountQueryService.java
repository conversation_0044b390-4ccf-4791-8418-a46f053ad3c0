package net.xianmu.usercenter.api.tenant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.tenant.dto.TenantAccountDTO;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:24
 */
public interface TenantAccountQueryService {

    /**
     * 根据auth_user_id查询账户
     * @return
     */
    TenantAccountDTO getTenantAccount(Long authUserId);


    /**
     * 根据auth_user_id查询账户
     * @return
     */
    List<TenantAccountDTO> getTenantAccountsByAuthUserIds(List<Long> list);



    /**
     * 根据auth_user_id查询账户
     * @return
     */
    List<TenantAccountDTO> getTenantAccounts(TenantAccountListQueryInput req);


    /**
     * 根据id查询账户
     * @return
     */
    TenantAccountDTO getTenantAccountById(Long id);


    /**
     * 根据id查询账户
     * @return
     */
    List<TenantAccountDTO> getTenantAccountByTenantIdsAndPhone(List<Long> tenantIdList, String phone);


    /**
     * 根据指定参数查询账户信息(带分页)
     * @param
     * @return
     */
    PageInfo<TenantAccountDTO> getTenantAccountsPage(TenantAccountListQueryInput input, PageQueryInput pageQueryInput);
}
