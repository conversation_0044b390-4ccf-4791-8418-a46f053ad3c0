package net.xianmu.usercenter.api.merchant.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.api.merchant.dto.MerchantStoreAccountDTO;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.List;

/**
 *
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public interface MerchantStoreAccountQueryService {

    /**
     * 根据id查询门店信息
     * @param
     * @return
     */
    MerchantStoreAccountEntity getMerchantStoreAccountById(Long id);

    /**
     * 根据id列表查询门店信息列表
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> getMerchantStoreAccountsByIds(List<Long> idList);


    /**
     * 根据指定参数查询门店列表（不支持模糊匹配）
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> getMerchantStoreAccounts(MerchantStoreAccountQueryInput req);

    /**
     * 根据指定参数查询门店列表（支持模糊匹配）
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> getMerchantStoreAccountsWithFuzzy(MerchantStoreAccountQueryInput req);


    /**
     * 账户分页（不支持模糊匹配）
     * @param
     * @return
     */
    PageInfo<MerchantStoreEntity> getAccountPage(MerchantStoreAccountQueryInput req, PageQueryInput page);



}