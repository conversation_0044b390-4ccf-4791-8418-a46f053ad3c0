<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>usercenter</artifactId>
        <groupId>net.xianmu</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>usercenter-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-domain</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>usercenter-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-common</artifactId>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- alibaba开源数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.4.1</version>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>
</project>
