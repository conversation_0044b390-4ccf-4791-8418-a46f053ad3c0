package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreAccountMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 14:19
 */
@Component
public class MerchantStoreAccountDao extends ServiceImpl<MerchantStoreAccountMapper, MerchantStoreAccount> {

    public List<MerchantStoreAccount> listByCondition(MerchantStoreAccountQueryInput req) {
        LambdaQueryWrapper<MerchantStoreAccount> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantStoreAccount::getId, req.getId());
        query.in(CollUtil.isNotEmpty(req.getIdList()), MerchantStoreAccount::getId, req.getIdList());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreAccount::getStoreId, req.getStoreId());
        query.eq(Objects.nonNull(req.getMId()), MerchantStoreAccount::getMId, req.getMId());
        query.in(CollUtil.isNotEmpty(req.getMIdList()), MerchantStoreAccount::getMId, req.getMIdList());
        query.in(CollUtil.isNotEmpty(req.getStoreIdList()), MerchantStoreAccount::getStoreId, req.getStoreIdList());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreAccount::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getXmAccountId()), MerchantStoreAccount::getXmAccountId, req.getXmAccountId());
        query.in(CollUtil.isNotEmpty(req.getXmAccountIdList()), MerchantStoreAccount::getXmAccountId, req.getXmAccountIdList());
        query.eq(Objects.nonNull(req.getMId()), MerchantStoreAccount::getMId, req.getMId());
        query.eq(Objects.nonNull(req.getType()), MerchantStoreAccount::getType, req.getType());
        query.eq(Objects.nonNull(req.getStatus()), MerchantStoreAccount::getStatus, req.getStatus());
        query.eq(Objects.nonNull(req.getDeleteFlag()), MerchantStoreAccount::getDeleteFlag, req.getDeleteFlag());
        query.eq(StrUtil.isNotBlank(req.getPhone()), MerchantStoreAccount::getPhone, req.getPhone());
        query.likeRight(StrUtil.isNotBlank(req.getPhonePrefix()), MerchantStoreAccount::getPhone, req.getPhonePrefix());
        return list(query);
    }

    public List<MerchantStoreAccount> listByConditionWithFuzzy(MerchantStoreAccountQueryInput req) {
        LambdaQueryWrapper<MerchantStoreAccount> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreAccount::getStoreId, req.getStoreId());
        query.eq(Objects.nonNull(req.getMId()), MerchantStoreAccount::getMId, req.getMId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreAccount::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getType()), MerchantStoreAccount::getType, req.getType());
        query.eq(Objects.nonNull(req.getStatus()), MerchantStoreAccount::getStatus, req.getStatus());
        query.eq(Objects.nonNull(req.getDeleteFlag()), MerchantStoreAccount::getDeleteFlag, req.getDeleteFlag());
        query.like(StrUtil.isNotBlank(req.getPhone()), MerchantStoreAccount::getPhone, req.getPhone());
        return list(query);
    }

    public List<MerchantStoreAccount> selectSaasAccount() {
        LambdaQueryWrapper<MerchantStoreAccount> query = new LambdaQueryWrapper<>();
        query.ne(MerchantStoreAccount::getTenantId, TenantDefaultConstant.XIAN_MU_TENANT_ID);
        return list(query);
    }


    public void updateManagerToClerk(Long storeId, Long currentAccountId){
        LambdaUpdateWrapper<MerchantStoreAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MerchantStoreAccount::getType, MerchantAccountEnums.Type.CLERK.getCode());
        wrapper.ne(MerchantStoreAccount::getId, currentAccountId);
        wrapper.eq(MerchantStoreAccount::getStoreId, storeId);
        wrapper.eq(MerchantStoreAccount::getType, MerchantAccountEnums.Type.MANAGER.getCode());
        update(wrapper);
    }



    public Boolean updateStatusBatch(List<Long> idList, Integer status){
        LambdaUpdateWrapper<MerchantStoreAccount> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MerchantStoreAccount::getStatus, status);
        wrapper.in(MerchantStoreAccount::getId, idList);
        return update(wrapper);
    }

}
