package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStorePropertiesExtRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStorePropertiesExtConverter;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStorePropertiesExtMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStorePropertiesExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MerchantStorePropertiesExtRepositoryImpl implements MerchantStorePropertiesExtRepository {
    @Resource
    MerchantStorePropertiesExtMapper merchantStorePropertiesExtMapper;
    @Override
    public List<MerchantStorePropertiesExtEntity> selectByMIds(List<Long> mIds, String proKey) {
        List<MerchantStorePropertiesExt> merchantStorePropertiesExts = merchantStorePropertiesExtMapper.selectByMidsKey(mIds, proKey);
        return merchantStorePropertiesExts.stream().map(MerchantStorePropertiesExtConverter::toMerchantStorePropertiesExtEntity).collect(Collectors.toList());
    }

    @Override
    public Boolean createOrUpdate(MerchantStorePropertiesExtEntity entity) {
        merchantStorePropertiesExtMapper.insertSelectiveOnDuplicate(MerchantStorePropertiesExtConverter.toMerchantStorePropertiesExt(entity));
        return true;
    }
}
