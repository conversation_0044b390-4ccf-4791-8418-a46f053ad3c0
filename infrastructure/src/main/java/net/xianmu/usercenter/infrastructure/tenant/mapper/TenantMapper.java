package net.xianmu.usercenter.infrastructure.tenant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.infrastructure.tenant.model.Tenant;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-05 11:32:09
 * @version 1.0
 *
 */
@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {
    /**
     * 查询租户列表
     *
     * @param req
     * @return
     */
    List<Tenant> selectTenantsByCondition(TenantQueryInput req);


    /**
     * 查询租户列表（不支持模糊匹配）
     *
     * @param req
     * @return
     */
    List<Tenant> selectTenantsByConditionNonFuzzy(TenantQueryInput req);

    /**
     * 查询租户以及工商信息
     *
     * @param req
     * @return
     */
    List<TenantAndBusinessModel> selectTenantAndBusinessModelsByCondition(TenantQueryInput req);

    /**
     * 查询租户以及工商信息
     *
     * @param req
     * @return
     */
    List<TenantAndBusinessModel> selectTenantAndBusinessModelsByIds(@Param("idList") List<Long> idList);


    /**
     * 查询租户分页列表
     *
     * @param req
     * @return
     */
    List<TenantAndBusinessModel> getTenantsPage (TenantQueryInput req);


    /**
     * 批量修改操作人
     *
     * @param ids
     * @param opUname
     * @param opUid
     */
    void batcheUpdateOp(@Param("ids") List<Long> ids, @Param("opUname") String opUname, @Param("opUid") Long opUid);
}
