package net.xianmu.usercenter.infrastructure.tenant.converter;

import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAccount;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 14:25
 */
public class TenantAccountConverter {


    private TenantAccountConverter() {
        // 无需实现
    }

    public static List<TenantAccountEntity> toTenantAccountEntityList(List<TenantAccount> tenantAccountList) {
        if (tenantAccountList == null) {
            return Collections.emptyList();
        }
        List<TenantAccountEntity> tenantAccountEntityList = new ArrayList<>();
        for (TenantAccount tenantAccount : tenantAccountList) {
            tenantAccountEntityList.add(toTenantAccountEntity(tenantAccount));
        }
        return tenantAccountEntityList;
    }

    public static TenantAccountEntity toTenantAccountEntity(TenantAccount tenantAccount) {
        if (tenantAccount == null) {
            return null;
        }
        TenantAccountEntity tenantAccountEntity = new TenantAccountEntity();
        tenantAccountEntity.setId(tenantAccount.getId());
        tenantAccountEntity.setAuthUserId(tenantAccount.getAuthUserId());
        tenantAccountEntity.setTenantId(tenantAccount.getTenantId());
        tenantAccountEntity.setPhone(tenantAccount.getPhone());
        tenantAccountEntity.setNickname(tenantAccount.getNickname());
        tenantAccountEntity.setProfilePicture(tenantAccount.getProfilePicture());
        tenantAccountEntity.setStatus(tenantAccount.getStatus());
        tenantAccountEntity.setOperatorTime(tenantAccount.getOperatorTime());
        tenantAccountEntity.setOperatorPhone(tenantAccount.getOperatorPhone());
        tenantAccountEntity.setUpdateTime(tenantAccount.getUpdateTime());
        tenantAccountEntity.setCreateTime(tenantAccount.getCreateTime());
        tenantAccountEntity.setDeletedFlag(tenantAccount.getDeletedFlag());
        tenantAccountEntity.setEmail(tenantAccount.getEmail());
        tenantAccountEntity.setUpdater(tenantAccount.getUpdater());
        return tenantAccountEntity;
    }

    public static List<TenantAccount> toTenantAccountList(List<TenantAccountEntity> tenantAccountEntityList) {
        if (tenantAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<TenantAccount> tenantAccountList = new ArrayList<>();
        for (TenantAccountEntity tenantAccountEntity : tenantAccountEntityList) {
            tenantAccountList.add(toTenantAccount(tenantAccountEntity));
        }
        return tenantAccountList;
    }

    public static TenantAccount toTenantAccount(TenantAccountEntity tenantAccountEntity) {
        if (tenantAccountEntity == null) {
            return null;
        }
        TenantAccount tenantAccount = new TenantAccount();
        tenantAccount.setId(tenantAccountEntity.getId());
        tenantAccount.setAuthUserId(tenantAccountEntity.getAuthUserId());
        tenantAccount.setTenantId(tenantAccountEntity.getTenantId());
        tenantAccount.setPhone(tenantAccountEntity.getPhone());
        tenantAccount.setNickname(tenantAccountEntity.getNickname());
        tenantAccount.setProfilePicture(tenantAccountEntity.getProfilePicture());
        tenantAccount.setStatus(tenantAccountEntity.getStatus());
        tenantAccount.setOperatorTime(tenantAccountEntity.getOperatorTime());
        tenantAccount.setOperatorPhone(tenantAccountEntity.getOperatorPhone());
        tenantAccount.setUpdateTime(tenantAccountEntity.getUpdateTime());
        tenantAccount.setCreateTime(tenantAccountEntity.getCreateTime());
        tenantAccount.setDeletedFlag(tenantAccountEntity.getDeletedFlag());
        tenantAccount.setEmail(tenantAccountEntity.getEmail());
        tenantAccount.setUpdater(tenantAccountEntity.getUpdater());
        return tenantAccount;
    }
}
