package net.xianmu.usercenter.infrastructure.tenant.converter;

import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.infrastructure.tenant.model.Tenant;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-05 11:32:09
 */
public class TenantConverter {


    private TenantConverter() {
        // 无需实现
    }


    public static Tenant toTenant(TenantEntity tenantEntity) {
        if (tenantEntity == null) {
            return null;
        }
        Tenant tenant = new Tenant();
        tenant.setId(tenantEntity.getId());
        tenant.setPhone(tenantEntity.getPhone());
        tenant.setPassword(tenantEntity.getPassword());
        tenant.setTenantName(tenantEntity.getTenantName());
        tenant.setType(tenantEntity.getType());
        tenant.setStatus(tenantEntity.getStatus());
        tenant.setCreateTime(tenantEntity.getCreateTime());
        tenant.setUpdateTime(tenantEntity.getUpdateTime());
        tenant.setAdminId(tenantEntity.getAdminId());
        tenant.setOpUid(tenantEntity.getOpUid());
        tenant.setOpUname(tenantEntity.getOpUname());
        tenant.setOperator(tenantEntity.getOperator());
        tenant.setBelongDb(tenantEntity.getBelongDB());
        tenant.setProfitSharingSwitch(tenantEntity.getProfitSharingSwitch());
        tenant.setOnlinePayChannel(tenantEntity.getOnlinePayChannel());
        tenant.setEmail(tenantEntity.getEmail());
        tenant.setAccountLoginType(tenantEntity.getAccountLoginType());
        return tenant;
    }

    public static List<TenantEntity> toTenantEntityList(List<Tenant> tenantList) {
        if (tenantList == null) {
            return Collections.emptyList();
        }
        List<TenantEntity> tenantEntityList = new ArrayList<>();
        for (Tenant tenant : tenantList) {
            tenantEntityList.add(toTenantEntity(tenant));
        }
        return tenantEntityList;
    }

    public static TenantEntity toTenantEntity(Tenant tenant) {
        if (tenant == null) {
            return null;
        }
        TenantEntity tenantEntity = new TenantEntity();
        tenantEntity.setId(tenant.getId());
        tenantEntity.setPhone(tenant.getPhone());
        tenantEntity.setPassword(tenant.getPassword());
        tenantEntity.setTenantName(tenant.getTenantName());
        tenantEntity.setType(tenant.getType());
        tenantEntity.setStatus(tenant.getStatus());
        tenantEntity.setCreateTime(tenant.getCreateTime());
        tenantEntity.setUpdateTime(tenant.getUpdateTime());
        tenantEntity.setAdminId(tenant.getAdminId());
        tenantEntity.setOpUid(tenant.getOpUid());
        tenantEntity.setOpUname(tenant.getOpUname());
        tenantEntity.setOperator(tenant.getOperator());
        tenantEntity.setBelongDB(tenant.getBelongDb());
        tenantEntity.setProfitSharingSwitch(tenant.getProfitSharingSwitch());
        tenantEntity.setOnlinePayChannel(tenant.getOnlinePayChannel());
        tenantEntity.setEmail(tenant.getEmail());
        tenantEntity.setAccountLoginType(tenant.getAccountLoginType());
        return tenantEntity;
    }


    /**
     * ************************* TenantAndBusinessEntity ***************************
     */

    public static List<TenantAndBusinessEntity> toTenantAndBusinessEntityList(List<TenantAndBusinessModel> tenantAndBusinessModelList) {
        if (tenantAndBusinessModelList == null) {
            return Collections.emptyList();
        }
        List<TenantAndBusinessEntity> tenantAndBusinessEntityList = new ArrayList<>();
        for (TenantAndBusinessModel tenantAndBusinessModel : tenantAndBusinessModelList) {
            tenantAndBusinessEntityList.add(toTenantAndBusinessEntity(tenantAndBusinessModel));
        }
        return tenantAndBusinessEntityList;
    }

    public static TenantAndBusinessEntity toTenantAndBusinessEntity(TenantAndBusinessModel tenantAndBusinessModel) {
        if (tenantAndBusinessModel == null) {
            return null;
        }
        TenantAndBusinessEntity tenantAndBusinessEntity = new TenantAndBusinessEntity();
        tenantAndBusinessEntity.setTenantId(tenantAndBusinessModel.getTenantId());
        tenantAndBusinessEntity.setTenantName(tenantAndBusinessModel.getTenantName());
        tenantAndBusinessEntity.setPhone(tenantAndBusinessModel.getPhone());
        tenantAndBusinessEntity.setCompanyName(tenantAndBusinessModel.getCompanyName());
        tenantAndBusinessEntity.setCreditCode(tenantAndBusinessModel.getCreditCode());
        tenantAndBusinessEntity.setTenantCompanyId(tenantAndBusinessModel.getTenantCompanyId());
        tenantAndBusinessEntity.setAdminId(tenantAndBusinessModel.getAdminId());
        tenantAndBusinessEntity.setCreateTime(tenantAndBusinessModel.getCreateTime());
        tenantAndBusinessEntity.setUpdateTime(tenantAndBusinessModel.getUpdateTime());
        tenantAndBusinessEntity.setOpUname(tenantAndBusinessModel.getOpUname());
        tenantAndBusinessEntity.setContactName(tenantAndBusinessModel.getContactName());
        tenantAndBusinessEntity.setTenantType(tenantAndBusinessModel.getTenantType());
        tenantAndBusinessEntity.setStatus(tenantAndBusinessModel.getStatus());
        return tenantAndBusinessEntity;
    }


}
