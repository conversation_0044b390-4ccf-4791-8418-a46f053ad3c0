package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantAddress;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantAddressAndContactModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */

@Mapper
public interface MerchantAddressMapper extends BaseMapper<MerchantAddress> {

    List<MerchantAddressAndContactModel> selectDefaultMerchantAddressList(@Param("storeIdList")List<Long> storeIdList, @Param("tenantId")Long tenantId);

    List<String> selectConcatAddressByTenantIdAndStatus(@Param("tenantId")Long tenantId, @Param("status")Integer status);


    List<MerchantAddress> selectXmErrorAddress();

}
