package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreAccountConverter {


    private MerchantStoreAccountConverter() {
        // 无需实现
    }

    public static List<MerchantStoreAccountEntity> toMerchantStoreAccountEntityList(List<MerchantStoreAccount> merchantStoreAccountList) {
        if (merchantStoreAccountList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountEntity> merchantStoreAccountEntityList = new ArrayList<>();
        for (MerchantStoreAccount merchantStoreAccount : merchantStoreAccountList) {
            merchantStoreAccountEntityList.add(toMerchantStoreAccountEntity(merchantStoreAccount));
        }
        return merchantStoreAccountEntityList;
    }

    public static MerchantStoreAccountEntity toMerchantStoreAccountEntity(MerchantStoreAccount merchantStoreAccount) {
        if (merchantStoreAccount == null) {
            return null;
        }
        MerchantStoreAccountEntity merchantStoreAccountEntity = new MerchantStoreAccountEntity();
        merchantStoreAccountEntity.setId(merchantStoreAccount.getId());
        merchantStoreAccountEntity.setTenantId(merchantStoreAccount.getTenantId());
        merchantStoreAccountEntity.setStoreId(merchantStoreAccount.getStoreId());
        merchantStoreAccountEntity.setAccountName(merchantStoreAccount.getAccountName());
        merchantStoreAccountEntity.setPhone(merchantStoreAccount.getPhone());
        merchantStoreAccountEntity.setType(merchantStoreAccount.getType());
        merchantStoreAccountEntity.setRegisterTime(merchantStoreAccount.getRegisterTime());
        merchantStoreAccountEntity.setAuditTime(merchantStoreAccount.getAuditTime());
        merchantStoreAccountEntity.setOpenId(merchantStoreAccount.getOpenId());
        merchantStoreAccountEntity.setUnionId(merchantStoreAccount.getUnionId());
        merchantStoreAccountEntity.setCreateTime(merchantStoreAccount.getCreateTime());
        merchantStoreAccountEntity.setUpdateTime(merchantStoreAccount.getUpdateTime());
        merchantStoreAccountEntity.setStatus(merchantStoreAccount.getStatus());
        merchantStoreAccountEntity.setLastLoginTime(merchantStoreAccount.getLastLoginTime());
        merchantStoreAccountEntity.setDeleteFlag(merchantStoreAccount.getDeleteFlag());
        merchantStoreAccountEntity.setMId(merchantStoreAccount.getMId());
        merchantStoreAccountEntity.setOaOpenId(merchantStoreAccount.getOaOpenId());
        merchantStoreAccountEntity.setXmAccountId(merchantStoreAccount.getXmAccountId());
        return merchantStoreAccountEntity;
    }

    public static List<MerchantStoreAccount> toMerchantStoreAccountList(List<MerchantStoreAccountEntity> merchantStoreAccountEntityList) {
        if (merchantStoreAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccount> merchantStoreAccountList = new ArrayList<>();
        for (MerchantStoreAccountEntity merchantStoreAccountEntity : merchantStoreAccountEntityList) {
            merchantStoreAccountList.add(toMerchantStoreAccount(merchantStoreAccountEntity));
        }
        return merchantStoreAccountList;
    }

    public static MerchantStoreAccount toMerchantStoreAccount(MerchantStoreAccountEntity merchantStoreAccountEntity) {
        if (merchantStoreAccountEntity == null) {
            return null;
        }
        MerchantStoreAccount merchantStoreAccount = new MerchantStoreAccount();
        merchantStoreAccount.setId(merchantStoreAccountEntity.getId());
        merchantStoreAccount.setTenantId(merchantStoreAccountEntity.getTenantId());
        merchantStoreAccount.setStoreId(merchantStoreAccountEntity.getStoreId());
        merchantStoreAccount.setAccountName(merchantStoreAccountEntity.getAccountName());
        merchantStoreAccount.setPhone(merchantStoreAccountEntity.getPhone());
        merchantStoreAccount.setType(merchantStoreAccountEntity.getType());
        merchantStoreAccount.setRegisterTime(merchantStoreAccountEntity.getRegisterTime());
        merchantStoreAccount.setAuditTime(merchantStoreAccountEntity.getAuditTime());
        merchantStoreAccount.setOpenId(merchantStoreAccountEntity.getOpenId());
        merchantStoreAccount.setUnionId(merchantStoreAccountEntity.getUnionId());
        merchantStoreAccount.setCreateTime(merchantStoreAccountEntity.getCreateTime());
        merchantStoreAccount.setUpdateTime(merchantStoreAccountEntity.getUpdateTime());
        merchantStoreAccount.setStatus(merchantStoreAccountEntity.getStatus());
        merchantStoreAccount.setLastLoginTime(merchantStoreAccountEntity.getLastLoginTime());
        merchantStoreAccount.setDeleteFlag(merchantStoreAccountEntity.getDeleteFlag());
        merchantStoreAccount.setMId(merchantStoreAccountEntity.getMId());
        merchantStoreAccount.setOaOpenId(merchantStoreAccountEntity.getOaOpenId());
        merchantStoreAccount.setXmAccountId(merchantStoreAccountEntity.getXmAccountId());
        return merchantStoreAccount;
    }
}
