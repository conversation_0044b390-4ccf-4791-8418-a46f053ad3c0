package net.xianmu.usercenter.infrastructure.regional.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-06 16:32:07
 * @version 1.0
 *
 */
@Data
public class RegionalOrganization {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 组织名称
	 */
	private String organizationName;

	/**
	 * 组织来源：0-saas,1-鲜沐
	 */
	private Integer source;

	/**
	 * 客户类型：1-大客户，2-单店
	 */
	private Integer size;

	/**
	 * 组织状态： 0正常 1禁用
	 */
	private Integer status;

	/**
	 * 鲜沐大客户id
	 */
	private Long adminId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 操作人名称
	 */
	private String updater;

	

	
}