package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.usercenter.common.enums.MerchantAddressEnums;
import net.xianmu.usercenter.common.enums.SortTypeEnum;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantAddressMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantAddress;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */

@Component
public class MerchantAddressDao extends ServiceImpl<MerchantAddressMapper, MerchantAddress> {

    /**
     * @param
     * @return
     */
    public List<MerchantAddress> listByCondition(MerchantAddressQueryInput req) {
        QueryWrapper<MerchantAddress> queryWrapper = new QueryWrapper<MerchantAddress>();
        List<PageSortInput> sortList = req.getSortList();
        if(CollUtil.isNotEmpty(sortList)) {
            sortList.forEach(input -> queryWrapper.orderBy(true, SortTypeEnum.ASCENDING.getKeyWord().equals(input.getSortBy()), input.getOrderBy()));
        }

        LambdaQueryWrapper<MerchantAddress> query = queryWrapper.lambda();
        query.eq(Objects.nonNull(req.getId()), MerchantAddress::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantAddress::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantAddress::getStoreId, req.getStoreId());
        query.in(CollUtil.isNotEmpty(req.getStoreIdList()), MerchantAddress::getStoreId, req.getStoreIdList());
        query.eq(Objects.nonNull(req.getStatus()), MerchantAddress::getStatus, req.getStatus());
        query.eq(Objects.nonNull(req.getDefaultFlag()), MerchantAddress::getDefaultFlag, req.getDefaultFlag());
        query.eq(!StringUtils.isEmpty(req.getProvince()), MerchantAddress::getProvince, req.getProvince());
        query.eq(!StringUtils.isEmpty(req.getCity()), MerchantAddress::getCity, req.getCity());
        query.in(CollUtil.isNotEmpty(req.getCityList()), MerchantAddress::getCity, req.getCityList());
        query.in(CollUtil.isNotEmpty(req.getXmContactIdList()), MerchantAddress::getXmContactId, req.getXmContactIdList());
        query.eq(!StringUtils.isEmpty(req.getArea()), MerchantAddress::getArea, req.getArea());
        query.in(CollUtil.isNotEmpty(req.getAreaList()), MerchantAddress::getArea, req.getAreaList());
        query.eq(Objects.nonNull(req.getXmContactId()), MerchantAddress::getXmContactId, req.getXmContactId());
        query.eq(Objects.nonNull(req.getMId()), MerchantAddress::getMId, req.getMId());
        return list(query);
    }


    public List<MerchantAddress> selectDefaultMerchantAddressList(List<Long> storeIdList, Long tenantId) {
        LambdaQueryWrapper<MerchantAddress> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(tenantId), MerchantAddress::getTenantId, tenantId);
        query.eq(MerchantAddress::getStatus, MerchantAddressEnums.status.NORMAL);
        query.eq(MerchantAddress::getDefaultFlag, MerchantAddressEnums.DefaultFlag.DEFAULT);
        query.in(CollUtil.isNotEmpty(storeIdList), MerchantAddress::getStoreId, storeIdList);
        return list(query);
    }
}
