package net.xianmu.usercenter.infrastructure.tenant.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;
import net.xianmu.usercenter.domain.tenant.repository.TenantAccountQueryRepository;
import net.xianmu.usercenter.infrastructure.tenant.converter.TenantAccountConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.TenantAccountDao;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:39
 */
@Repository
public class TenantAccountQueryRepositoryImpl implements TenantAccountQueryRepository {

    @Autowired
    private TenantAccountDao tenantAccountDao;

    @Override
    public TenantAccountEntity selectByAuthUserId(Long authUserId) {
        return TenantAccountConverter.toTenantAccountEntity(tenantAccountDao.selectByAuthUserId(authUserId));
    }

    @Override
    public List<TenantAccountEntity> selectByAuthUserIds(List<Long> authUserIdList) {
        return TenantAccountConverter.toTenantAccountEntityList(tenantAccountDao.selectByAuthUserIdList(authUserIdList));
    }

    @Override
    public List<TenantAccountEntity> selectByByCondition(TenantAccountListQueryInput req) {
        return TenantAccountConverter.toTenantAccountEntityList(tenantAccountDao.listByCondition(req));
    }

    @Override
    public TenantAccountEntity selectById(Long id) {
        return TenantAccountConverter.toTenantAccountEntity(tenantAccountDao.getById(id));
    }

    @Override
    public TenantAccountEntity selectByPhoneAndTenantId(Long tenantId, String phone) {
        return TenantAccountConverter.toTenantAccountEntity(tenantAccountDao.selectByPhoneAndTenantId(tenantId, phone));
    }

    @Override
    public TenantAccountEntity selectByEmailAndTenantId(Long tenantId, String email) {
        return TenantAccountConverter.toTenantAccountEntity(tenantAccountDao.selectByEmailAndTenantId(tenantId, email));
    }

    @Override
    public List<TenantAccountEntity> getTenantAccountByTenantIdsAndPhone(List<Long> tenantIdList, String phone) {
        return TenantAccountConverter.toTenantAccountEntityList(tenantAccountDao.selectTenantAccountByTenantIdsAndPhone(phone, tenantIdList));
    }

    @Override
    public PageInfo<TenantAccountEntity> getTenantAccountPage(TenantAccountListQueryInput req, PageQueryInput pageQueryInput) {
        PageHelper.startPage(pageQueryInput.getPageIndex(), pageQueryInput.getPageSize());
        List<TenantAccount> tenantAccounts = tenantAccountDao.pageByCondition(req);
        return PageInfoConverter.toPageResp(new PageInfo<>(tenantAccounts), TenantAccountConverter::toTenantAccountEntity);
    }
}
