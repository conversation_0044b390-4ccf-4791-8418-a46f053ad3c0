package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-10 17:23:10
 * @version 1.0
 *
 */
@Data
public class MerchantStoreChangeLog {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 区域id
	 */
	private Long regionalId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 记录是哪个销售邀请的
	 */
	private String inviterChannelCode;

	/**
	 * 记录是哪个门店邀请的
	 */
	private String merchantChannelCode;

	/**
	 * 操作人名称
	 */
	private String opName;

	/**
	 * 操作类型，0-创建，1-审核，2-关店，3-拉黑
	 */
	private Integer opType;

	/**
	 * 操作备注
	 */
	private String opRemark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}