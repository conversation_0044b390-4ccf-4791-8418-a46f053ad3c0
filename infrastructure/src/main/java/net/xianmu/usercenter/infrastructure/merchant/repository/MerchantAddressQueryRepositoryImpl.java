package net.xianmu.usercenter.infrastructure.merchant.repository;

import cn.hutool.core.collection.CollUtil;
import net.xianmu.usercenter.common.enums.MerchantAddressEnums;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantAddressConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantAddressDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 13:50:22
* @version 1.0
*
*/
@Repository
public class MerchantAddressQueryRepositoryImpl implements MerchantAddressQueryRepository {

    @Autowired
    private MerchantAddressDao merchantAddressDao;

    @Override
    public MerchantAddressEntity selectDefaultAddress(Long tenantId, Long storeId) {
        final MerchantAddressQueryInput input = MerchantAddressQueryInput.builder()
                .defaultFlag(MerchantAddressEnums.DefaultFlag.DEFAULT.getCode())
                .status(MerchantAddressEnums.status.NORMAL.getCode())
                .tenantId(tenantId)
                .storeId(storeId)
                .build();
        final List<MerchantAddressEntity> merchantAddressEntities = selectByCondition(input);
        return CollUtil.isEmpty(merchantAddressEntities) ? null : merchantAddressEntities.get(0);
    }

    @Override
    public MerchantAddressEntity selectByXmContactId(Long xmContactId) {
        MerchantAddressQueryInput input = MerchantAddressQueryInput.builder()
                .xmContactId(xmContactId)
                .build();
        final List<MerchantAddressEntity> merchantAddressEntities = selectByCondition(input);
        return CollUtil.isEmpty(merchantAddressEntities) ? null : merchantAddressEntities.get(0);
    }


    @Override
    public MerchantAddressEntity selectById(Long id) {
        return MerchantAddressConverter.toMerchantAddressEntity(merchantAddressDao.getById(id));
    }

    @Override
    public List<String> selectConcatAddressByTenantIdAndStatus(Long tenantId, Integer status) {
        return merchantAddressDao.getBaseMapper().selectConcatAddressByTenantIdAndStatus(tenantId, status);
    }

    @Override
    public List<MerchantAddressEntity> selectByCondition(MerchantAddressQueryInput req) {
        return MerchantAddressConverter.toMerchantAddressEntityList(merchantAddressDao.listByCondition(req));
    }

    @Override
    public List<MerchantAddressEntity> selectXmErrorAddress() {
        return MerchantAddressConverter.toMerchantAddressEntityList(merchantAddressDao.getBaseMapper().selectXmErrorAddress());
    }


}