package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreExtRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreExtConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreExtDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-08-30 14:08:25
* @version 1.0
*
*/
@Repository
public class MerchantStoreExtRepositoryImpl implements MerchantStoreExtRepository {

    @Autowired
    private MerchantStoreExtDao merchantStoreExtDao;

    @Override
    public MerchantStoreExtEntity selectByStoreId(Long storeId) {
        return MerchantStoreExtConverter.toMerchantStoreExtEntity(merchantStoreExtDao.selectByStoreId(storeId));
    }

    @Override
    public MerchantStoreExtEntity createOrUpdate(MerchantStoreExtEntity entity) {
        MerchantStoreExt merchantStoreExt = MerchantStoreExtConverter.toMerchantStoreExt(entity);
        merchantStoreExtDao.saveOrUpdate(merchantStoreExt);
        return MerchantStoreExtConverter.toMerchantStoreExtEntity(merchantStoreExt);
    }

}