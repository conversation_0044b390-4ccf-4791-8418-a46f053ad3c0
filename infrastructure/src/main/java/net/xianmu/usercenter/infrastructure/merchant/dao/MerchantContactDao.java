package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.enums.MerchantContactEnum;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantContactMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAccount;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@Component
public class MerchantContactDao extends ServiceImpl<MerchantContactMapper, MerchantContact> {

    /**
     * @param
     * @return
     */
    public List<MerchantContact> listByCondition(MerchantContactQueryInput req) {
        LambdaQueryWrapper<MerchantContact> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantContact::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantContact::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getAddressId()), MerchantContact::getAddressId, req.getAddressId());
        query.in(CollUtil.isNotEmpty(req.getAddressIdList()), MerchantContact::getAddressId, req.getAddressIdList());
        query.eq(Objects.nonNull(req.getDefaultFlag()), MerchantContact::getDefaultFlag, req.getDefaultFlag());
        query.like(!StringUtils.isBlank(req.getPhone()), MerchantContact::getPhone, req.getPhone());
        query.like(!StringUtils.isBlank(req.getName()), MerchantContact::getName, req.getName());
        return list(query);
    }


    public MerchantContact selectDefaultContact(Long addressId){
        LambdaQueryWrapper<MerchantContact> query = new LambdaQueryWrapper<>();
        query.eq(MerchantContact::getAddressId, addressId);
        query.eq(MerchantContact::getDefaultFlag, MerchantContactEnum.DEFAULT.getType());
        return getOne(query);
    }

    public MerchantContact selectDefaultContact(Long tenantId, Long addressId){
        LambdaQueryWrapper<MerchantContact> query = new LambdaQueryWrapper<>();
        query.eq(MerchantContact::getAddressId, addressId);
        query.eq(MerchantContact::getTenantId, tenantId);
        query.eq(MerchantContact::getDefaultFlag, MerchantContactEnum.DEFAULT.getType());
        return getOne(query);
    }
}
