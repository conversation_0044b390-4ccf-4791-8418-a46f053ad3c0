package net.xianmu.usercenter.infrastructure.tenant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-05-05 11:24:18
 * @version 1.0
 *
 */
@Data
public class Tenant {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 租户名称
	 */
	private String tenantName;

	/**
	 * 租户类型：0供应商1品牌方2帆台
	 */
	private Integer type;

	/**
	 * 租户状态：0、禁用 1、启用
	 */
	private Integer status;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 归属DB
	 */
	private String belongDb;

	/**
	 * 大客户ID
	 */
	private Long adminId;

	/**
	 * 操作人id
	 */
	private Long opUid;

	/**
	 * 操作人名称
	 */
	private String opUname;

	/**
	 * 操作人
	 */
	private String operator;

	/**
	 * 分账开关0关闭1开启
	 */
	private Integer profitSharingSwitch;

	/**
	 * 0 微信 1 汇付
	 */
	private Integer onlinePayChannel;

	/**
	 * email
	 */
	private String email;

	/**
	 * 租户账户类型:0-手机号登录，1-邮箱登录
	 */
	private Integer accountLoginType;
	
}