package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantContactConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantContactDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 17:08:24
* @version 1.0
*
*/
@Repository
public class MerchantContactCommandRepositoryImpl implements MerchantContactCommandRepository {

    @Autowired
    private MerchantContactDao merchantContactDao;

    @Override
    public MerchantContactEntity createOrUpdate(MerchantContactEntity entity) {
        MerchantContact merchantContact = MerchantContactConverter.toMerchantContact(entity);
        merchantContactDao.saveOrUpdate(merchantContact);
        return MerchantContactConverter.toMerchantContactEntity(merchantContact);
    }

    @Override
    public Boolean createBatch(List<MerchantContactEntity> entities) {
        return merchantContactDao.saveBatch(MerchantContactConverter.toMerchantContactList(entities));
    }

    @Override
    public boolean updateSelective(MerchantContactEntity entity) {
        return merchantContactDao.updateById(MerchantContactConverter.toMerchantContact(entity));

    }

    @Override
    public boolean delete(Long id) {
        return merchantContactDao.removeById(id);
    }

    @Override
    public boolean deleteBatch(List<Long> idList) {
        return merchantContactDao.removeBatchByIds(idList);
    }
}