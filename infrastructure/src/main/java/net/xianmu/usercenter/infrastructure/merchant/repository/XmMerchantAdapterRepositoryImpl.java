package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAdapterRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.XmMerchantAdapterConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.XmMerchantAdapterDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-10-23 13:47:58
* @version 1.0
*
*/
@Repository
public class XmMerchantAdapterRepositoryImpl implements XmMerchantAdapterRepository {

    @Autowired
    private XmMerchantAdapterDao xmMerchantAdapterDao;



    @Override
    public XmMerchantAdapterEntity selectByStoreId(Long storeId) {
        return XmMerchantAdapterConverter.INSTANCE.toXmMerchantAdapterEntity(xmMerchantAdapterDao.selectByStoreId(storeId));
    }

    @Override
    public void createOrUpdate(XmMerchantAdapterEntity entity) {
        xmMerchantAdapterDao.saveOrUpdate(XmMerchantAdapterConverter.INSTANCE.toXmMerchantAdapter(entity));
    }

    @Override
    public void update(XmMerchantAdapterEntity entity) {
        xmMerchantAdapterDao.update(XmMerchantAdapterConverter.INSTANCE.toXmMerchantAdapter(entity));
    }

}