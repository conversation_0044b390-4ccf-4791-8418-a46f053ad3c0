package net.xianmu.usercenter.infrastructure.invoice.converter;

import net.xianmu.usercenter.domain.invoice.entity.InvoiceMerchantRelationEntity;
import net.xianmu.usercenter.infrastructure.invoice.model.InvoiceMerchantRelation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-13 13:58:41
 * @version 1.0
 *
 */
public class InvoiceMerchantRelationConverter {


    private InvoiceMerchantRelationConverter() {
        // 无需实现
    }

    public static List<InvoiceMerchantRelationEntity> toInvoiceMerchantRelationEntityList(List<InvoiceMerchantRelation> invoiceMerchantRelationList) {
        if (invoiceMerchantRelationList == null) {
            return Collections.emptyList();
        }
        List<InvoiceMerchantRelationEntity> invoiceMerchantRelationEntityList = new ArrayList<>();
        for (InvoiceMerchantRelation invoiceMerchantRelation : invoiceMerchantRelationList) {
            invoiceMerchantRelationEntityList.add(toInvoiceMerchantRelationEntity(invoiceMerchantRelation));
        }
        return invoiceMerchantRelationEntityList;
    }

    public static InvoiceMerchantRelationEntity toInvoiceMerchantRelationEntity(InvoiceMerchantRelation invoiceMerchantRelation) {
        if (invoiceMerchantRelation == null) {
            return null;
        }
        InvoiceMerchantRelationEntity invoiceMerchantRelationEntity = new InvoiceMerchantRelationEntity();
        invoiceMerchantRelationEntity.setId(invoiceMerchantRelation.getId());
        invoiceMerchantRelationEntity.setInvoiceId(invoiceMerchantRelation.getInvoiceId());
        invoiceMerchantRelationEntity.setStoreId(invoiceMerchantRelation.getStoreId());
        invoiceMerchantRelationEntity.setUpdateTime(invoiceMerchantRelation.getUpdateTime());
        invoiceMerchantRelationEntity.setCreateTime(invoiceMerchantRelation.getCreateTime());
        invoiceMerchantRelationEntity.setUpdater(invoiceMerchantRelation.getUpdater());
        invoiceMerchantRelationEntity.setCreator(invoiceMerchantRelation.getCreator());
        invoiceMerchantRelationEntity.setStatus(invoiceMerchantRelation.getStatus());
        return invoiceMerchantRelationEntity;
    }

    public static List<InvoiceMerchantRelation> toInvoiceMerchantRelationList(List<InvoiceMerchantRelationEntity> invoiceMerchantRelationEntityList) {
        if (invoiceMerchantRelationEntityList == null) {
            return Collections.emptyList();
        }
        List<InvoiceMerchantRelation> invoiceMerchantRelationList = new ArrayList<>();
        for (InvoiceMerchantRelationEntity invoiceMerchantRelationEntity : invoiceMerchantRelationEntityList) {
            invoiceMerchantRelationList.add(toInvoiceMerchantRelation(invoiceMerchantRelationEntity));
        }
        return invoiceMerchantRelationList;
    }

    public static InvoiceMerchantRelation toInvoiceMerchantRelation(InvoiceMerchantRelationEntity invoiceMerchantRelationEntity) {
        if (invoiceMerchantRelationEntity == null) {
            return null;
        }
        InvoiceMerchantRelation invoiceMerchantRelation = new InvoiceMerchantRelation();
        invoiceMerchantRelation.setId(invoiceMerchantRelationEntity.getId());
        invoiceMerchantRelation.setInvoiceId(invoiceMerchantRelationEntity.getInvoiceId());
        invoiceMerchantRelation.setStoreId(invoiceMerchantRelationEntity.getStoreId());
        invoiceMerchantRelation.setUpdateTime(invoiceMerchantRelationEntity.getUpdateTime());
        invoiceMerchantRelation.setCreateTime(invoiceMerchantRelationEntity.getCreateTime());
        invoiceMerchantRelation.setUpdater(invoiceMerchantRelationEntity.getUpdater());
        invoiceMerchantRelation.setCreator(invoiceMerchantRelationEntity.getCreator());
        invoiceMerchantRelation.setStatus(invoiceMerchantRelationEntity.getStatus());
        return invoiceMerchantRelation;
    }
}
