package net.xianmu.usercenter.infrastructure.merchant.repository;


import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreAccountConverter;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreAndAddressModelConverter;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-08 14:08:17
* @version 1.0
*
*/
@Repository
public class MerchantStoreQueryRepositoryImpl implements MerchantStoreQueryRepository {

    @Autowired
    private MerchantStoreDao merchantStoreDao;

    @Override
    public MerchantStoreEntity selectById(Long id) {
        return MerchantStoreConverter.toMerchantStoreEntity(merchantStoreDao.getById(id));
    }

    @Override
    public MerchantStoreEntity selectByMId(Long mId, Long tenantId) {
        return MerchantStoreConverter.toMerchantStoreEntity(merchantStoreDao.selectByMId(mId, tenantId));
    }

    @Override
    public List<MerchantStoreEntity> selectByIds(List<Long> idList) {
        return MerchantStoreConverter.toMerchantStoreEntityList(merchantStoreDao.getBaseMapper().selectBatchIds(idList));
    }

    @Override
    public MerchantStoreEntity selectByStoreName(Long tenantId, String storeName) {
        return MerchantStoreConverter.toMerchantStoreEntity(merchantStoreDao.selectByStoreName(tenantId, storeName));
    }

    @Override
    public Integer countStoreNum(Long tenantId) {
        return merchantStoreDao.countStoreNum(tenantId);
    }

    @Override
    public Integer countStoreNum(MerchantStoreQueryInput req) {
        return merchantStoreDao.countStoreNum(req);
    }

    @Override
    public List<MerchantStoreEntity> selectByCondition(MerchantStoreQueryInput req) {
        return MerchantStoreConverter.toMerchantStoreEntityList(merchantStoreDao.listByCondition(req));    }

    @Override
    public List<MerchantStoreEntity> selectByAccountCondition(MerchantStoreQueryInput req) {
        return MerchantStoreConverter.toMerchantStoreEntityList(merchantStoreDao.getBaseMapper().selectByAccountCondition(req));
    }

    @Override
    public PageInfo<MerchantStoreEntity> selectMerchantStoreAndAddressPage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput) {
        PageHelper.startPage(pageQueryInput.getPageIndex(), pageQueryInput.getPageSize());
        final List<MerchantStoreModel> list = merchantStoreDao.getBaseMapper().selectMerchantStoreAndAddressPage(input);
        return PageInfoConverter.toPageResp(new PageInfo<>(list), MerchantStoreAndAddressModelConverter::toMerchantStoreEntity);
    }


    @Override
    public PageInfo<MerchantStoreEntity> selectMerchantStorePage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput) {
        PageHelper.startPage(pageQueryInput.getPageIndex(), pageQueryInput.getPageSize());
        final List<MerchantStoreModel> list = merchantStoreDao.getBaseMapper().selectMerchantStorePage(input);
        return PageInfoConverter.toPageResp(new PageInfo<>(list), MerchantStoreAndAddressModelConverter::toMerchantStoreEntity);
    }


    @Override
    public PageInfo<MerchantStoreEntity> selectAccountPage(MerchantStoreAccountQueryInput req, PageQueryInput page) {
        PageHelper.startPage(page.getPageIndex(), page.getPageSize());
        List<MerchantStoreModel> list = merchantStoreDao.getBaseMapper().selectAccountPage(req);
        return PageInfoConverter.toPageResp(new PageInfo<>(list), MerchantStoreAndAddressModelConverter::toMerchantStoreEntity);
    }


    @Override
    public List<MerchantStoreEntity> selectNoPullBlackRecordMerchant() {
        return MerchantStoreConverter.toMerchantStoreEntityList(merchantStoreDao.getBaseMapper().selectNoPullBlackRecordMerchant());
    }

    @Override
    public List<MerchantStoreEntity> selectMerchantStoreAndExtends(MerchantStoreQueryInput input) {
        return merchantStoreDao.getBaseMapper().selectMerchantStoreAndExtends(input);
    }


    @Override
    public List<Long> selectMIdListByCondition(MerchantStoreQueryInput input) {
        return merchantStoreDao.getBaseMapper().selectMIdListByCondition(input);
    }


    @Override
    public List<MerchantStoreEntity> selectMerchantStorePageForXM(MerchantStorePageQueryInput input) {
        return merchantStoreDao.getBaseMapper().selectMerchantStorePageForXM(input);
    }

    @Override
    public List<Long> selectStoreIdForXmPage(MerchantStorePageQueryInput input) {
        return merchantStoreDao.getBaseMapper().selectStoreIdForXmPage(input);
    }
}