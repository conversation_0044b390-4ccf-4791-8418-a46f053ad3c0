package net.xianmu.usercenter.infrastructure.regional.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.RegionalOrganizationAccountQueryInput;
import net.xianmu.usercenter.infrastructure.regional.mapper.RegionalOrganizationAccountMapper;
import net.xianmu.usercenter.infrastructure.regional.model.RegionalOrganizationAccount;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-06 16:46:41
 */
@Component
public class RegionalOrganizationAccountDao extends ServiceImpl<RegionalOrganizationAccountMapper, RegionalOrganizationAccount> {

    /**
     * @param
     * @return
     */
    public List<RegionalOrganizationAccount> listByCondition(RegionalOrganizationAccountQueryInput req) {
        LambdaQueryWrapper<RegionalOrganizationAccount> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), RegionalOrganizationAccount::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), RegionalOrganizationAccount::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getRegionalId()), RegionalOrganizationAccount::getRegionalId, req.getRegionalId());
        query.eq(StrUtil.isNotBlank(req.getPhone()), RegionalOrganizationAccount::getPhone, req.getPhone());
        query.eq(StrUtil.isNotBlank(req.getName()), RegionalOrganizationAccount::getName, req.getName());
        query.eq(Objects.nonNull(req.getStatus()), RegionalOrganizationAccount::getStatus, req.getStatus());
        query.eq(StrUtil.isNotBlank(req.getCreator()), RegionalOrganizationAccount::getCreator, req.getCreator());
        query.eq(StrUtil.isNotBlank(req.getUpdater()), RegionalOrganizationAccount::getUpdater, req.getUpdater());
        return list(query);
    }


}
