package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantAddressConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantAddressDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */
@Repository
public class MerchantAddressCommandRepositoryImpl implements MerchantAddressCommandRepository {

    @Autowired
    private MerchantAddressDao merchantAddressDao;

    @Override
    public MerchantAddressEntity createOrUpdate(MerchantAddressEntity entity) {
        MerchantAddress merchantAddress = MerchantAddressConverter.toMerchantAddress(entity);
        merchantAddressDao.saveOrUpdate(merchantAddress);
        return MerchantAddressConverter.toMerchantAddressEntity(merchantAddress);
    }

    @Override
    public void updateSelectiveBatch(List<MerchantAddressEntity> entities) {
        merchantAddressDao.updateBatchById(MerchantAddressConverter.toMerchantAddressList(entities));
    }

    @Override
    public Boolean delete(Long id) {
        return merchantAddressDao.removeById(id);
    }

}