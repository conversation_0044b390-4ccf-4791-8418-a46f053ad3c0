package net.xianmu.usercenter.infrastructure.tenant.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/5 16:21
 */

@Data
public class TenantAndBusinessModel {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 商城名称
     */
    private String tenantName;
    /**
     * 租户类型：0-供应商,1-品牌方,2-帆台
     */
    private Integer tenantType;
    /**
     * 管理员手机号
     */
    private String phone;
    /**
     * 工商主体
     */
    private String companyName;
    /**
     * 租户联系人名称
     */
    private String contactName;
    /**
     * 信用代码/税号
     */
    private String creditCode;
    /**
     * 租户公司主键Id
     */
    private Long tenantCompanyId;
    /**
     * 大客户Id
     */
    private Long adminId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private String opUname;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;
}
