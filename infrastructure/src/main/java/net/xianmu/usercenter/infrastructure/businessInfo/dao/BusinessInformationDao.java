package net.xianmu.usercenter.infrastructure.businessInfo.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.infrastructure.businessInfo.mapper.BusinessInformationMapper;
import net.xianmu.usercenter.infrastructure.businessInfo.model.BusinessInformation;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 17:01:58
 * @version 1.0
 *
 */
@Component
public class BusinessInformationDao extends ServiceImpl<BusinessInformationMapper, BusinessInformation> {


    public BusinessInformation getBusinessInfoByBizIdAndType(Long bizId, Integer type) {
        LambdaQueryWrapper<BusinessInformation> query = new LambdaQueryWrapper<>();
        query.eq(BusinessInformation::getBizId, bizId);
        query.eq(BusinessInformation::getType, type);
        return getOne(query);
    }

    public List<BusinessInformation> listByCondition(BusinessInformationQueryInput req) {
        LambdaQueryWrapper<BusinessInformation> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getTenantId()), BusinessInformation::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getType()), BusinessInformation::getType, req.getType());
        query.eq(Objects.nonNull(req.getBizId()), BusinessInformation::getBizId, req.getBizId());
        query.eq(Objects.nonNull(req.getId()), BusinessInformation::getId, req.getId());
        query.in(CollUtil.isNotEmpty(req.getBizIdList()), BusinessInformation::getBizId, req.getBizIdList());
        return list(query);
    }


    public void updateByEntity(BusinessInformationEntity entity) {
        LambdaUpdateWrapper<BusinessInformation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BusinessInformation::getId, entity.getId());
        wrapper.set(BusinessInformation::getCompanyName, entity.getCompanyName());
        wrapper.set(BusinessInformation::getCreditCode, entity.getCreditCode());
        wrapper.set(BusinessInformation::getProvince, entity.getProvince());
        wrapper.set(BusinessInformation::getCity, entity.getCity());
        wrapper.set(BusinessInformation::getArea, entity.getArea());
        wrapper.set(BusinessInformation::getAddress, entity.getAddress());
        wrapper.set(BusinessInformation::getPhone, entity.getPhone());
        wrapper.set(BusinessInformation::getBusinessLicense, entity.getBusinessLicense());
        wrapper.set(BusinessInformation::getContactName, entity.getContactName());
        wrapper.set(Objects.nonNull(entity.getCreateTime()), BusinessInformation::getCreateTime, entity.getCreateTime());
        wrapper.set(Objects.nonNull(entity.getUpdateTime()), BusinessInformation::getUpdateTime, entity.getUpdateTime());
        update(wrapper);
    }

}
