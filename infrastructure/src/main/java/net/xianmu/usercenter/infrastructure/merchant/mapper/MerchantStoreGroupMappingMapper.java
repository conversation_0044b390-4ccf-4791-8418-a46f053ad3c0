package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroupMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @date 2023-06-01 15:42:52
 * @version 1.0
 *
 */
@Mapper
public interface MerchantStoreGroupMappingMapper extends BaseMapper<MerchantStoreGroupMapping> {

    Integer countStoreNumByGroupId(@Param("tenantId") Long tenantId, @Param("groupId") Long groupId);

}
