package net.xianmu.usercenter.infrastructure.tenant.repository;

import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.domain.tenant.repository.TenantCommandRepository;
import net.xianmu.usercenter.infrastructure.tenant.converter.TenantConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.TenantDao;
import net.xianmu.usercenter.infrastructure.tenant.model.Tenant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-05 11:24:18
* @version 1.0
*
*/
@Repository
public class TenantCommandRepositoryImpl implements TenantCommandRepository {

    @Autowired
    private TenantDao tenantDao;

    @Override
    public TenantEntity createOrUpdate(TenantEntity entity) {
        Tenant tenant = TenantConverter.toTenant(entity);
        tenantDao.saveOrUpdate(tenant);
        return TenantConverter.toTenantEntity(tenant);
    }


    @Override
    public void batchUpdateOp(List<Long> ids, String opUname, Long opUid) {
        tenantDao.getBaseMapper().batcheUpdateOp(ids, opUname, opUid);
    }
}