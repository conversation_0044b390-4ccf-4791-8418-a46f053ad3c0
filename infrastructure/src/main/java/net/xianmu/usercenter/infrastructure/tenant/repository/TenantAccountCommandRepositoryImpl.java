package net.xianmu.usercenter.infrastructure.tenant.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;
import net.xianmu.usercenter.domain.tenant.repository.TenantAccountCommandRepository;
import net.xianmu.usercenter.infrastructure.tenant.converter.TenantAccountConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.TenantAccountDao;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/10 11:22
 */
@Repository
public class TenantAccountCommandRepositoryImpl implements TenantAccountCommandRepository {


    @Autowired
    private TenantAccountDao tenantAccountDao;

    @Override
    public void insertOrUpdate(TenantAccountEntity entity) {
        tenantAccountDao.saveOrUpdate(TenantAccountConverter.toTenantAccount(entity));
    }

    @Override
    public void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccountEntity entity) {
        tenantAccountDao.updateByTenantIdsAndPhone(tenantIds, TenantAccountConverter.toTenantAccount(entity));
    }

    @Override
    public void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountEntity entity) {
        tenantAccountDao.updateByTenantIdsAndAuthId(tenantIds, TenantAccountConverter.toTenantAccount(entity));
    }

    @Override
    public void remove(Long id) {
        tenantAccountDao.remove(id);
    }

}
