package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreConfigEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreConfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-11 10:48:04
 * @version 1.0
 *
 */
public class MerchantStoreConfigConverter {


    private MerchantStoreConfigConverter() {
        // 无需实现
    }

    public static List<MerchantStoreConfigEntity> toMerchantStoreConfigEntityList(List<MerchantStoreConfig> merchantStoreConfigList) {
        if (merchantStoreConfigList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreConfigEntity> merchantStoreConfigEntityList = new ArrayList<>();
        for (MerchantStoreConfig merchantStoreConfig : merchantStoreConfigList) {
            merchantStoreConfigEntityList.add(toMerchantStoreConfigEntity(merchantStoreConfig));
        }
        return merchantStoreConfigEntityList;
    }

    public static MerchantStoreConfigEntity toMerchantStoreConfigEntity(MerchantStoreConfig merchantStoreConfig) {
        if (merchantStoreConfig == null) {
            return null;
        }
        MerchantStoreConfigEntity merchantStoreConfigEntity = new MerchantStoreConfigEntity();
        merchantStoreConfigEntity.setId(merchantStoreConfig.getId());
        merchantStoreConfigEntity.setTenantId(merchantStoreConfig.getTenantId());
        merchantStoreConfigEntity.setStoreId(merchantStoreConfig.getStoreId());
        merchantStoreConfigEntity.setCreateTime(merchantStoreConfig.getCreateTime());
        merchantStoreConfigEntity.setUpdateTime(merchantStoreConfig.getUpdateTime());
        merchantStoreConfigEntity.setCreator(merchantStoreConfig.getCreator());
        merchantStoreConfigEntity.setUpdater(merchantStoreConfig.getUpdater());
        merchantStoreConfigEntity.setPopView(merchantStoreConfig.getPopView());
        merchantStoreConfigEntity.setChangePop(merchantStoreConfig.getChangePop());
        merchantStoreConfigEntity.setFirstLoginPop(merchantStoreConfig.getFirstLoginPop());
        merchantStoreConfigEntity.setDisplayButton(merchantStoreConfig.getDisplayButton());
        return merchantStoreConfigEntity;
    }

    public static List<MerchantStoreConfig> toMerchantStoreConfigList(List<MerchantStoreConfigEntity> merchantStoreConfigEntityList) {
        if (merchantStoreConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreConfig> merchantStoreConfigList = new ArrayList<>();
        for (MerchantStoreConfigEntity merchantStoreConfigEntity : merchantStoreConfigEntityList) {
            merchantStoreConfigList.add(toMerchantStoreConfig(merchantStoreConfigEntity));
        }
        return merchantStoreConfigList;
    }

    public static MerchantStoreConfig toMerchantStoreConfig(MerchantStoreConfigEntity merchantStoreConfigEntity) {
        if (merchantStoreConfigEntity == null) {
            return null;
        }
        MerchantStoreConfig merchantStoreConfig = new MerchantStoreConfig();
        merchantStoreConfig.setId(merchantStoreConfigEntity.getId());
        merchantStoreConfig.setTenantId(merchantStoreConfigEntity.getTenantId());
        merchantStoreConfig.setStoreId(merchantStoreConfigEntity.getStoreId());
        merchantStoreConfig.setCreateTime(merchantStoreConfigEntity.getCreateTime());
        merchantStoreConfig.setUpdateTime(merchantStoreConfigEntity.getUpdateTime());
        merchantStoreConfig.setCreator(merchantStoreConfigEntity.getCreator());
        merchantStoreConfig.setUpdater(merchantStoreConfigEntity.getUpdater());
        merchantStoreConfig.setPopView(merchantStoreConfigEntity.getPopView());
        merchantStoreConfig.setChangePop(merchantStoreConfigEntity.getChangePop());
        merchantStoreConfig.setFirstLoginPop(merchantStoreConfigEntity.getFirstLoginPop());
        merchantStoreConfig.setDisplayButton(merchantStoreConfigEntity.getDisplayButton());
        return merchantStoreConfig;
    }
}
