package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStorePropertiesExt;

public class MerchantStorePropertiesExtConverter {
    public MerchantStorePropertiesExtConverter(){

    }
    public static MerchantStorePropertiesExtEntity toMerchantStorePropertiesExtEntity(MerchantStorePropertiesExt ext){
        if (ext == null){
            return null;
        }
        MerchantStorePropertiesExtEntity entity = new MerchantStorePropertiesExtEntity();
        entity.setStoreId(ext.getStoreId());
        entity.setMId(ext.getMId());
        entity.setProKey(ext.getProKey());
        entity.setProValue(ext.getProValue());
        return entity;
    }

    public static MerchantStorePropertiesExt toMerchantStorePropertiesExt(MerchantStorePropertiesExtEntity ext){
        if (ext == null){
            return null;
        }
        MerchantStorePropertiesExt entity = new MerchantStorePropertiesExt();
        entity.setStoreId(ext.getStoreId());
        entity.setMId(ext.getMId());
        entity.setProKey(ext.getProKey());
        entity.setProValue(ext.getProValue());
        return entity;
    }
}
