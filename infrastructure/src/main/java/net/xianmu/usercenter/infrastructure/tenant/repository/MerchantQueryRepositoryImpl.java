package net.xianmu.usercenter.infrastructure.tenant.repository;

import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantQueryRepository;
import net.xianmu.usercenter.infrastructure.tenant.converter.MerchantConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.MerchantDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-25 10:47:01
* @version 1.0
*
*/
@Repository
public class MerchantQueryRepositoryImpl implements MerchantQueryRepository {

    @Autowired
    private MerchantDao merchantDao;

    @Override
    public MerchantEntity selectByTenantId(Long tenantId) {
        return MerchantConverter.toMerchantEntity(merchantDao.selectByTenantId(tenantId));
    }

    @Override
    public MerchantEntity selectById(Long id) {
        return MerchantConverter.toMerchantEntity(merchantDao.getById(id));
    }

    @Override
    public List<MerchantEntity> selectByCondition(MerchantQueryInput input) {
        return MerchantConverter.toMerchantEntityList(merchantDao.listByCondition(input));
    }
}