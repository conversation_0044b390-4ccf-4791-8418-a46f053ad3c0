package net.xianmu.usercenter.infrastructure.invoice.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.infrastructure.invoice.mapper.InvoiceMerchantRelationMapper;
import net.xianmu.usercenter.infrastructure.invoice.model.InvoiceMerchantRelation;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2023-07-13 13:58:41
* @version 1.0
*
*/
//@Component
public class InvoiceMerchantRelationDao extends ServiceImpl<InvoiceMerchantRelationMapper, InvoiceMerchantRelation> {

}
