package net.xianmu.usercenter.infrastructure.merchant.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreGroupMappingMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroupMapping;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-01 15:42:52
 */
@Component
public class MerchantStoreGroupMappingDao extends ServiceImpl<MerchantStoreGroupMappingMapper, MerchantStoreGroupMapping> {

    /**
     * @param
     * @return
     */
/*    public List<MerchantStoreGroupMapping> listByCondition(MerchantStoreGroupMappingQueryInput req) {
        LambdaQueryWrapper<MerchantStoreGroupMapping> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantStoreGroupMapping::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreGroupMapping::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreGroupMapping::getStoreId, req.getStoreId());
        query.eq(Objects.nonNull(req.getGroupId()), MerchantStoreGroupMapping::getGroupId, req.getGroupId());
        return list(query);
    }*/


    public MerchantStoreGroupMapping selectByStoreId(Long storeId, Long tenantId) {
        LambdaQueryWrapper<MerchantStoreGroupMapping> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MerchantStoreGroupMapping::getStoreId, storeId);
        queryWrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
        return getOne(queryWrapper);
    }


    public List<MerchantStoreGroupMapping> selectByByGroupId(Long groupId, Long tenantId) {
        LambdaQueryWrapper<MerchantStoreGroupMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MerchantStoreGroupMapping::getGroupId, groupId);
        wrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
        return list(wrapper);
    }


    public void updateGroupIdByStoreIds(Long groupId, Long tenantId,  List<Long> storeIdList) {
        LambdaUpdateWrapper<MerchantStoreGroupMapping> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(MerchantStoreGroupMapping::getStoreId, storeIdList);
        wrapper.eq(MerchantStoreGroupMapping::getTenantId, tenantId);
        wrapper.set(MerchantStoreGroupMapping::getGroupId, groupId);
        update(wrapper);
    }
}
