package net.xianmu.usercenter.infrastructure.businessInfo.repository;


import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.domain.businessInfo.repository.BusinessInformationCommandRepository;
import net.xianmu.usercenter.infrastructure.businessInfo.converter.BusinessInformationConverter;
import net.xianmu.usercenter.infrastructure.businessInfo.dao.BusinessInformationDao;
import net.xianmu.usercenter.infrastructure.businessInfo.model.BusinessInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-08 17:01:58
 */
@Repository
public class BusinessInformationCommandRepositoryImpl implements BusinessInformationCommandRepository {

    @Autowired
    private BusinessInformationDao dao;

    @Override
    public void insert(BusinessInformationCommandInput input) {
        BusinessInformation businessInformation = new BusinessInformation();
        businessInformation.warp(input);
        dao.save(businessInformation);
    }

    @Override
    public void insert(BusinessInformationEntity input) {
        dao.save(BusinessInformationConverter.toBusinessInformation(input));
    }

    @Override
    public void updateByEntity(BusinessInformationEntity entity) {
        dao.updateByEntity(entity);
    }


    @Override
    public void updateSelective(BusinessInformationEntity input) {
        dao.updateById(BusinessInformationConverter.toBusinessInformation(input));
    }


}