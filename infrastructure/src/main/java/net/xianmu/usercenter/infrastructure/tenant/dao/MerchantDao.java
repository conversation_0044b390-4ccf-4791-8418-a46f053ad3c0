package net.xianmu.usercenter.infrastructure.tenant.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.infrastructure.tenant.mapper.MerchantMapper;
import net.xianmu.usercenter.infrastructure.tenant.model.Merchant;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:44:50
 * @version 1.0
 *
 */
@Component
public class MerchantDao extends ServiceImpl<MerchantMapper, Merchant> {

    public Merchant selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<Merchant> query = new LambdaQueryWrapper<>();
        query.eq(Merchant::getTenantId, tenantId);
        return getOne(query);
    }

    /**
     * @param
     * @return
     */
    public List<Merchant> listByCondition(MerchantQueryInput req) {
        LambdaQueryWrapper<Merchant> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getTenantId()), Merchant::getTenantId, req.getTenantId());
        query.in(CollUtil.isNotEmpty(req.getTenantIdList()), Merchant::getTenantId, req.getTenantIdList());
        return list(query);
    }


    public void update(Merchant merchant) {
        LambdaUpdateWrapper<Merchant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Merchant::getId, merchant.getId());
        updateWrapper.set(!StringUtils.isEmpty(merchant.getMerchantName()), Merchant::getMerchantName, merchant.getMerchantName());
        updateWrapper.set(!StringUtils.isEmpty(merchant.getBackgroundImage()), Merchant::getBackgroundImage, merchant.getBackgroundImage());
        updateWrapper.set(!StringUtils.isEmpty(merchant.getLogoImage()), Merchant::getLogoImage, merchant.getLogoImage());
        update(updateWrapper);
    }
}
