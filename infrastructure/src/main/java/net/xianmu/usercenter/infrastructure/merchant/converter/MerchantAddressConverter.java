package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantAddress;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public class MerchantAddressConverter {


    private MerchantAddressConverter() {
        // 无需实现
    }

    public static List<MerchantAddressEntity> toMerchantAddressEntityList(List<MerchantAddress> merchantAddressList) {
        if (merchantAddressList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressEntity> merchantAddressEntityList = new ArrayList<>();
        for (MerchantAddress merchantAddress : merchantAddressList) {
            merchantAddressEntityList.add(toMerchantAddressEntity(merchantAddress));
        }
        return merchantAddressEntityList;
    }

    public static MerchantAddressEntity toMerchantAddressEntity(MerchantAddress merchantAddress) {
        if (merchantAddress == null) {
            return null;
        }
        MerchantAddressEntity merchantAddressEntity = new MerchantAddressEntity();
        merchantAddressEntity.setId(merchantAddress.getId());
        merchantAddressEntity.setTenantId(merchantAddress.getTenantId());
        merchantAddressEntity.setStoreId(merchantAddress.getStoreId());
        merchantAddressEntity.setProvince(merchantAddress.getProvince());
        merchantAddressEntity.setCity(merchantAddress.getCity());
        merchantAddressEntity.setArea(merchantAddress.getArea());
        merchantAddressEntity.setAddress(merchantAddress.getAddress());
        merchantAddressEntity.setHouseNumber(merchantAddress.getHouseNumber());
        merchantAddressEntity.setPoiNote(merchantAddress.getPoiNote());
        merchantAddressEntity.setCreateTime(merchantAddress.getCreateTime());
        merchantAddressEntity.setUpdateTime(merchantAddress.getUpdateTime());
        merchantAddressEntity.setDefaultFlag(merchantAddress.getDefaultFlag());
        merchantAddressEntity.setStatus(merchantAddress.getStatus());
        merchantAddressEntity.setXmContactId(merchantAddress.getXmContactId());
        merchantAddressEntity.setRemark(merchantAddress.getRemark());
        merchantAddressEntity.setAddressRemark(merchantAddress.getAddressRemark());
        merchantAddressEntity.setMId(merchantAddress.getMId());
        return merchantAddressEntity;
    }

    public static List<MerchantAddress> toMerchantAddressList(List<MerchantAddressEntity> merchantAddressEntityList) {
        if (merchantAddressEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddress> merchantAddressList = new ArrayList<>();
        for (MerchantAddressEntity merchantAddressEntity : merchantAddressEntityList) {
            merchantAddressList.add(toMerchantAddress(merchantAddressEntity));
        }
        return merchantAddressList;
    }

    public static MerchantAddress toMerchantAddress(MerchantAddressEntity merchantAddressEntity) {
        if (merchantAddressEntity == null) {
            return null;
        }
        MerchantAddress merchantAddress = new MerchantAddress();
        merchantAddress.setId(merchantAddressEntity.getId());
        merchantAddress.setTenantId(merchantAddressEntity.getTenantId());
        merchantAddress.setStoreId(merchantAddressEntity.getStoreId());
        merchantAddress.setProvince(merchantAddressEntity.getProvince());
        merchantAddress.setCity(merchantAddressEntity.getCity());
        merchantAddress.setArea(merchantAddressEntity.getArea());
        merchantAddress.setAddress(merchantAddressEntity.getAddress());
        merchantAddress.setHouseNumber(merchantAddressEntity.getHouseNumber());
        merchantAddress.setPoiNote(merchantAddressEntity.getPoiNote());
        merchantAddress.setCreateTime(merchantAddressEntity.getCreateTime());
        merchantAddress.setUpdateTime(merchantAddressEntity.getUpdateTime());
        merchantAddress.setDefaultFlag(merchantAddressEntity.getDefaultFlag());
        merchantAddress.setStatus(merchantAddressEntity.getStatus());
        merchantAddress.setXmContactId(merchantAddressEntity.getXmContactId());
        merchantAddress.setRemark(merchantAddressEntity.getRemark());
        merchantAddress.setAddressRemark(merchantAddressEntity.getAddressRemark());
        merchantAddress.setMId(merchantAddressEntity.getMId());
        return merchantAddress;
    }
}
