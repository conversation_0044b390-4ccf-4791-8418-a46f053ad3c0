package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.constants.MerchantConstant;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 14:19
 */
@Component
@Slf4j
public class MerchantStoreDao extends ServiceImpl<MerchantStoreMapper, MerchantStore> {

    public List<MerchantStore> listByCondition(MerchantStoreQueryInput req) {
        LambdaQueryWrapper<MerchantStore> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStore::getId, req.getStoreId());
        query.eq(Objects.nonNull(req.getMId()), MerchantStore::getMId, req.getMId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStore::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getType()), MerchantStore::getType, req.getType());
        query.eq(Objects.nonNull(req.getStatus()), MerchantStore::getStatus, req.getStatus());
        query.eq(Objects.nonNull(req.getAreaNo()), MerchantStore::getAreaNo, req.getAreaNo());
        query.eq(Objects.nonNull(req.getChannelCode()), MerchantStore::getChannelCode, req.getChannelCode());
        query.eq(!StringUtils.isEmpty(req.getStoreNo()), MerchantStore::getStoreNo, req.getStoreNo());
        query.in(CollUtil.isNotEmpty(req.getStoreIdList()), MerchantStore::getId, req.getStoreIdList());
        query.in(CollUtil.isNotEmpty(req.getMIds()), MerchantStore::getMId, req.getMIds());
        query.in(CollUtil.isNotEmpty(req.getAreaNos()), MerchantStore::getAreaNo, req.getAreaNos());
        query.in(CollUtil.isNotEmpty(req.getStoreNoList()), MerchantStore::getStoreNo, req.getStoreNoList());
        query.like(!StringUtils.isEmpty(req.getStoreName()), MerchantStore::getStoreName, req.getStoreName());
        query.eq(!StringUtils.isEmpty(req.getExactStoreName()), MerchantStore::getStoreName, req.getExactStoreName());
        query.eq(!StringUtils.isEmpty(req.getEnableOfflinePayment()), MerchantStore::getEnableOfflinePayment, req.getEnableOfflinePayment());
        query.in(CollUtil.isNotEmpty(req.getExactStoreNameList()), MerchantStore::getStoreName, req.getExactStoreNameList());
        query.ge(Objects.nonNull(req.getStartRegisterTime()), MerchantStore::getRegisterTime, req.getStartRegisterTime());
        query.le(Objects.nonNull(req.getEndRegisterTime()), MerchantStore::getRegisterTime, req.getEndRegisterTime());
        query.ge(Objects.nonNull(req.getStartAuditTime()), MerchantStore::getAuditTime, req.getStartAuditTime());
        query.le(Objects.nonNull(req.getEndAuditTime()), MerchantStore::getAuditTime, req.getEndAuditTime());
        return list(query);
    }

    public MerchantStore selectByMId(Long mId, Long tenantId) {
        LambdaQueryWrapper<MerchantStore> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStore::getTenantId, tenantId);
        query.eq(MerchantStore::getMId, mId);
        return getOne(query);
    }

    public MerchantStore selectByStoreName(Long tenantId, String storeName) {
        try {
            LambdaQueryWrapper<MerchantStore> query = new LambdaQueryWrapper<>();
            query.eq(MerchantStore::getTenantId, tenantId);
            query.eq(MerchantStore::getStoreName, storeName);
            return getOne(query);
        } catch (RuntimeException ex) {
            // 无法获取持续更新的emoji，需要兜底异常
            if (ex.getMessage().contains(MerchantConstant.STORE_NAME_INVALID_MSG)) {
                log.warn("查询门店信息时发生异常:{}", ex.getMessage());
                throw new BizException("门店名称中含有特殊字符！");
            } else {
                throw ex;
            }
        }
    }


    public Integer countStoreNum(Long tenantId) {
        LambdaQueryWrapper<MerchantStore> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStore::getTenantId, tenantId);
        return Math.toIntExact(count(query));
    }

    public Integer countStoreNum(MerchantStoreQueryInput req) {
        LambdaQueryWrapper<MerchantStore> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStore::getId, req.getStoreId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStore::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getType()), MerchantStore::getType, req.getType());
        query.eq(Objects.nonNull(req.getStatus()), MerchantStore::getStatus, req.getStatus());
        query.eq(!StringUtils.isEmpty(req.getStoreNo()), MerchantStore::getStoreNo, req.getStoreNo());
        query.in(CollUtil.isNotEmpty(req.getStoreIdList()), MerchantStore::getId, req.getStoreIdList());
        query.like(!StringUtils.isEmpty(req.getStoreName()), MerchantStore::getStoreName, req.getStoreName());
        return Math.toIntExact(count(query));
    }


    /**
     * 编辑非空
     * @param
     * @return
     */
    public Boolean updateSelective(MerchantStoreCommandInput input) {
        LambdaUpdateWrapper<MerchantStore> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Objects.nonNull(input.getTenantId()), MerchantStore::getTenantId, input.getTenantId());
        wrapper.set(!StringUtils.isEmpty(input.getStoreName()), MerchantStore::getStoreName, input.getStoreName());
        wrapper.set(Objects.nonNull(input.getType()), MerchantStore::getType, input.getType());
        wrapper.set(Objects.nonNull(input.getRegisterTime()), MerchantStore::getRegisterTime, input.getRegisterTime());
        wrapper.set(Objects.nonNull(input.getStatus()), MerchantStore::getStatus, input.getStatus());
        wrapper.set(Objects.nonNull(input.getAuditRemark()), MerchantStore::getAuditRemark, input.getAuditRemark());
        wrapper.set(Objects.nonNull(input.getAuditTime()), MerchantStore::getAuditTime, input.getAuditTime());
        wrapper.set(Objects.nonNull(input.getRemark()), MerchantStore::getRemark, input.getRemark());
        wrapper.set(Objects.nonNull(input.getBillSwitch()), MerchantStore::getBillSwitch, input.getBillSwitch());
        wrapper.set(Objects.nonNull(input.getOnlinePayment()), MerchantStore::getOnlinePayment, input.getOnlinePayment());
        wrapper.set(Objects.nonNull(input.getStoreNo()), MerchantStore::getStoreNo, input.getStoreNo());
        wrapper.set(Objects.nonNull(input.getBalanceAuthority()), MerchantStore::getBalanceAuthority, input.getBalanceAuthority());
        wrapper.set(Objects.nonNull(input.getPlaceOrderPermissionTimeLimited ()), MerchantStore::getPlaceOrderPermissionTimeLimited, input.getPlaceOrderPermissionTimeLimited());
        wrapper.set(Objects.nonNull(input.getPlaceOrderPermissionExpiryTime ()), MerchantStore::getPlaceOrderPermissionExpiryTime, input.getPlaceOrderPermissionExpiryTime());
        wrapper.set(Objects.nonNull(input.getEnableOfflinePayment ()), MerchantStore::getEnableOfflinePayment, input.getEnableOfflinePayment());
        wrapper.set(Objects.nonNull(input.getNonCashAuthority ()), MerchantStore::getNonCashAuthority, input.getNonCashAuthority());
        wrapper.eq(MerchantStore::getId, input.getId());
        return update(wrapper);
    }
}
