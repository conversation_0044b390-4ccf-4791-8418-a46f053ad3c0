package net.xianmu.usercenter.infrastructure.merchant.repository;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.constants.MerchantConstant;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-05-08 14:08:17
* @version 1.0
*
*/
@Repository
@Slf4j
public class MerchantStoreCommandRepositoryImpl implements MerchantStoreCommandRepository {

    @Autowired
    private MerchantStoreDao merchantStoreDao;
    @Override
    public MerchantStoreEntity createOrUpdate(MerchantStoreEntity entity) {
        try {
            MerchantStore merchantStore = MerchantStoreConverter.toMerchantStore(entity);
            merchantStoreDao.saveOrUpdate(merchantStore);
            return MerchantStoreConverter.toMerchantStoreEntity(merchantStore);
        } catch (RuntimeException ex) {
            // 无法获取持续更新的emoji，需要兜底异常
            if (ex.getMessage().contains(MerchantConstant.STORE_NAME_INVALID_MSG)) {
                log.warn("保存门店信息时发生异常:{}", ex.getMessage());
                throw new BizException("门店名称中含有特殊字符！");
            } else {
                throw ex;
            }
        }
    }

    @Override
    public Boolean updateSelective(MerchantStoreEntity entity) {
        return merchantStoreDao.updateById(MerchantStoreConverter.toMerchantStore(entity));
    }

    @Override
    public Boolean delete(Long id) {
        return merchantStoreDao.removeById(id);
    }

    @Override
    public Boolean updateSelective(MerchantStoreCommandInput input) {
        return merchantStoreDao.updateSelective(input);
    }
}