package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreGroupMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-11 10:55:37
 */
@Component
public class MerchantStoreGroupDao extends ServiceImpl<MerchantStoreGroupMapper, MerchantStoreGroup> {

    public List<MerchantStoreGroup> selectByCondition(MerchantStoreGroupQueryInput input) {
        LambdaQueryWrapper<MerchantStoreGroup> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(input.getType()), MerchantStoreGroup::getType, input.getType());
        query.eq(Objects.nonNull(input.getId()), MerchantStoreGroup::getId, input.getId());
        query.eq(Objects.nonNull(input.getTenantId()), MerchantStoreGroup::getTenantId, input.getTenantId());
        query.in(CollUtil.isNotEmpty(input.getIdList()), MerchantStoreGroup::getId, input.getIdList());
        query.like(!StringUtils.isEmpty(input.getMerchantStoreGroupName()), MerchantStoreGroup::getName, input.getMerchantStoreGroupName());

        // 排序
        query.orderByDesc(MerchantStoreGroup::getType);
        Boolean sort = StringUtils.isEmpty(input.getCreateTimeSort()) ? false : input.getCreateTimeSort().equals("asc");
        query.orderBy(true, sort, MerchantStoreGroup::getId);
        if (!StringUtils.isEmpty(input.getUpdateTimeSort())) {
            query.orderBy(true, input.getUpdateTimeSort().equals("asc"), MerchantStoreGroup::getUpdateTime);
        }

        return list(query);
    }


    public MerchantStoreGroup selectDefaultGroup(Long tenantId) {
        LambdaQueryWrapper<MerchantStoreGroup> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreGroup::getType, TenantDefaultConstant.GROUP_TYPE);
        query.eq(MerchantStoreGroup::getTenantId, tenantId);
        return getOne(query);
    }

    public MerchantStoreGroup selectByName(Long tenantId, String name) {
        LambdaQueryWrapper<MerchantStoreGroup> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MerchantStoreGroup::getTenantId, tenantId);
        queryWrapper.eq(MerchantStoreGroup::getName, name);
        return getOne(queryWrapper);
    }
}
