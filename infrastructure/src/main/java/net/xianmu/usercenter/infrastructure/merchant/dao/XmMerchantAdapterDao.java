package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.XmMerchantAdapterMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.XmMerchantAdapter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2023-10-23 13:47:58
* @version 1.0
*
*/
@Component
public class XmMerchantAdapterDao extends ServiceImpl<XmMerchantAdapterMapper, XmMerchantAdapter> {

    public XmMerchantAdapter selectByStoreId(Long storeId) {
        LambdaQueryWrapper<XmMerchantAdapter> query = new LambdaQueryWrapper<>();
        query.eq(XmMerchantAdapter::getStoreId, storeId);
        return getOne(query);
    }


    /**
     * @param
     * @return
     */
    public void update(XmMerchantAdapter entity) {
        LambdaUpdateWrapper<XmMerchantAdapter> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(XmMerchantAdapter::getDirect, entity.getDirect());
        wrapper.set(StrUtil.isNotBlank(entity.getCreator()), XmMerchantAdapter::getCreator, entity.getCreator());
        wrapper.set(Objects.nonNull(entity.getCreateTime()), XmMerchantAdapter::getCreateTime, entity.getCreateTime());
        wrapper.set(Objects.nonNull(entity.getUpdateTime()), XmMerchantAdapter::getUpdateTime, entity.getUpdateTime());
        wrapper.set(StrUtil.isNotBlank(entity.getUpdater()), XmMerchantAdapter::getUpdater, entity.getUpdater());
        wrapper.eq(XmMerchantAdapter::getId, entity.getId());
        update(wrapper);
    }
}
