package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/6/2 18:29
 */
public class MerchantStoreAndAddressModelConverter {


    private MerchantStoreAndAddressModelConverter() {
        // 无需实现
    }

    public static List<MerchantStoreEntity> toMerchantStoreEntityList(List<MerchantStoreModel> merchantStoreModelList) {
        if (merchantStoreModelList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreEntity> merchantStoreEntityList = new ArrayList<>();
        for (MerchantStoreModel merchantStoreModel : merchantStoreModelList) {
            merchantStoreEntityList.add(toMerchantStoreEntity(merchantStoreModel));
        }
        return merchantStoreEntityList;
    }

    public static MerchantStoreEntity toMerchantStoreEntity(MerchantStoreModel merchantStoreModel) {
        if (merchantStoreModel == null) {
            return null;
        }
        MerchantStoreEntity merchantStoreEntity = new MerchantStoreEntity();
        merchantStoreEntity.setId(merchantStoreModel.getId());
        merchantStoreEntity.setTenantId(merchantStoreModel.getTenantId());
        merchantStoreEntity.setStoreName(merchantStoreModel.getStoreName());
        merchantStoreEntity.setType(merchantStoreModel.getType());
        merchantStoreEntity.setRegisterTime(merchantStoreModel.getRegisterTime());
        merchantStoreEntity.setStatus(merchantStoreModel.getStatus());
        merchantStoreEntity.setAuditRemark(merchantStoreModel.getAuditRemark());
        merchantStoreEntity.setAuditTime(merchantStoreModel.getAuditTime());
        merchantStoreEntity.setRemark(merchantStoreModel.getRemark());
        merchantStoreEntity.setCreateTime(merchantStoreModel.getCreateTime());
        merchantStoreEntity.setUpdateTime(merchantStoreModel.getUpdateTime());
        merchantStoreEntity.setBillSwitch(merchantStoreModel.getBillSwitch());
        merchantStoreEntity.setOnlinePayment(merchantStoreModel.getOnlinePayment());
        merchantStoreEntity.setStoreNo(merchantStoreModel.getStoreNo());
        merchantStoreEntity.setBalanceAuthority(merchantStoreModel.getBalanceAuthority());
        merchantStoreEntity.setProvince(merchantStoreModel.getProvince());
        merchantStoreEntity.setCity(merchantStoreModel.getCity());
        merchantStoreEntity.setArea(merchantStoreModel.getArea());
        merchantStoreEntity.setAddress(merchantStoreModel.getAddress());
        merchantStoreEntity.setHouseNumber(merchantStoreModel.getHouseNumber());
        merchantStoreEntity.setDeliveryAddress(merchantStoreModel.getDeliveryAddress());
        merchantStoreEntity.setAccountId(merchantStoreModel.getAccountId());
        merchantStoreEntity.setPhone(merchantStoreModel.getPhone());
        merchantStoreEntity.setAccountName(merchantStoreModel.getAccountName());
        merchantStoreEntity.setAccountType(merchantStoreModel.getAccountType());
        merchantStoreEntity.setAccountStatus(merchantStoreModel.getAccountStatus());
        merchantStoreEntity.setLastLoginTime(merchantStoreModel.getLastLoginTime());
        merchantStoreEntity.setPlaceOrderPermissionTimeLimited(merchantStoreModel.getPlaceOrderPermissionTimeLimited ());
        merchantStoreEntity.setPlaceOrderPermissionExpiryTime(merchantStoreModel.getPlaceOrderPermissionExpiryTime ());
        merchantStoreEntity.setEnableOfflinePayment (merchantStoreModel.getEnableOfflinePayment ());
        return merchantStoreEntity;
    }

    public static List<MerchantStoreModel> toMerchantStoreAndAddressModelList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreModel> merchantStoreModelList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreModelList.add(toMerchantStoreAndAddressModel(merchantStoreEntity));
        }
        return merchantStoreModelList;
    }

    public static MerchantStoreModel toMerchantStoreAndAddressModel(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStoreModel merchantStoreModel = new MerchantStoreModel();
        merchantStoreModel.setId(merchantStoreEntity.getId());
        merchantStoreModel.setTenantId(merchantStoreEntity.getTenantId());
        merchantStoreModel.setStoreName(merchantStoreEntity.getStoreName());
        merchantStoreModel.setType(merchantStoreEntity.getType());
        merchantStoreModel.setRegisterTime(merchantStoreEntity.getRegisterTime());
        merchantStoreModel.setStatus(merchantStoreEntity.getStatus());
        merchantStoreModel.setAuditRemark(merchantStoreEntity.getAuditRemark());
        merchantStoreModel.setRemark(merchantStoreEntity.getRemark());
        merchantStoreModel.setAuditTime(merchantStoreEntity.getAuditTime());
        merchantStoreModel.setCreateTime(merchantStoreEntity.getCreateTime());
        merchantStoreModel.setUpdateTime(merchantStoreEntity.getUpdateTime());
        merchantStoreModel.setBillSwitch(merchantStoreEntity.getBillSwitch());
        merchantStoreModel.setOnlinePayment(merchantStoreEntity.getOnlinePayment());
        merchantStoreModel.setBalanceAuthority(merchantStoreEntity.getBalanceAuthority());
        merchantStoreModel.setStoreNo(merchantStoreEntity.getStoreNo());
        merchantStoreModel.setProvince(merchantStoreEntity.getProvince());
        merchantStoreModel.setCity(merchantStoreEntity.getCity());
        merchantStoreModel.setArea(merchantStoreEntity.getArea());
        merchantStoreModel.setAddress(merchantStoreEntity.getAddress());
        merchantStoreModel.setHouseNumber(merchantStoreEntity.getHouseNumber());
        merchantStoreModel.setDeliveryAddress(merchantStoreEntity.getDeliveryAddress());
        return merchantStoreModel;
    }
}
