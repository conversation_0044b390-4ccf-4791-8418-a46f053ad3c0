package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
@Data
public class MerchantAddress {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 们拍好
	 */
	private String houseNumber;

	/**
	 * 商家腾讯地图坐标
	 */
	private String poiNote;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


	/**
	 * 是否是默认地址：0、否 1、是
	 */
	private Integer defaultFlag;

	/**
	 * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
	 */
	private Integer status;

	/**
	 * 鲜沐contact联系人表id
	 */
	private Long xmContactId;

	/**
	 * 审核备注
	 */
	private String remark;

	/**
	 * 地址备注
	 */
	private String addressRemark;

	/**
	 * 鲜沐merchant表id,冗余字段便于鲜沐接入
	 */
	private Long mId;


}