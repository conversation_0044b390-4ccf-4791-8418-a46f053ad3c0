package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.XmMerchantAdapter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-10-23 13:47:58
 * @version 1.0
 *
 */
@Mapper
public interface XmMerchantAdapterConverter {

    XmMerchantAdapterConverter INSTANCE = Mappers.getMapper(XmMerchantAdapterConverter.class);

    List<XmMerchantAdapterEntity> toXmMerchantAdapterEntityList(List<XmMerchantAdapter> xmMerchantAdapterList);

    XmMerchantAdapterEntity toXmMerchantAdapterEntity(XmMerchantAdapter xmMerchantAdapter);

    List<XmMerchantAdapter> toXmMerchantAdapterList(List<XmMerchantAdapterEntity> xmMerchantAdapterEntityList);

    XmMerchantAdapter toXmMerchantAdapter(XmMerchantAdapterEntity xmMerchantAdapterEntity);
}
