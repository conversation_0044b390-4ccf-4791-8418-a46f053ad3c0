package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreGroupConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreGroupDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-05-11 10:55:37
* @version 1.0
*
*/
@Repository
public class MerchantStoreGroupCommandRepositoryImpl implements MerchantStoreGroupCommandRepository {

    @Autowired
    private MerchantStoreGroupDao merchantStoreGroupDao;

    @Override
    public MerchantStoreGroupEntity createOrUpdate(MerchantStoreGroupEntity entity) {
        MerchantStoreGroup merchantStoreGroup = MerchantStoreGroupConverter.toMerchantStoreGroup(entity);
        merchantStoreGroupDao.saveOrUpdate(merchantStoreGroup);
        return MerchantStoreGroupConverter.toMerchantStoreGroupEntity(merchantStoreGroup);
    }

    @Override
    public void delete(Long id) {
        merchantStoreGroupDao.removeById(id);
    }
}