package net.xianmu.usercenter.infrastructure.regional.converter;

import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.infrastructure.regional.model.RegionalOrganization;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-06 16:32:07
 * @version 1.0
 *
 */
public class RegionalOrganizationConverter {


    private RegionalOrganizationConverter() {
        // 无需实现
    }

    public static List<RegionalOrganization> toRegionalOrganizationList(List<RegionalOrganizationEntity> regionalOrganizationEntityList) {
        if (regionalOrganizationEntityList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganization> regionalOrganizationList = new ArrayList<>();
        for (RegionalOrganizationEntity regionalOrganizationEntity : regionalOrganizationEntityList) {
            regionalOrganizationList.add(toRegionalOrganization(regionalOrganizationEntity));
        }
        return regionalOrganizationList;
    }

    public static RegionalOrganization toRegionalOrganization(RegionalOrganizationEntity regionalOrganizationEntity) {
        if (regionalOrganizationEntity == null) {
            return null;
        }
        RegionalOrganization regionalOrganization = new RegionalOrganization();
        regionalOrganization.setId(regionalOrganizationEntity.getId());
        regionalOrganization.setTenantId(regionalOrganizationEntity.getTenantId());
        regionalOrganization.setPhone(regionalOrganizationEntity.getPhone());
        regionalOrganization.setOrganizationName(regionalOrganizationEntity.getOrganizationName());
        regionalOrganization.setSource(regionalOrganizationEntity.getSource());
        regionalOrganization.setSize(regionalOrganizationEntity.getSize());
        regionalOrganization.setStatus(regionalOrganizationEntity.getStatus());
        regionalOrganization.setAdminId(regionalOrganizationEntity.getAdminId());
        regionalOrganization.setCreateTime(regionalOrganizationEntity.getCreateTime());
        regionalOrganization.setUpdateTime(regionalOrganizationEntity.getUpdateTime());
        regionalOrganization.setCreator(regionalOrganizationEntity.getCreator());
        regionalOrganization.setUpdater(regionalOrganizationEntity.getUpdater());
        return regionalOrganization;
    }

    public static List<RegionalOrganizationEntity> toRegionalOrganizationEntityList(List<RegionalOrganization> regionalOrganizationList) {
        if (regionalOrganizationList == null) {
            return Collections.emptyList();
        }
        List<RegionalOrganizationEntity> regionalOrganizationEntityList = new ArrayList<>();
        for (RegionalOrganization regionalOrganization : regionalOrganizationList) {
            regionalOrganizationEntityList.add(toRegionalOrganizationEntity(regionalOrganization));
        }
        return regionalOrganizationEntityList;
    }

    public static RegionalOrganizationEntity toRegionalOrganizationEntity(RegionalOrganization regionalOrganization) {
        if (regionalOrganization == null) {
            return null;
        }
        RegionalOrganizationEntity regionalOrganizationEntity = new RegionalOrganizationEntity();
        regionalOrganizationEntity.setId(regionalOrganization.getId());
        regionalOrganizationEntity.setTenantId(regionalOrganization.getTenantId());
        regionalOrganizationEntity.setPhone(regionalOrganization.getPhone());
        regionalOrganizationEntity.setOrganizationName(regionalOrganization.getOrganizationName());
        regionalOrganizationEntity.setSource(regionalOrganization.getSource());
        regionalOrganizationEntity.setSize(regionalOrganization.getSize());
        regionalOrganizationEntity.setStatus(regionalOrganization.getStatus());
        regionalOrganizationEntity.setAdminId(regionalOrganization.getAdminId());
        regionalOrganizationEntity.setCreateTime(regionalOrganization.getCreateTime());
        regionalOrganizationEntity.setUpdateTime(regionalOrganization.getUpdateTime());
        regionalOrganizationEntity.setCreator(regionalOrganization.getCreator());
        regionalOrganizationEntity.setUpdater(regionalOrganization.getUpdater());
        return regionalOrganizationEntity;
    }
}
