package net.xianmu.usercenter.infrastructure.merchant.repository;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreAccountConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreAccountDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-08 16:09:34
* @version 1.0
*
*/
@Repository
public class MerchantStoreAccountQueryRepositoryImpl implements MerchantStoreAccountQueryRepository {

    @Autowired
    private MerchantStoreAccountDao merchantStoreAccountDao;

    @Override
    public MerchantStoreAccountEntity selectById(Long id) {
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntity(merchantStoreAccountDao.getById(id));
    }

    @Override
    public List<MerchantStoreAccountEntity> selectByIds(List<Long> idList) {
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntityList(merchantStoreAccountDao.getBaseMapper().selectBatchIds(idList));
    }

    @Override
    public List<MerchantStoreAccountEntity> selectByStoreId(Long tenantId, Long storeId) {
        return selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(tenantId).storeId(storeId).build());
    }

    @Override
    public MerchantStoreAccountEntity selectByXmAccountId(Long xmAccountId) {
        List<MerchantStoreAccountEntity> entities = selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID).xmAccountId(xmAccountId).build());
        return CollUtil.isEmpty(entities) ? null : entities.get(0);
    }

    @Override
    public List<MerchantStoreAccountEntity> selectByCondition(MerchantStoreAccountQueryInput req) {
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntityList(merchantStoreAccountDao.listByCondition(req));    }

    @Override
    public List<MerchantStoreAccountEntity> selectByConditionWithFuzzy(MerchantStoreAccountQueryInput req) {
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntityList(merchantStoreAccountDao.listByConditionWithFuzzy(req));    }

    @Override
    public List<MerchantStoreAccountEntity> selectSaasAccount() {
        return  MerchantStoreAccountConverter.toMerchantStoreAccountEntityList(merchantStoreAccountDao.selectSaasAccount());
    }

    @Override
    public List<MerchantStoreAccountEntity> selectXmErrorAccount() {
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntityList(merchantStoreAccountDao.getBaseMapper().selectXmErrorAccount());
    }
}