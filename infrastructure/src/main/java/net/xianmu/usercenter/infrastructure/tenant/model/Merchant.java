package net.xianmu.usercenter.infrastructure.tenant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-11 10:44:50
 * @version 1.0
 *
 */
@Data
public class Merchant {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户信息
	 */
	private Long tenantId;

	/**
	 * 品牌名称
	 */
	private String merchantName;

	/**
	 * logo
	 */
	private String logoImage;

	/**
	 * 背景图
	 */
	private String backgroundImage;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	
}