package net.xianmu.usercenter.infrastructure.merchant.repository;

import cn.hutool.core.collection.CollUtil;
import net.xianmu.usercenter.common.input.query.MerchantStoreChangeLogQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreChangeLogRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreChangeLogConverter;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreConfigConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreChangeLogDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreChangeLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-10 17:23:10
 */
@Repository
public class MerchantStoreChangeLogRepositoryImpl implements MerchantStoreChangeLogRepository {

    @Autowired
    private MerchantStoreChangeLogDao merchantStoreChangeLogDao;

    @Override
    public MerchantStoreChangeLogEntity createOrUpdate(MerchantStoreChangeLogEntity entity) {
        MerchantStoreChangeLog log = MerchantStoreChangeLogConverter.toMerchantStoreChangeLog(entity);
        merchantStoreChangeLogDao.saveOrUpdate(log);
        return MerchantStoreChangeLogConverter.toMerchantStoreChangeLogEntity(log);
    }

    @Override
    public void createBatch(List<MerchantStoreChangeLogEntity> entities) {
        merchantStoreChangeLogDao.saveBatch(MerchantStoreChangeLogConverter.toMerchantStoreChangeLogList(entities));
    }

    @Override
    public void updateChannelCode(Long id, String inviterChannelCode, String merchantChannelCode) {
        merchantStoreChangeLogDao.updateChannelCode(id, inviterChannelCode, merchantChannelCode);
    }

    @Override
    public MerchantStoreChangeLogEntity selectByStoreId(Long storeId, Integer opType) {
        MerchantStoreChangeLogQueryInput input = MerchantStoreChangeLogQueryInput.builder().storeId(storeId).opType(opType).build();
        List<MerchantStoreChangeLog> changeLogs = merchantStoreChangeLogDao.listByCondition(input);
        return CollUtil.isEmpty(changeLogs) ? null : MerchantStoreChangeLogConverter.toMerchantStoreChangeLogEntity(changeLogs.get(0));
    }

    @Override
    public List<MerchantStoreChangeLogEntity> selectByCondition(MerchantStoreChangeLogQueryInput input) {
        List<MerchantStoreChangeLog> changeLogs = merchantStoreChangeLogDao.listByCondition(input);
        return MerchantStoreChangeLogConverter.toMerchantStoreChangeLogEntityList(changeLogs);
    }
}