package net.xianmu.usercenter.infrastructure.invoice.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-13 13:58:41
 * @version 1.0
 *
 */
@Data
public class InvoiceConfig {
	/**
	 * 自增长主键
	 */
	//@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 根据类型的不同，取自regional_organization/merchant_store表id
	 */
	private Long bizId;

	/**
	 * 0:门店自有抬头；1:区域组织下的抬头
	 */
	private Integer type;

	/**
	 * 发票抬头
	 */
	private String invoiceTitle;

	/**
	 * 税号
	 */
	private String taxNumber;

	/**
	 * 开户账号
	 */
	private String openAccount;

	/**
	 * 开户银行
	 */
	private String openBank;

	/**
	 * 公司地址
	 */
	private String companyAddress;

	/**
	 * 公司电话
	 */
	private String companyPhone;

	/**
	 * 邮寄地址
	 */
	private String mailAddress;

	/**
	 * 收件人
	 */
	private String companyReceiver;

	/**
	 * 邮箱
	 */
	private String companyEmail;

	/**
	 * 0:生效中（默认), 1:(失效)
	 */
	private Integer validStatus;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建抬头提交时间
	 */
	private LocalDateTime createTime;

	/**
	 * 联系方式
	 */
	private String linkMethod;

	/**
	 * 营业执照地址
	 */
	private String businessLicenseAddress;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 修改人
	 */
	private String updater;


	
}