package net.xianmu.usercenter.infrastructure.tenant.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <AUTHOR>
 * @date 2023-05-19
 */
@Data
public class CrmFollow implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id 自增
     */
    private Long id;

    /**
     * clue 线索
     */
    private String type;

    /**
     * 主题id
     */
    private Long subjectId;

    /**
     * bd id
     */
    private Integer bdId;

    /**
     * 跟进时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime followTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 跟进目的
     */
    private String followGoal;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 下次跟进
     */
    private String nextFollow;

    /**
     * 图片
     */
    private String images;
    /**
     * 跟进方式
     */
    private String followModel;


    public CrmFollow() {}
}