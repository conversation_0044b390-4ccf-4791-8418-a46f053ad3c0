package net.xianmu.usercenter.infrastructure.tenant.converter;

import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.infrastructure.tenant.model.Merchant;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/11 11:07
 */
public class MerchantConverter {


    private MerchantConverter() {
        // 无需实现
    }

    public static List<MerchantEntity> toMerchantEntityList(List<Merchant> merchantList) {
        if (merchantList == null) {
            return Collections.emptyList();
        }
        List<MerchantEntity> merchantEntityList = new ArrayList<>();
        for (Merchant merchant : merchantList) {
            merchantEntityList.add(toMerchantEntity(merchant));
        }
        return merchantEntityList;
    }

    public static MerchantEntity toMerchantEntity(Merchant merchant) {
        if (merchant == null) {
            return null;
        }
        MerchantEntity merchantEntity = new MerchantEntity();
        merchantEntity.setId(merchant.getId());
        merchantEntity.setTenantId(merchant.getTenantId());
        merchantEntity.setMerchantName(merchant.getMerchantName());
        merchantEntity.setLogoImage(merchant.getLogoImage());
        merchantEntity.setBackgroundImage(merchant.getBackgroundImage());
        merchantEntity.setCreateTime(merchant.getCreateTime());
        merchantEntity.setUpdateTime(merchant.getUpdateTime());
        return merchantEntity;
    }

    public static List<Merchant> toMerchantList(List<MerchantEntity> merchantEntityList) {
        if (merchantEntityList == null) {
            return Collections.emptyList();
        }
        List<Merchant> merchantList = new ArrayList<>();
        for (MerchantEntity merchantEntity : merchantEntityList) {
            merchantList.add(toMerchant(merchantEntity));
        }
        return merchantList;
    }

    public static Merchant toMerchant(MerchantEntity merchantEntity) {
        if (merchantEntity == null) {
            return null;
        }
        Merchant merchant = new Merchant();
        merchant.setId(merchantEntity.getId());
        merchant.setTenantId(merchantEntity.getTenantId());
        merchant.setMerchantName(merchantEntity.getMerchantName());
        merchant.setLogoImage(merchantEntity.getLogoImage());
        merchant.setBackgroundImage(merchantEntity.getBackgroundImage());
        merchant.setCreateTime(merchantEntity.getCreateTime());
        merchant.setUpdateTime(merchantEntity.getUpdateTime());
        return merchant;
    }
}
