package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAccountMappingEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.XmMerchantAccountMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-11 16:46:09
 * @version 1.0
 *
 */
public class XmMerchantAccountMappingConverter {


    private XmMerchantAccountMappingConverter() {
        // 无需实现
    }

    public static List<XmMerchantAccountMappingEntity> toXmMerchantAccountMappingEntityList(List<XmMerchantAccountMapping> xmMerchantAccountMappingList) {
        if (xmMerchantAccountMappingList == null) {
            return Collections.emptyList();
        }
        List<XmMerchantAccountMappingEntity> xmMerchantAccountMappingEntityList = new ArrayList<>();
        for (XmMerchantAccountMapping xmMerchantAccountMapping : xmMerchantAccountMappingList) {
            xmMerchantAccountMappingEntityList.add(toXmMerchantAccountMappingEntity(xmMerchantAccountMapping));
        }
        return xmMerchantAccountMappingEntityList;
    }

    public static XmMerchantAccountMappingEntity toXmMerchantAccountMappingEntity(XmMerchantAccountMapping xmMerchantAccountMapping) {
        if (xmMerchantAccountMapping == null) {
            return null;
        }
        XmMerchantAccountMappingEntity xmMerchantAccountMappingEntity = new XmMerchantAccountMappingEntity();
        xmMerchantAccountMappingEntity.setId(xmMerchantAccountMapping.getId());
        xmMerchantAccountMappingEntity.setXmAccountId(xmMerchantAccountMapping.getXmAccountId());
        xmMerchantAccountMappingEntity.setAccountId(xmMerchantAccountMapping.getAccountId());
        xmMerchantAccountMappingEntity.setUpdateTime(xmMerchantAccountMapping.getUpdateTime());
        xmMerchantAccountMappingEntity.setCreateTime(xmMerchantAccountMapping.getCreateTime());
        return xmMerchantAccountMappingEntity;
    }

    public static List<XmMerchantAccountMapping> toXmMerchantAccountMappingList(List<XmMerchantAccountMappingEntity> xmMerchantAccountMappingEntityList) {
        if (xmMerchantAccountMappingEntityList == null) {
            return Collections.emptyList();
        }
        List<XmMerchantAccountMapping> xmMerchantAccountMappingList = new ArrayList<>();
        for (XmMerchantAccountMappingEntity xmMerchantAccountMappingEntity : xmMerchantAccountMappingEntityList) {
            xmMerchantAccountMappingList.add(toXmMerchantAccountMapping(xmMerchantAccountMappingEntity));
        }
        return xmMerchantAccountMappingList;
    }

    public static XmMerchantAccountMapping toXmMerchantAccountMapping(XmMerchantAccountMappingEntity xmMerchantAccountMappingEntity) {
        if (xmMerchantAccountMappingEntity == null) {
            return null;
        }
        XmMerchantAccountMapping xmMerchantAccountMapping = new XmMerchantAccountMapping();
        xmMerchantAccountMapping.setId(xmMerchantAccountMappingEntity.getId());
        xmMerchantAccountMapping.setXmAccountId(xmMerchantAccountMappingEntity.getXmAccountId());
        xmMerchantAccountMapping.setAccountId(xmMerchantAccountMappingEntity.getAccountId());
        xmMerchantAccountMapping.setUpdateTime(xmMerchantAccountMappingEntity.getUpdateTime());
        xmMerchantAccountMapping.setCreateTime(xmMerchantAccountMappingEntity.getCreateTime());
        return xmMerchantAccountMapping;
    }
}
