package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
@Data
public class MerchantContact {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 地址id
	 */
	private Long addressId;

	/**
	 * 联系人名称
	 */
	private String name;

	/**
	 * 联系人手机号
	 */
	private String phone;

	/**
	 * 是否是默认联系人：0、否 1、是
	 */
	private Integer defaultFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	
}