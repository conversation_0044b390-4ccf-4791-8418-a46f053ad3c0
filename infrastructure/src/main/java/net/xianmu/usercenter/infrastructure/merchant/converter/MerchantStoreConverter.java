package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
public class MerchantStoreConverter {

    private MerchantStoreConverter() {
        // 无需实现
    }

    public static List<MerchantStoreEntity> toMerchantStoreEntityList(List<MerchantStore> merchantStoreList) {
        if (merchantStoreList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreEntity> merchantStoreEntityList = new ArrayList<>();
        for (MerchantStore merchantStore : merchantStoreList) {
            merchantStoreEntityList.add(toMerchantStoreEntity(merchantStore));
        }
        return merchantStoreEntityList;
    }

    public static MerchantStoreEntity toMerchantStoreEntity(MerchantStore merchantStore) {
        if (merchantStore == null) {
            return null;
        }
        MerchantStoreEntity merchantStoreEntity = new MerchantStoreEntity();
        merchantStoreEntity.setId(merchantStore.getId());
        merchantStoreEntity.setTenantId(merchantStore.getTenantId());
        merchantStoreEntity.setStoreName(merchantStore.getStoreName());
        merchantStoreEntity.setType(merchantStore.getType());
        merchantStoreEntity.setRegisterTime(merchantStore.getRegisterTime());
        merchantStoreEntity.setStatus(merchantStore.getStatus());
        merchantStoreEntity.setAuditRemark(merchantStore.getAuditRemark());
        merchantStoreEntity.setAuditTime(merchantStore.getAuditTime());
        merchantStoreEntity.setRemark(merchantStore.getRemark());
        merchantStoreEntity.setCreateTime(merchantStore.getCreateTime());
        merchantStoreEntity.setUpdateTime(merchantStore.getUpdateTime());
        merchantStoreEntity.setBillSwitch(merchantStore.getBillSwitch());
        merchantStoreEntity.setOnlinePayment(merchantStore.getOnlinePayment());
        merchantStoreEntity.setStoreNo(merchantStore.getStoreNo());
        merchantStoreEntity.setBalanceAuthority(merchantStore.getBalanceAuthority());
        merchantStoreEntity.setBusinessType(merchantStore.getBusinessType());
        merchantStoreEntity.setRegionalId(merchantStore.getRegionalId());
        merchantStoreEntity.setMId(merchantStore.getMId());
        merchantStoreEntity.setChannelCode(merchantStore.getChannelCode());
        merchantStoreEntity.setAreaNo(merchantStore.getAreaNo());
        merchantStoreEntity.setPlaceOrderPermissionTimeLimited(merchantStore.getPlaceOrderPermissionTimeLimited ());
        merchantStoreEntity.setPlaceOrderPermissionExpiryTime(merchantStore.getPlaceOrderPermissionExpiryTime ());
        merchantStoreEntity.setEnableOfflinePayment (merchantStore.getEnableOfflinePayment ());
        merchantStoreEntity.setNonCashAuthority(merchantStore.getNonCashAuthority());
        return merchantStoreEntity;
    }

    public static List<MerchantStore> toMerchantStoreList(List<MerchantStoreEntity> merchantStoreEntityList) {
        if (merchantStoreEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStore> merchantStoreList = new ArrayList<>();
        for (MerchantStoreEntity merchantStoreEntity : merchantStoreEntityList) {
            merchantStoreList.add(toMerchantStore(merchantStoreEntity));
        }
        return merchantStoreList;
    }

    public static MerchantStore toMerchantStore(MerchantStoreEntity merchantStoreEntity) {
        if (merchantStoreEntity == null) {
            return null;
        }
        MerchantStore merchantStore = new MerchantStore();
        merchantStore.setId(merchantStoreEntity.getId());
        merchantStore.setTenantId(merchantStoreEntity.getTenantId());
        merchantStore.setStoreName(merchantStoreEntity.getStoreName());
        merchantStore.setType(merchantStoreEntity.getType());
        merchantStore.setRegisterTime(merchantStoreEntity.getRegisterTime());
        merchantStore.setStatus(merchantStoreEntity.getStatus());
        merchantStore.setAuditRemark(merchantStoreEntity.getAuditRemark());
        merchantStore.setAuditTime(merchantStoreEntity.getAuditTime());
        merchantStore.setRemark(merchantStoreEntity.getRemark());
        merchantStore.setCreateTime(merchantStoreEntity.getCreateTime());
        merchantStore.setUpdateTime(merchantStoreEntity.getUpdateTime());
        merchantStore.setBillSwitch(merchantStoreEntity.getBillSwitch());
        merchantStore.setOnlinePayment(merchantStoreEntity.getOnlinePayment());
        merchantStore.setStoreNo(merchantStoreEntity.getStoreNo());
        merchantStore.setBalanceAuthority(merchantStoreEntity.getBalanceAuthority());
        merchantStore.setNonCashAuthority(merchantStoreEntity.getNonCashAuthority());
        merchantStore.setBusinessType(merchantStoreEntity.getBusinessType());
        merchantStore.setRegionalId(merchantStoreEntity.getRegionalId());
        merchantStore.setMId(merchantStoreEntity.getMId());
        merchantStore.setChannelCode(merchantStoreEntity.getChannelCode());
        merchantStore.setAreaNo(merchantStoreEntity.getAreaNo());
        merchantStore.setPlaceOrderPermissionTimeLimited(merchantStoreEntity.getPlaceOrderPermissionTimeLimited ());
        merchantStore.setPlaceOrderPermissionExpiryTime(merchantStoreEntity.getPlaceOrderPermissionExpiryTime ());
        merchantStore.setEnableOfflinePayment (merchantStoreEntity.getEnableOfflinePayment ());
        return merchantStore;
    }
}
