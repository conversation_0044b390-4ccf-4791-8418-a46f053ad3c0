package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupMappingCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreGroupMappingConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreGroupMappingDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-06-01 18:18:22
* @version 1.0
*
*/
@Repository
public class MerchantStoreGroupMappingCommandRepositoryImpl implements MerchantStoreGroupMappingCommandRepository {

    @Autowired
    private MerchantStoreGroupMappingDao merchantStoreGroupMappingDao;

    @Override
    public void createOrUpdate(MerchantStoreGroupMappingEntity entity) {
        merchantStoreGroupMappingDao.saveOrUpdate(MerchantStoreGroupMappingConverter.toMerchantStoreGroupMapping(entity));
    }

    @Override
    public void updateGroupIdByStoreIds(Long groupId, Long tenantId,  List<Long> storeIdList) {
        merchantStoreGroupMappingDao.updateGroupIdByStoreIds(groupId, tenantId, storeIdList);
    }
}