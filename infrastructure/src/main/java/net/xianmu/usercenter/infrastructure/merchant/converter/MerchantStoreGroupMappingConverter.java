package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroupMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
public class MerchantStoreGroupMappingConverter {


    private MerchantStoreGroupMappingConverter() {
        // 无需实现
    }

    public static List<MerchantStoreGroupMappingEntity> toMerchantStoreGroupMappingEntityList(List<MerchantStoreGroupMapping> merchantStoreGroupMappingList) {
        if (merchantStoreGroupMappingList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupMappingEntity> merchantStoreGroupMappingEntityList = new ArrayList<>();
        for (MerchantStoreGroupMapping merchantStoreGroupMapping : merchantStoreGroupMappingList) {
            merchantStoreGroupMappingEntityList.add(toMerchantStoreGroupMappingEntity(merchantStoreGroupMapping));
        }
        return merchantStoreGroupMappingEntityList;
    }

    public static MerchantStoreGroupMappingEntity toMerchantStoreGroupMappingEntity(MerchantStoreGroupMapping merchantStoreGroupMapping) {
        if (merchantStoreGroupMapping == null) {
            return null;
        }
        MerchantStoreGroupMappingEntity merchantStoreGroupMappingEntity = new MerchantStoreGroupMappingEntity();
        merchantStoreGroupMappingEntity.setId(merchantStoreGroupMapping.getId());
        merchantStoreGroupMappingEntity.setTenantId(merchantStoreGroupMapping.getTenantId());
        merchantStoreGroupMappingEntity.setStoreId(merchantStoreGroupMapping.getStoreId());
        merchantStoreGroupMappingEntity.setGroupId(merchantStoreGroupMapping.getGroupId());
        merchantStoreGroupMappingEntity.setCreateTime(merchantStoreGroupMapping.getCreateTime());
        merchantStoreGroupMappingEntity.setUpdateTime(merchantStoreGroupMapping.getUpdateTime());
        return merchantStoreGroupMappingEntity;
    }

    public static List<MerchantStoreGroupMapping> toMerchantStoreGroupMappingList(List<MerchantStoreGroupMappingEntity> merchantStoreGroupMappingEntityList) {
        if (merchantStoreGroupMappingEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupMapping> merchantStoreGroupMappingList = new ArrayList<>();
        for (MerchantStoreGroupMappingEntity merchantStoreGroupMappingEntity : merchantStoreGroupMappingEntityList) {
            merchantStoreGroupMappingList.add(toMerchantStoreGroupMapping(merchantStoreGroupMappingEntity));
        }
        return merchantStoreGroupMappingList;
    }

    public static MerchantStoreGroupMapping toMerchantStoreGroupMapping(MerchantStoreGroupMappingEntity merchantStoreGroupMappingEntity) {
        if (merchantStoreGroupMappingEntity == null) {
            return null;
        }
        MerchantStoreGroupMapping merchantStoreGroupMapping = new MerchantStoreGroupMapping();
        merchantStoreGroupMapping.setId(merchantStoreGroupMappingEntity.getId());
        merchantStoreGroupMapping.setTenantId(merchantStoreGroupMappingEntity.getTenantId());
        merchantStoreGroupMapping.setStoreId(merchantStoreGroupMappingEntity.getStoreId());
        merchantStoreGroupMapping.setGroupId(merchantStoreGroupMappingEntity.getGroupId());
        merchantStoreGroupMapping.setCreateTime(merchantStoreGroupMappingEntity.getCreateTime());
        merchantStoreGroupMapping.setUpdateTime(merchantStoreGroupMappingEntity.getUpdateTime());
        return merchantStoreGroupMapping;
    }
}
