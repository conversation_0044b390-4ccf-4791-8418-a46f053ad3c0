package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
@Data
public class MerchantStoreGroup {
	/**
	 * primary key
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户Id
	 */
	private Long tenantId;

	/**
	 * 分组名称
	 */
	private String name;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 0、非默认分组 1、默认分组
	 */
	private Integer type;

	
}