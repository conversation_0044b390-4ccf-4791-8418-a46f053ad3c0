package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.MerchantStoreExtQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreExtMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreExt;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-30 14:08:25
 */
@Component
public class MerchantStoreExtDao extends ServiceImpl<MerchantStoreExtMapper, MerchantStoreExt> {

    /**
     * @param
     * @return
     */
    public List<MerchantStoreExt> listByCondition(MerchantStoreExtQueryInput req) {
        LambdaQueryWrapper<MerchantStoreExt> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantStoreExt::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreExt::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreExt::getStoreId, req.getStoreId());
        query.eq(Objects.nonNull(req.getPopView()), MerchantStoreExt::getPopView, req.getPopView());
        query.eq(Objects.nonNull(req.getChangePop()), MerchantStoreExt::getChangePop, req.getChangePop());
        query.eq(Objects.nonNull(req.getFirstLoginPop()), MerchantStoreExt::getFirstLoginPop, req.getFirstLoginPop());
        query.eq(Objects.nonNull(req.getDisplayButton()), MerchantStoreExt::getDisplayButton, req.getDisplayButton());
        query.eq(Objects.nonNull(req.getPreRegisterFlag()), MerchantStoreExt::getPreRegisterFlag, req.getPreRegisterFlag());
        query.eq(Objects.nonNull(req.getMockLoginFlag()), MerchantStoreExt::getMockLoginFlag, req.getMockLoginFlag());
        query.eq(StrUtil.isNotBlank(req.getProvince()), MerchantStoreExt::getProvince, req.getProvince());
        query.eq(StrUtil.isNotBlank(req.getCity()), MerchantStoreExt::getCity, req.getCity());
        query.eq(StrUtil.isNotBlank(req.getArea()), MerchantStoreExt::getArea, req.getArea());
        query.eq(StrUtil.isNotBlank(req.getCreator()), MerchantStoreExt::getCreator, req.getCreator());
        query.eq(StrUtil.isNotBlank(req.getUpdater()), MerchantStoreExt::getUpdater, req.getUpdater());
        query.eq(Objects.nonNull(req.getOperateStatus()), MerchantStoreExt::getOperateStatus, req.getOperateStatus());
        return list(query);
    }

    public MerchantStoreExt selectByStoreId(Long storeId) {
        LambdaQueryWrapper<MerchantStoreExt> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreExt::getStoreId, storeId);
        return getOne(query);
    }
}
