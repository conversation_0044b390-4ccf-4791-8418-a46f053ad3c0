package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantAddressAndContactModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 13:50:22
 * @version 1.0
 *
 */
public class MerchantAddressModelConverter {


    private MerchantAddressModelConverter() {
        // 无需实现
    }


    public static List<MerchantAddressEntity> toMerchantAddressEntityList(List<MerchantAddressAndContactModel> merchantAddressAndContactModelList) {
        if (merchantAddressAndContactModelList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressEntity> merchantAddressEntityList = new ArrayList<>();
        for (MerchantAddressAndContactModel merchantAddressAndContactModel : merchantAddressAndContactModelList) {
            merchantAddressEntityList.add(toMerchantAddressEntity(merchantAddressAndContactModel));
        }
        return merchantAddressEntityList;
    }

    public static MerchantAddressEntity toMerchantAddressEntity(MerchantAddressAndContactModel merchantAddressAndContactModel) {
        if (merchantAddressAndContactModel == null) {
            return null;
        }
        MerchantAddressEntity merchantAddressEntity = new MerchantAddressEntity();
        merchantAddressEntity.setId(merchantAddressAndContactModel.getId());
        merchantAddressEntity.setTenantId(merchantAddressAndContactModel.getTenantId());
        merchantAddressEntity.setStoreId(merchantAddressAndContactModel.getStoreId());
        merchantAddressEntity.setProvince(merchantAddressAndContactModel.getProvince());
        merchantAddressEntity.setCity(merchantAddressAndContactModel.getCity());
        merchantAddressEntity.setArea(merchantAddressAndContactModel.getArea());
        merchantAddressEntity.setAddress(merchantAddressAndContactModel.getAddress());
        merchantAddressEntity.setHouseNumber(merchantAddressAndContactModel.getHouseNumber());
        merchantAddressEntity.setPoiNote(merchantAddressAndContactModel.getPoiNote());
        merchantAddressEntity.setDefaultFlag(merchantAddressAndContactModel.getDefaultFlag());
        merchantAddressEntity.setStatus(merchantAddressAndContactModel.getStatus());
        merchantAddressEntity.setContactName(merchantAddressAndContactModel.getContactName());
        merchantAddressEntity.setContactPhone(merchantAddressAndContactModel.getContactPhone());
        merchantAddressEntity.setDeliveryAddress(merchantAddressAndContactModel.getDeliveryAddress());
// Not mapped TO fields:
// createTime
// updateTime
        return merchantAddressEntity;
    }

    public static List<MerchantAddressAndContactModel> toMerchantAddressAndContactModelList(List<MerchantAddressEntity> merchantAddressEntityList) {
        if (merchantAddressEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAddressAndContactModel> merchantAddressAndContactModelList = new ArrayList<>();
        for (MerchantAddressEntity merchantAddressEntity : merchantAddressEntityList) {
            merchantAddressAndContactModelList.add(toMerchantAddressAndContactModel(merchantAddressEntity));
        }
        return merchantAddressAndContactModelList;
    }

    public static MerchantAddressAndContactModel toMerchantAddressAndContactModel(MerchantAddressEntity merchantAddressEntity) {
        if (merchantAddressEntity == null) {
            return null;
        }
        MerchantAddressAndContactModel merchantAddressAndContactModel = new MerchantAddressAndContactModel();
        merchantAddressAndContactModel.setId(merchantAddressEntity.getId());
        merchantAddressAndContactModel.setTenantId(merchantAddressEntity.getTenantId());
        merchantAddressAndContactModel.setStoreId(merchantAddressEntity.getStoreId());
        merchantAddressAndContactModel.setProvince(merchantAddressEntity.getProvince());
        merchantAddressAndContactModel.setCity(merchantAddressEntity.getCity());
        merchantAddressAndContactModel.setArea(merchantAddressEntity.getArea());
        merchantAddressAndContactModel.setAddress(merchantAddressEntity.getAddress());
        merchantAddressAndContactModel.setHouseNumber(merchantAddressEntity.getHouseNumber());
        merchantAddressAndContactModel.setPoiNote(merchantAddressEntity.getPoiNote());
        merchantAddressAndContactModel.setDefaultFlag(merchantAddressEntity.getDefaultFlag());
        merchantAddressAndContactModel.setStatus(merchantAddressEntity.getStatus());
        merchantAddressAndContactModel.setContactName(merchantAddressEntity.getContactName());
        merchantAddressAndContactModel.setContactPhone(merchantAddressEntity.getContactPhone());
        merchantAddressAndContactModel.setDeliveryAddress(merchantAddressEntity.getDeliveryAddress());
// Not mapped FROM fields:
// createTime
// updateTime
        return merchantAddressAndContactModel;
    }

}
