package net.xianmu.usercenter.infrastructure.merchant.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.infrastructure.merchant.mapper.XmMerchantAccountMappingMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.XmMerchantAccountMapping;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-11 16:46:09
 */
@Component
public class XmMerchantAccountMappingDao extends ServiceImpl<XmMerchantAccountMappingMapper, XmMerchantAccountMapping> {
    public XmMerchantAccountMapping selectByXmAccountId(Long xmAccountId) {
        LambdaQueryWrapper<XmMerchantAccountMapping> query = new LambdaQueryWrapper<>();
        query.eq(XmMerchantAccountMapping::getXmAccountId, xmAccountId);
        return getOne(query);
    }

}
