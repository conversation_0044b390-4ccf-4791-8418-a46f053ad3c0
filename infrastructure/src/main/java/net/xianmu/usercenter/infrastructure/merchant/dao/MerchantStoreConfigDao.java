package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.MerchantStoreConfigQueryInput;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreConfigMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreConfig;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-10 17:23:10
 */
@Component
public class MerchantStoreConfigDao extends ServiceImpl<MerchantStoreConfigMapper, MerchantStoreConfig> {

    /**
     * @param
     * @return
     */
    public List<MerchantStoreConfig> listByCondition(MerchantStoreConfigQueryInput req) {
        LambdaQueryWrapper<MerchantStoreConfig> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantStoreConfig::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreConfig::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreConfig::getStoreId, req.getStoreId());
        return list(query);
    }


    /**
     * @param
     * @return
     */
    public MerchantStoreConfig selectByStoreId(Long storeId) {
        LambdaQueryWrapper<MerchantStoreConfig> query = new LambdaQueryWrapper<>();
        query.eq(MerchantStoreConfig::getStoreId, storeId);
        return getOne(query);
    }


}
