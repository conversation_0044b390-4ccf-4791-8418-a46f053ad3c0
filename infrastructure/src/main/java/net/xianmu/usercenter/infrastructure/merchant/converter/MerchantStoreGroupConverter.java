package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
public class MerchantStoreGroupConverter {


    private MerchantStoreGroupConverter() {
        // 无需实现
    }

    public static MerchantStoreGroup toMerchantStoreGroup(MerchantStoreGroupEntity merchantStoreGroupEntity) {
        if (merchantStoreGroupEntity == null) {
            return null;
        }
        MerchantStoreGroup merchantStoreGroup = new MerchantStoreGroup();
        merchantStoreGroup.setId(merchantStoreGroupEntity.getId());
        merchantStoreGroup.setTenantId(merchantStoreGroupEntity.getTenantId());
        merchantStoreGroup.setName(merchantStoreGroupEntity.getName());
        merchantStoreGroup.setCreateTime(merchantStoreGroupEntity.getCreateTime());
        merchantStoreGroup.setUpdateTime(merchantStoreGroupEntity.getUpdateTime());
        merchantStoreGroup.setType(merchantStoreGroupEntity.getType());
        return merchantStoreGroup;
    }


    public static List<MerchantStoreGroup> toMerchantStoreGroupList(List<MerchantStoreGroupEntity> merchantStoreGroupEntityList) {
        if (merchantStoreGroupEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroup> merchantStoreGroupList = new ArrayList<>();
        for (MerchantStoreGroupEntity merchantStoreGroupEntity : merchantStoreGroupEntityList) {
            merchantStoreGroupList.add(toMerchantStoreGroup(merchantStoreGroupEntity));
        }
        return merchantStoreGroupList;
    }

    public static List<MerchantStoreGroupEntity> toMerchantStoreGroupEntityList(List<MerchantStoreGroup> merchantStoreGroupList) {
        if (merchantStoreGroupList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupEntity> merchantStoreGroupEntityList = new ArrayList<>();
        for (MerchantStoreGroup merchantStoreGroup : merchantStoreGroupList) {
            merchantStoreGroupEntityList.add(toMerchantStoreGroupEntity(merchantStoreGroup));
        }
        return merchantStoreGroupEntityList;
    }

    public static MerchantStoreGroupEntity toMerchantStoreGroupEntity(MerchantStoreGroup merchantStoreGroup) {
        if (merchantStoreGroup == null) {
            return null;
        }
        MerchantStoreGroupEntity merchantStoreGroupEntity = new MerchantStoreGroupEntity();
        merchantStoreGroupEntity.setId(merchantStoreGroup.getId());
        merchantStoreGroupEntity.setTenantId(merchantStoreGroup.getTenantId());
        merchantStoreGroupEntity.setName(merchantStoreGroup.getName());
        merchantStoreGroupEntity.setCreateTime(merchantStoreGroup.getCreateTime());
        merchantStoreGroupEntity.setUpdateTime(merchantStoreGroup.getUpdateTime());
        merchantStoreGroupEntity.setType(merchantStoreGroup.getType());
// Not mapped TO fields:
// storeId
        return merchantStoreGroupEntity;
    }
}
