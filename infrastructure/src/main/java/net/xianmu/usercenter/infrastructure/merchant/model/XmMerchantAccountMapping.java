package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-11 16:46:09
 * @version 1.0
 *
 */
@Data
public class XmMerchantAccountMapping {
	/**
	 * 自增长主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 鲜沐merchant_sub_account表id
	 */
	private Long xmAccountId;

	/**
	 * 用户中心merchant_store_account表的id
	 */
	private Long accountId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}