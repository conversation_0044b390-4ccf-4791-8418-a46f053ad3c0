package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreGroupModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
public class MerchantStoreGroupModelConverter {


    private MerchantStoreGroupModelConverter() {
        // 无需实现
    }






    public static List<MerchantStoreGroupEntity> toMerchantStoreGroupEntityList(List<MerchantStoreGroupModel> merchantStoreGroupModelList) {
        if (merchantStoreGroupModelList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupEntity> merchantStoreGroupEntityList = new ArrayList<>();
        for (MerchantStoreGroupModel merchantStoreGroupModel : merchantStoreGroupModelList) {
            merchantStoreGroupEntityList.add(toMerchantStoreGroupEntity(merchantStoreGroupModel));
        }
        return merchantStoreGroupEntityList;
    }

    public static MerchantStoreGroupEntity toMerchantStoreGroupEntity(MerchantStoreGroupModel merchantStoreGroupModel) {
        if (merchantStoreGroupModel == null) {
            return null;
        }
        MerchantStoreGroupEntity merchantStoreGroupEntity = new MerchantStoreGroupEntity();
        merchantStoreGroupEntity.setStoreId(merchantStoreGroupModel.getStoreId());
        merchantStoreGroupEntity.setName(merchantStoreGroupModel.getMerchantStoreGroupName());
        merchantStoreGroupEntity.setId(merchantStoreGroupModel.getMerchantStoreGroupId());
        merchantStoreGroupEntity.setType(merchantStoreGroupModel.getType());
        merchantStoreGroupEntity.setStoreNum(merchantStoreGroupModel.getStoreNum());
        merchantStoreGroupEntity.setCreateTime(merchantStoreGroupModel.getCreateTime());
        merchantStoreGroupEntity.setUpdateTime(merchantStoreGroupModel.getUpdateTime());
        return merchantStoreGroupEntity;
    }
}
