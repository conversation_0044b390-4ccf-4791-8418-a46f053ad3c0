package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreConfigEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreConfigRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreConfigConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreConfigDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-07-10 17:23:10
* @version 1.0
*
*/
@Repository
public class MerchantStoreConfigRepositoryImpl implements MerchantStoreConfigRepository {

    @Autowired
    private MerchantStoreConfigDao merchantStoreConfigDao;

    @Override
    public MerchantStoreConfigEntity selectByStoreId(Long storeId) {
        return MerchantStoreConfigConverter.toMerchantStoreConfigEntity(merchantStoreConfigDao.selectByStoreId(storeId));
    }

    @Override
    public MerchantStoreConfigEntity createOrUpdate(MerchantStoreConfigEntity entity) {
        MerchantStoreConfig config = MerchantStoreConfigConverter.toMerchantStoreConfig(entity);
        merchantStoreConfigDao.saveOrUpdate(config);
        return MerchantStoreConfigConverter.toMerchantStoreConfigEntity(config);
    }

    @Override
    public void updateSelective(MerchantStoreConfigEntity entity) {
        MerchantStoreConfig config = MerchantStoreConfigConverter.toMerchantStoreConfig(entity);
        merchantStoreConfigDao.updateById(config);
    }
}