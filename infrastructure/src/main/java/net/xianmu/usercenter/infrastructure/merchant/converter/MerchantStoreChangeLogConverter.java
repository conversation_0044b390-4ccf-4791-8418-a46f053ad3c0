package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreChangeLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-11 10:48:04
 * @version 1.0
 *
 */
public class MerchantStoreChangeLogConverter {


    private MerchantStoreChangeLogConverter() {
        // 无需实现
    }

    public static List<MerchantStoreChangeLogEntity> toMerchantStoreChangeLogEntityList(List<MerchantStoreChangeLog> merchantStoreChangeLogList) {
        if (merchantStoreChangeLogList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreChangeLogEntity> merchantStoreChangeLogEntityList = new ArrayList<>();
        for (MerchantStoreChangeLog merchantStoreChangeLog : merchantStoreChangeLogList) {
            merchantStoreChangeLogEntityList.add(toMerchantStoreChangeLogEntity(merchantStoreChangeLog));
        }
        return merchantStoreChangeLogEntityList;
    }

    public static MerchantStoreChangeLogEntity toMerchantStoreChangeLogEntity(MerchantStoreChangeLog merchantStoreChangeLog) {
        if (merchantStoreChangeLog == null) {
            return null;
        }
        MerchantStoreChangeLogEntity merchantStoreChangeLogEntity = new MerchantStoreChangeLogEntity();
        merchantStoreChangeLogEntity.setId(merchantStoreChangeLog.getId());
        merchantStoreChangeLogEntity.setTenantId(merchantStoreChangeLog.getTenantId());
        merchantStoreChangeLogEntity.setRegionalId(merchantStoreChangeLog.getRegionalId());
        merchantStoreChangeLogEntity.setStoreId(merchantStoreChangeLog.getStoreId());
        merchantStoreChangeLogEntity.setInviterChannelCode(merchantStoreChangeLog.getInviterChannelCode());
        merchantStoreChangeLogEntity.setMerchantChannelCode(merchantStoreChangeLog.getMerchantChannelCode());
        merchantStoreChangeLogEntity.setOpName(merchantStoreChangeLog.getOpName());
        merchantStoreChangeLogEntity.setOpType(merchantStoreChangeLog.getOpType());
        merchantStoreChangeLogEntity.setOpRemark(merchantStoreChangeLog.getOpRemark());
        merchantStoreChangeLogEntity.setCreateTime(merchantStoreChangeLog.getCreateTime());
        merchantStoreChangeLogEntity.setUpdateTime(merchantStoreChangeLog.getUpdateTime());
        return merchantStoreChangeLogEntity;
    }

    public static List<MerchantStoreChangeLog> toMerchantStoreChangeLogList(List<MerchantStoreChangeLogEntity> merchantStoreChangeLogEntityList) {
        if (merchantStoreChangeLogEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreChangeLog> merchantStoreChangeLogList = new ArrayList<>();
        for (MerchantStoreChangeLogEntity merchantStoreChangeLogEntity : merchantStoreChangeLogEntityList) {
            merchantStoreChangeLogList.add(toMerchantStoreChangeLog(merchantStoreChangeLogEntity));
        }
        return merchantStoreChangeLogList;
    }

    public static MerchantStoreChangeLog toMerchantStoreChangeLog(MerchantStoreChangeLogEntity merchantStoreChangeLogEntity) {
        if (merchantStoreChangeLogEntity == null) {
            return null;
        }
        MerchantStoreChangeLog merchantStoreChangeLog = new MerchantStoreChangeLog();
        merchantStoreChangeLog.setId(merchantStoreChangeLogEntity.getId());
        merchantStoreChangeLog.setTenantId(merchantStoreChangeLogEntity.getTenantId());
        merchantStoreChangeLog.setRegionalId(merchantStoreChangeLogEntity.getRegionalId());
        merchantStoreChangeLog.setStoreId(merchantStoreChangeLogEntity.getStoreId());
        merchantStoreChangeLog.setInviterChannelCode(merchantStoreChangeLogEntity.getInviterChannelCode());
        merchantStoreChangeLog.setMerchantChannelCode(merchantStoreChangeLogEntity.getMerchantChannelCode());
        merchantStoreChangeLog.setOpName(merchantStoreChangeLogEntity.getOpName());
        merchantStoreChangeLog.setOpType(merchantStoreChangeLogEntity.getOpType());
        merchantStoreChangeLog.setOpRemark(merchantStoreChangeLogEntity.getOpRemark());
        merchantStoreChangeLog.setCreateTime(merchantStoreChangeLogEntity.getCreateTime());
        merchantStoreChangeLog.setUpdateTime(merchantStoreChangeLogEntity.getUpdateTime());
        return merchantStoreChangeLog;
    }
}
