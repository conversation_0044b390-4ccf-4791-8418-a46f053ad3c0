package net.xianmu.usercenter.infrastructure.invoice.repository;

import net.xianmu.usercenter.domain.invoice.entity.InvoiceConfigEntity;
import net.xianmu.usercenter.domain.invoice.repository.InvoiceConfigRepository;
import net.xianmu.usercenter.infrastructure.invoice.converter.InvoiceConfigConverter;
import net.xianmu.usercenter.infrastructure.invoice.dao.InvoiceConfigDao;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-07-13 13:58:41
* @version 1.0
*
*/
//@Repository
public class InvoiceConfigRepositoryImpl implements InvoiceConfigRepository {

    private InvoiceConfigDao invoiceConfigDao;

    @Override
    public InvoiceConfigEntity selectById(Long id) {
        //return InvoiceConfigConverter.toInvoiceConfigEntity(invoiceConfigDao.getById(id));
        return null;
    }

    @Override
    public void createOrUpdate(InvoiceConfigEntity entity) {
        // invoiceConfigDao.saveOrUpdate(InvoiceConfigConverter.toInvoiceConfig(entity));
    }

    @Override
    public void updateSelective(InvoiceConfigEntity entity) {
        // invoiceConfigDao.updateById(InvoiceConfigConverter.toInvoiceConfig(entity));
    }


}