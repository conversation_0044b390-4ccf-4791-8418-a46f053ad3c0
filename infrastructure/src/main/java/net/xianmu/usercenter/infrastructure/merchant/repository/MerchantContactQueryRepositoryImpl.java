package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantContactConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantContactDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 17:08:24
* @version 1.0
*
*/
@Repository
public class MerchantContactQueryRepositoryImpl implements MerchantContactQueryRepository {

    @Autowired
    private MerchantContactDao merchantContactDao;

    @Override
    public MerchantContactEntity selectById(Long id) {
        return MerchantContactConverter.toMerchantContactEntity(merchantContactDao.getById(id));
    }

    @Override
    public MerchantContactEntity selectDefaultContact(Long addressId) {
        return MerchantContactConverter.toMerchantContactEntity(merchantContactDao.selectDefaultContact(addressId));
    }

    @Override
    public MerchantContactEntity selectDefaultContact(Long tenantId, Long addressId) {
        return MerchantContactConverter.toMerchantContactEntity(merchantContactDao.selectDefaultContact(tenantId, addressId));
    }

    @Override
    public List<MerchantContactEntity> selectByCondition(MerchantContactQueryInput req) {
        return MerchantContactConverter.toMerchantContactEntityList(merchantContactDao.listByCondition(req));
    }

    @Override
    public List<MerchantContactEntity> selectByStoreId(Long tenantId, Long storeId){
        return MerchantContactConverter.toMerchantContactEntityList(merchantContactDao.getBaseMapper().selectByStoreId(tenantId, storeId));
    }
}