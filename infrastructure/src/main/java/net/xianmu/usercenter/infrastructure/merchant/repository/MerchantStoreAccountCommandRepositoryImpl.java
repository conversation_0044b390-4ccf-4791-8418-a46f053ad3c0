package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreAccountConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreAccountDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-08 16:09:34
* @version 1.0
*
*/
@Repository
public class MerchantStoreAccountCommandRepositoryImpl implements MerchantStoreAccountCommandRepository {

    @Autowired
    private MerchantStoreAccountDao dao;


    @Override
    public MerchantStoreAccountEntity createOrUpdate(MerchantStoreAccountEntity entity) {
        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountConverter.toMerchantStoreAccount(entity);
        dao.saveOrUpdate(merchantStoreAccount);
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntity(merchantStoreAccount);
    }

    @Override
    public MerchantStoreAccountEntity create(MerchantStoreAccountEntity entity) {
        MerchantStoreAccount merchantStoreAccount = MerchantStoreAccountConverter.toMerchantStoreAccount(entity);
        dao.save(merchantStoreAccount);
        return MerchantStoreAccountConverter.toMerchantStoreAccountEntity(merchantStoreAccount);
    }

    @Override
    public void updateSelective(MerchantStoreAccountEntity entity) {
        dao.updateById(MerchantStoreAccountConverter.toMerchantStoreAccount(entity));
    }

    @Override
    public void updateSelectiveBatch(List<MerchantStoreAccountEntity> entities) {
        dao.updateBatchById(MerchantStoreAccountConverter.toMerchantStoreAccountList(entities));
    }


    @Override
    public void updateManagerToClerk(Long storeId, Long currentAccountId) {
        dao.updateManagerToClerk(storeId, currentAccountId);
    }

    @Override
    public Boolean updateStatusBatch(List<Long> idList, Integer status) {
        return dao.updateStatusBatch(idList, status);
    }

    @Override
    public void delete(Long id) {
        dao.removeById(id);
    }
}