package net.xianmu.usercenter.infrastructure.businessInfo.repository;


import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.domain.businessInfo.repository.BusinessInformationQueryRepository;
import net.xianmu.usercenter.infrastructure.businessInfo.converter.BusinessInformationConverter;
import net.xianmu.usercenter.infrastructure.businessInfo.dao.BusinessInformationDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-08 17:01:58
* @version 1.0
*
*/
@Repository
public class BusinessInformationQueryRepositoryImpl implements BusinessInformationQueryRepository {

    @Autowired
    private BusinessInformationDao businessInformationDao;

    @Override
    public BusinessInformationEntity getBusinessInfoByBizIdAndType(Long bizId, Integer type) {
        return BusinessInformationConverter.toBusinessInformationEntity(businessInformationDao.getBusinessInfoByBizIdAndType(bizId, type));
    }

    @Override
    public BusinessInformationEntity selectById(Long id) {
        return BusinessInformationConverter.toBusinessInformationEntity(businessInformationDao.getById(id));
    }


    @Override
    public List<BusinessInformationEntity> selectByCondition(BusinessInformationQueryInput req) {
        return BusinessInformationConverter.toBusinessInformationEntityList(businessInformationDao.listByCondition(req));
    }

}