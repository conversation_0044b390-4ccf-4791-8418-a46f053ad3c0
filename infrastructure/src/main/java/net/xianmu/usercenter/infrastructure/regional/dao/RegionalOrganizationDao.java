package net.xianmu.usercenter.infrastructure.regional.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.RegionalOrganizationQueryInput;
import net.xianmu.usercenter.infrastructure.regional.mapper.RegionalOrganizationMapper;
import net.xianmu.usercenter.infrastructure.regional.model.RegionalOrganization;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-06 16:32:07
 */
@Component
public class RegionalOrganizationDao extends ServiceImpl<RegionalOrganizationMapper, RegionalOrganization> {

    /**
     * @param
     * @return
     */
    public List<RegionalOrganization> listByCondition(RegionalOrganizationQueryInput req) {
        LambdaQueryWrapper<RegionalOrganization> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), RegionalOrganization::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), RegionalOrganization::getTenantId, req.getTenantId());
        query.in(CollUtil.isNotEmpty(req.getTenantIds()), RegionalOrganization::getTenantId, req.getTenantIds());
        query.eq(StrUtil.isNotBlank(req.getPhone()), RegionalOrganization::getPhone, req.getPhone());
        query.eq(StrUtil.isNotBlank(req.getOrganizationName()), RegionalOrganization::getOrganizationName, req.getOrganizationName());
        query.eq(Objects.nonNull(req.getSource()), RegionalOrganization::getSource, req.getSource());
        query.eq(Objects.nonNull(req.getSize()), RegionalOrganization::getSize, req.getSize());
        query.eq(Objects.nonNull(req.getStatus()), RegionalOrganization::getStatus, req.getStatus());
        query.eq(Objects.nonNull(req.getAdminId()), RegionalOrganization::getAdminId, req.getAdminId());
        query.eq(StrUtil.isNotBlank(req.getCreator()), RegionalOrganization::getCreator, req.getCreator());
        query.eq(StrUtil.isNotBlank(req.getUpdater()), RegionalOrganization::getUpdater, req.getUpdater());
        return list(query);
    }


    public RegionalOrganization selectByAdminAndTenant(Long adminId, Long tenantId) {
        LambdaQueryWrapper<RegionalOrganization> query = new LambdaQueryWrapper<>();
        query.eq(RegionalOrganization::getTenantId, tenantId);
        query.eq(RegionalOrganization::getAdminId, adminId);
        return getOne(query);
    }


}
