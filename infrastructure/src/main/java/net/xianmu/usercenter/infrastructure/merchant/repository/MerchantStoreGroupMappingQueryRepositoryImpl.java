package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupMappingQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreGroupMappingConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreGroupMappingDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-06-01 15:42:52
* @version 1.0
*
*/
@Repository
public class MerchantStoreGroupMappingQueryRepositoryImpl implements MerchantStoreGroupMappingQueryRepository {

    @Autowired
    private MerchantStoreGroupMappingDao merchantStoreGroupMappingDao;

    @Override
    public Integer countStoreNumByGroupId(Long groupId, Long tenantId) {
        return merchantStoreGroupMappingDao.getBaseMapper().countStoreNumByGroupId(tenantId, groupId);
    }

    @Override
    public List<MerchantStoreGroupMappingEntity> selectStoresByByGroupId(Long groupId, Long tenantId) {
        return MerchantStoreGroupMappingConverter.toMerchantStoreGroupMappingEntityList(merchantStoreGroupMappingDao.selectByByGroupId(groupId, tenantId));
    }

    @Override
    public MerchantStoreGroupMappingEntity selectByStoreId(Long storeId, Long tenantId) {
        return MerchantStoreGroupMappingConverter.toMerchantStoreGroupMappingEntity(merchantStoreGroupMappingDao.selectByStoreId(storeId, tenantId));
    }
}