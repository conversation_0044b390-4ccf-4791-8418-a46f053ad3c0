package net.xianmu.usercenter.infrastructure.merchant.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupQueryRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreGroupConverter;
import net.xianmu.usercenter.infrastructure.merchant.converter.MerchantStoreGroupModelConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.MerchantStoreGroupDao;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-11 10:55:37
* @version 1.0
*
*/
@Repository
public class MerchantStoreGroupQueryRepositoryImpl implements MerchantStoreGroupQueryRepository {

    @Autowired
    private MerchantStoreGroupDao merchantStoreGroupDao;

    @Override
    public List<MerchantStoreGroupEntity> selectByStoreIds(Long tenantId, List<Long> storeIdList) {
        return MerchantStoreGroupModelConverter.toMerchantStoreGroupEntityList(merchantStoreGroupDao.getBaseMapper().queryBatchByStoreIds(tenantId, storeIdList));
    }

    @Override
    public List<MerchantStoreGroupEntity> selectGroupByGroupIds(Long tenantId, List<Long> groupIdList) {
        return MerchantStoreGroupModelConverter.toMerchantStoreGroupEntityList(merchantStoreGroupDao.getBaseMapper().queryBatchByGroupIds(tenantId, groupIdList));
    }

    @Override
    public MerchantStoreGroupEntity selectDefaultGroup(Long tenantId) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupEntity(merchantStoreGroupDao.selectDefaultGroup(tenantId));
    }

    @Override
    public MerchantStoreGroupEntity selectById(Long id) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupEntity(merchantStoreGroupDao.getById(id));
    }

    @Override
    public MerchantStoreGroupEntity selectByName(Long tenantId, String name) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupEntity(merchantStoreGroupDao.selectByName(tenantId, name));
    }

    @Override
    public List<MerchantStoreGroupEntity> queryGroupsWithStoreCount(Long tenantId, String name) {
        return MerchantStoreGroupModelConverter.toMerchantStoreGroupEntityList(merchantStoreGroupDao.getBaseMapper().queryGroupsWithStoreCount(tenantId, name));
    }

    @Override
    public List<MerchantStoreGroupEntity> selectByCondition(MerchantStoreGroupQueryInput input) {
        return MerchantStoreGroupConverter.toMerchantStoreGroupEntityList(merchantStoreGroupDao.selectByCondition(input));
    }

    @Override
    public PageInfo<MerchantStoreGroupEntity> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput, PageQueryInput pageQueryInput) {
        PageHelper.startPage(pageQueryInput.getPageIndex(), pageQueryInput.getPageSize());
        List<MerchantStoreGroup> list = merchantStoreGroupDao.getBaseMapper().getMerchantStoreGroupPage(merchantStoreGroupQueryInput);
        return PageInfoConverter.toPageResp(new PageInfo<>(list), MerchantStoreGroupConverter::toMerchantStoreGroupEntity);
    }
}