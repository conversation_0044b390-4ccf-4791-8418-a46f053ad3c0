package net.xianmu.usercenter.infrastructure.merchant.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.MerchantStoreChangeLogQueryInput;
import net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreChangeLogMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreChangeLog;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-10 17:23:10
 */
@Component
public class MerchantStoreChangeLogDao extends ServiceImpl<MerchantStoreChangeLogMapper, MerchantStoreChangeLog> {

    /**
     * @param
     * @return
     */
    public List<MerchantStoreChangeLog> listByCondition(MerchantStoreChangeLogQueryInput req) {
        LambdaQueryWrapper<MerchantStoreChangeLog> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getId()), MerchantStoreChangeLog::getId, req.getId());
        query.eq(Objects.nonNull(req.getTenantId()), MerchantStoreChangeLog::getTenantId, req.getTenantId());
        query.eq(Objects.nonNull(req.getRegionalId()), MerchantStoreChangeLog::getRegionalId, req.getRegionalId());
        query.eq(Objects.nonNull(req.getStoreId()), MerchantStoreChangeLog::getStoreId, req.getStoreId());
        query.in(CollUtil.isNotEmpty(req.getStoreIdList()), MerchantStoreChangeLog::getStoreId, req.getStoreIdList());
        query.eq(StrUtil.isNotBlank(req.getInviterChannelCode()), MerchantStoreChangeLog::getInviterChannelCode, req.getInviterChannelCode());
        query.eq(StrUtil.isNotBlank(req.getMerchantChannelCode()), MerchantStoreChangeLog::getMerchantChannelCode, req.getMerchantChannelCode());
        query.eq(StrUtil.isNotBlank(req.getOpName()), MerchantStoreChangeLog::getOpName, req.getOpName());
        query.eq(Objects.nonNull(req.getOpType()), MerchantStoreChangeLog::getOpType, req.getOpType());
        query.eq(StrUtil.isNotBlank(req.getOpRemark()), MerchantStoreChangeLog::getOpRemark, req.getOpRemark());
        return list(query);
    }


    public void updateChannelCode(Long id, String inviterChannelCode, String merchantChannelCode) {
        LambdaUpdateWrapper<MerchantStoreChangeLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantStoreChangeLog::getId, id);
        wrapper.set(MerchantStoreChangeLog::getInviterChannelCode, inviterChannelCode);
        wrapper.set(MerchantStoreChangeLog::getMerchantChannelCode, merchantChannelCode);
        update(wrapper);
    }


}
