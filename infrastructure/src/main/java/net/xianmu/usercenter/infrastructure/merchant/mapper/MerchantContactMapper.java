package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@Mapper
public interface MerchantContactMapper extends BaseMapper<MerchantContact> {

    List<MerchantContact> selectByStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId);
}
