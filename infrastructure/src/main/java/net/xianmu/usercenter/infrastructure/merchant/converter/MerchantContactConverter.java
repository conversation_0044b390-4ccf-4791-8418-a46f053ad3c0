package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-29 17:08:24
 * @version 1.0
 *
 */
public class MerchantContactConverter {


    private MerchantContactConverter() {
        // 无需实现
    }

    public static List<MerchantContactEntity> toMerchantContactEntityList(List<MerchantContact> merchantContactList) {
        if (merchantContactList == null) {
            return Collections.emptyList();
        }
        List<MerchantContactEntity> merchantContactEntityList = new ArrayList<>();
        for (MerchantContact merchantContact : merchantContactList) {
            merchantContactEntityList.add(toMerchantContactEntity(merchantContact));
        }
        return merchantContactEntityList;
    }

    public static MerchantContactEntity toMerchantContactEntity(MerchantContact merchantContact) {
        if (merchantContact == null) {
            return null;
        }
        MerchantContactEntity merchantContactEntity = new MerchantContactEntity();
        merchantContactEntity.setId(merchantContact.getId());
        merchantContactEntity.setTenantId(merchantContact.getTenantId());
        merchantContactEntity.setAddressId(merchantContact.getAddressId());
        merchantContactEntity.setName(merchantContact.getName());
        merchantContactEntity.setPhone(merchantContact.getPhone());
        merchantContactEntity.setDefaultFlag(merchantContact.getDefaultFlag());
        merchantContactEntity.setCreateTime(merchantContact.getCreateTime());
        merchantContactEntity.setUpdateTime(merchantContact.getUpdateTime());
        return merchantContactEntity;
    }

    public static List<MerchantContact> toMerchantContactList(List<MerchantContactEntity> merchantContactEntityList) {
        if (merchantContactEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantContact> merchantContactList = new ArrayList<>();
        for (MerchantContactEntity merchantContactEntity : merchantContactEntityList) {
            merchantContactList.add(toMerchantContact(merchantContactEntity));
        }
        return merchantContactList;
    }

    public static MerchantContact toMerchantContact(MerchantContactEntity merchantContactEntity) {
        if (merchantContactEntity == null) {
            return null;
        }
        MerchantContact merchantContact = new MerchantContact();
        merchantContact.setId(merchantContactEntity.getId());
        merchantContact.setTenantId(merchantContactEntity.getTenantId());
        merchantContact.setAddressId(merchantContactEntity.getAddressId());
        merchantContact.setName(merchantContactEntity.getName());
        merchantContact.setPhone(merchantContactEntity.getPhone());
        merchantContact.setDefaultFlag(merchantContactEntity.getDefaultFlag());
        merchantContact.setCreateTime(merchantContactEntity.getCreateTime());
        merchantContact.setUpdateTime(merchantContactEntity.getUpdateTime());
        return merchantContact;
    }
}
