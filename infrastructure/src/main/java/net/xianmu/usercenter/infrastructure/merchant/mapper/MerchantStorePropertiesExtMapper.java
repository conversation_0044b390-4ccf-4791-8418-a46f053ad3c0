package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStorePropertiesExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MerchantStorePropertiesExtMapper extends BaseMapper<MerchantStorePropertiesExt> { ;

    MerchantStorePropertiesExt selectByPrimaryKey(Long id);

    MerchantStorePropertiesExt selectByMidKey(@Param("mId") Long id, @Param("proKey")String key);


    /**
     * 批量查询店铺打印配送单配置信息
     * @param mids 门店ID集合
     * @param key key
     * @return 结果
     */
    List<MerchantStorePropertiesExt> selectByMidsKey(@Param("mids")List<Long> mids, @Param("proKey")String key);

    int insertSelectiveOnDuplicate(MerchantStorePropertiesExt record);

}