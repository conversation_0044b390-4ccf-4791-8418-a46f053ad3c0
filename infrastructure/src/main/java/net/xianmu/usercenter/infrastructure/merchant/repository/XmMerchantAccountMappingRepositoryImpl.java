package net.xianmu.usercenter.infrastructure.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAccountMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAccountMappingRepository;
import net.xianmu.usercenter.infrastructure.merchant.converter.XmMerchantAccountMappingConverter;
import net.xianmu.usercenter.infrastructure.merchant.dao.XmMerchantAccountMappingDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-07-11 16:46:09
* @version 1.0
*
*/
@Repository
public class XmMerchantAccountMappingRepositoryImpl implements XmMerchantAccountMappingRepository {

    @Autowired
    private XmMerchantAccountMappingDao xmMerchantAccountMappingDao;

    @Override
    public XmMerchantAccountMappingEntity selectByXmAccountId(Long xmAccountId) {
        return XmMerchantAccountMappingConverter.toXmMerchantAccountMappingEntity(xmMerchantAccountMappingDao.selectByXmAccountId(xmAccountId));
    }

    @Override
    public void createOrUpdate(XmMerchantAccountMappingEntity entity) {
        xmMerchantAccountMappingDao.saveOrUpdate(XmMerchantAccountMappingConverter.toXmMerchantAccountMapping(entity));
    }
}