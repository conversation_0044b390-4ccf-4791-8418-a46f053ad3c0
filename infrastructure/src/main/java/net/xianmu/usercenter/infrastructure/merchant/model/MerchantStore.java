package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
@Data
public class MerchantStore {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;


	/**
	 * 门店名称
	 */
	private String storeName;

	/**
	 * 门店类型：0-直营店 1-加盟店 2-托管店 3-个人店 4-连锁 5-未知
	 */
	private Integer type;

	/**
	 * 注册时间
	 */
	private LocalDateTime registerTime;

	/**
	 * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店4、拉黑
	 */
	private Integer status;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 账期开关 1开启 0关闭
	 */
	private Integer billSwitch;

	/**
	 * 在线支付1开启0关闭
	 */
	private Integer onlinePayment;

	/**
	 * 门店编号
	 */
	private String storeNo;

	/**
	 * 余额权限 0、关闭 1、开启
	 */
	private Integer balanceAuthority;

	/**
	 * 门店下单有效期
	 * 1=短期
	 * 0=长期
	 */
	private Integer placeOrderPermissionTimeLimited;
	/**
	 * 门店下单失效日期
	 */
	private LocalDateTime placeOrderPermissionExpiryTime;

	/**
	 * 线下支付权限1=开启;0=关闭
	 */
	private Integer enableOfflinePayment;

	/**
	 * 非现金支付权限 0、关闭 1开启
	 */
	private Integer nonCashAuthority;

	// --------------------鲜沐数据迁移------------------

	/**
	 * 鲜沐merchant表id
	 */
	private Long mId;

	/**
	 * 门店经营类型
	 */
	private String businessType;

	/**
	 * 区域组织id
	 */
	private Long regionalId;

	/**
	 * 门店自己的邀请码，可用于邀请门店、子账号
	 */
	private String channelCode;

	/**
	 * 运营服务区域
	 */
	private Integer areaNo;


	
}