package net.xianmu.usercenter.infrastructure.businessInfo.model;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-08 17:01:58
 * @version 1.0
 *
 */
@Data
public class BusinessInformation {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 用户id
	 */
	private Long bizId;

	/**
	 * 类型：0-品牌用户，1-单店用户
	 */
	private Integer type;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 联系人名称
	 */
	private String contactName;

	/**
	 * 信用代码
	 */
	private String creditCode;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 区
	 */
	private String area;

	/**
	 * 联系地址
	 */
	private String address;

	/**
	 * 公司联系手机号
	 */
	private String phone;

	/**
	 * 营业执照
	 */
	private String businessLicense;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


	public void warp(BusinessInformationCommandInput businessInformationCommandInput) {
		if (businessInformationCommandInput == null) {
			return ;
		}
		this.setTenantId(businessInformationCommandInput.getTenantId());
		this.setBizId(businessInformationCommandInput.getBizId());
		this.setType(businessInformationCommandInput.getType());
		this.setCompanyName(businessInformationCommandInput.getCompanyName());
		this.setCreditCode(businessInformationCommandInput.getCreditCode());
		this.setProvince(businessInformationCommandInput.getProvince());
		this.setCity(businessInformationCommandInput.getCity());
		this.setArea(businessInformationCommandInput.getArea());
		this.setAddress(businessInformationCommandInput.getAddress());
		this.setBusinessLicense(businessInformationCommandInput.getBusinessLicense());
		this.setPhone(getCompletePhone(businessInformationCommandInput));
		this.setContactName(businessInformationCommandInput.getContactName());
	}


	private String getCompletePhone(BusinessInformationCommandInput input) {
		String areaPhone = input.getCompanyAreaPhone();
		String phone = input.getCompanyPhone();
		return StrUtil.isBlank(areaPhone) ? phone : areaPhone + "-" + phone;
	}
	
}