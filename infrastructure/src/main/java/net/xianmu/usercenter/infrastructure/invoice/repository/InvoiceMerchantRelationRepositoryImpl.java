package net.xianmu.usercenter.infrastructure.invoice.repository;

import net.xianmu.usercenter.domain.invoice.entity.InvoiceConfigEntity;
import net.xianmu.usercenter.domain.invoice.entity.InvoiceMerchantRelationEntity;
import net.xianmu.usercenter.domain.invoice.repository.InvoiceMerchantRelationRepository;
import net.xianmu.usercenter.infrastructure.invoice.converter.InvoiceMerchantRelationConverter;
import net.xianmu.usercenter.infrastructure.invoice.dao.InvoiceConfigDao;
import net.xianmu.usercenter.infrastructure.invoice.dao.InvoiceMerchantRelationDao;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-07-13 13:58:41
* @version 1.0
*
*/
//@Repository
public class InvoiceMerchantRelationRepositoryImpl implements InvoiceMerchantRelationRepository {

    private InvoiceMerchantRelationDao invoiceMerchantRelationDao;

    @Override
    public InvoiceMerchantRelationEntity selectById(Long id) {
        // return InvoiceMerchantRelationConverter.toInvoiceMerchantRelationEntity(invoiceMerchantRelationDao.getById(id));
        return null;
    }

    @Override
    public void createOrUpdate(InvoiceMerchantRelationEntity entity) {
        // invoiceMerchantRelationDao.saveOrUpdate(InvoiceMerchantRelationConverter.toInvoiceMerchantRelation(entity));
    }
}