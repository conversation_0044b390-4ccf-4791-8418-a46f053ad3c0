package net.xianmu.usercenter.infrastructure.invoice.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-13 13:58:41
 * @version 1.0
 *
 */
@Data
public class InvoiceMerchantRelation {
	/**
	 * 自增长主键
	 */
	//@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 取自invoice_config表内
	 */
	private Long invoiceId;

	/**
	 * 取自merchant_store表中store_id
	 */
	private Long storeId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建关联关系提交时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改人
	 */
	private String updater;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 状态 0 存在 1 作废
	 */
	private Integer status;

	
}