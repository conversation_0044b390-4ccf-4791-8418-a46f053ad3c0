package net.xianmu.usercenter.infrastructure.tenant.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:39
 */

@Data
public class TenantAccount {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部用户系统用户id
     */
    private Long authUserId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 状态0有效1失效
     */
    private Integer status;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 删除标识0 有效1已删除
     */
    private Integer deletedFlag;

    /**
     * email
     */
    private String email;

    /**
     * 操作人
     */
    private String updater;

}
