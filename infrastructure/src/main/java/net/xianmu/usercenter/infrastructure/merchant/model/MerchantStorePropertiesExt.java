package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-08-30 14:08:25
 * @version 1.0
 *
 */
@Data
public class MerchantStorePropertiesExt {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * mid
	 */
	private Long mId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * key
	 */
	private String proKey;

	/**
	 * value
	 */
	private String proValue;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


	

	
}