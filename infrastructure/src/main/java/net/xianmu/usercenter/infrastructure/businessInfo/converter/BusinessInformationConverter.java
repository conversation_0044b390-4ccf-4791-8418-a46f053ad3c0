package net.xianmu.usercenter.infrastructure.businessInfo.converter;

import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.infrastructure.businessInfo.model.BusinessInformation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 17:01:58
 * @version 1.0
 *
 */
public class BusinessInformationConverter {


    private BusinessInformationConverter() {
        // 无需实现
    }

    public static List<BusinessInformationEntity> toBusinessInformationEntityList(List<BusinessInformation> businessInformationList) {
        if (businessInformationList == null) {
            return Collections.emptyList();
        }
        List<BusinessInformationEntity> businessInformationEntityList = new ArrayList<>();
        for (BusinessInformation businessInformation : businessInformationList) {
            businessInformationEntityList.add(toBusinessInformationEntity(businessInformation));
        }
        return businessInformationEntityList;
    }

    public static BusinessInformationEntity toBusinessInformationEntity(BusinessInformation businessInformation) {
        if (businessInformation == null) {
            return null;
        }
        BusinessInformationEntity businessInformationEntity = new BusinessInformationEntity();
        businessInformationEntity.setId(businessInformation.getId());
        businessInformationEntity.setTenantId(businessInformation.getTenantId());
        businessInformationEntity.setBizId(businessInformation.getBizId());
        businessInformationEntity.setType(businessInformation.getType());
        businessInformationEntity.setCompanyName(businessInformation.getCompanyName());
        businessInformationEntity.setCreditCode(businessInformation.getCreditCode());
        businessInformationEntity.setProvince(businessInformation.getProvince());
        businessInformationEntity.setCity(businessInformation.getCity());
        businessInformationEntity.setArea(businessInformation.getArea());
        businessInformationEntity.setAddress(businessInformation.getAddress());
        businessInformationEntity.setPhone(businessInformation.getPhone());
        businessInformationEntity.setBusinessLicense(businessInformation.getBusinessLicense());
        businessInformationEntity.setCreateTime(businessInformation.getCreateTime());
        businessInformationEntity.setUpdateTime(businessInformation.getUpdateTime());
        businessInformationEntity.setContactName(businessInformation.getContactName());
        return businessInformationEntity;
    }


    public static List<BusinessInformation> toBusinessInformationList(List<BusinessInformationEntity> businessInformationEntityList) {
        if (businessInformationEntityList == null) {
            return Collections.emptyList();
        }
        List<BusinessInformation> businessInformationList = new ArrayList<>();
        for (BusinessInformationEntity businessInformationEntity : businessInformationEntityList) {
            businessInformationList.add(toBusinessInformation(businessInformationEntity));
        }
        return businessInformationList;
    }

    public static BusinessInformation toBusinessInformation(BusinessInformationEntity businessInformationEntity) {
        if (businessInformationEntity == null) {
            return null;
        }
        BusinessInformation businessInformation = new BusinessInformation();
        businessInformation.setId(businessInformationEntity.getId());
        businessInformation.setTenantId(businessInformationEntity.getTenantId());
        businessInformation.setBizId(businessInformationEntity.getBizId());
        businessInformation.setType(businessInformationEntity.getType());
        businessInformation.setCompanyName(businessInformationEntity.getCompanyName());
        businessInformation.setCreditCode(businessInformationEntity.getCreditCode());
        businessInformation.setProvince(businessInformationEntity.getProvince());
        businessInformation.setCity(businessInformationEntity.getCity());
        businessInformation.setArea(businessInformationEntity.getArea());
        businessInformation.setAddress(businessInformationEntity.getAddress());
        businessInformation.setPhone(businessInformationEntity.getPhone());
        businessInformation.setBusinessLicense(businessInformationEntity.getBusinessLicense());
        businessInformation.setCreateTime(businessInformationEntity.getCreateTime());
        businessInformation.setUpdateTime(businessInformationEntity.getUpdateTime());
        businessInformation.setContactName(businessInformationEntity.getContactName());
        return businessInformation;
    }
}
