package net.xianmu.usercenter.infrastructure.merchant.converter;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreExt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-08-30 14:08:25
 * @version 1.0
 *
 */
public class MerchantStoreExtConverter {


    private MerchantStoreExtConverter() {
        // 无需实现
    }

    public static List<MerchantStoreExtEntity> toMerchantStoreExtEntityList(List<MerchantStoreExt> merchantStoreExtList) {
        if (merchantStoreExtList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreExtEntity> merchantStoreExtEntityList = new ArrayList<>();
        for (MerchantStoreExt merchantStoreExt : merchantStoreExtList) {
            merchantStoreExtEntityList.add(toMerchantStoreExtEntity(merchantStoreExt));
        }
        return merchantStoreExtEntityList;
    }

    public static MerchantStoreExtEntity toMerchantStoreExtEntity(MerchantStoreExt merchantStoreExt) {
        if (merchantStoreExt == null) {
            return null;
        }
        MerchantStoreExtEntity merchantStoreExtEntity = new MerchantStoreExtEntity();
        merchantStoreExtEntity.setId(merchantStoreExt.getId());
        merchantStoreExtEntity.setTenantId(merchantStoreExt.getTenantId());
        merchantStoreExtEntity.setStoreId(merchantStoreExt.getStoreId());
        merchantStoreExtEntity.setPopView(merchantStoreExt.getPopView());
        merchantStoreExtEntity.setChangePop(merchantStoreExt.getChangePop());
        merchantStoreExtEntity.setFirstLoginPop(merchantStoreExt.getFirstLoginPop());
        merchantStoreExtEntity.setDisplayButton(merchantStoreExt.getDisplayButton());
        merchantStoreExtEntity.setPreRegisterFlag(merchantStoreExt.getPreRegisterFlag());
        merchantStoreExtEntity.setMockLoginFlag(merchantStoreExt.getMockLoginFlag());
        merchantStoreExtEntity.setProvince(merchantStoreExt.getProvince());
        merchantStoreExtEntity.setCity(merchantStoreExt.getCity());
        merchantStoreExtEntity.setArea(merchantStoreExt.getArea());
        merchantStoreExtEntity.setPoiNote(merchantStoreExt.getPoiNote());
        merchantStoreExtEntity.setCreator(merchantStoreExt.getCreator());
        merchantStoreExtEntity.setCreateTime(merchantStoreExt.getCreateTime());
        merchantStoreExtEntity.setUpdateTime(merchantStoreExt.getUpdateTime());
        merchantStoreExtEntity.setUpdater(merchantStoreExt.getUpdater());
        merchantStoreExtEntity.setOperateStatus(merchantStoreExt.getOperateStatus());
        merchantStoreExtEntity.setBusinessLine(merchantStoreExt.getBusinessLine());
        return merchantStoreExtEntity;
    }

    public static List<MerchantStoreExt> toMerchantStoreExtList(List<MerchantStoreExtEntity> merchantStoreExtEntityList) {
        if (merchantStoreExtEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreExt> merchantStoreExtList = new ArrayList<>();
        for (MerchantStoreExtEntity merchantStoreExtEntity : merchantStoreExtEntityList) {
            merchantStoreExtList.add(toMerchantStoreExt(merchantStoreExtEntity));
        }
        return merchantStoreExtList;
    }

    public static MerchantStoreExt toMerchantStoreExt(MerchantStoreExtEntity merchantStoreExtEntity) {
        if (merchantStoreExtEntity == null) {
            return null;
        }
        MerchantStoreExt merchantStoreExt = new MerchantStoreExt();
        merchantStoreExt.setId(merchantStoreExtEntity.getId());
        merchantStoreExt.setTenantId(merchantStoreExtEntity.getTenantId());
        merchantStoreExt.setStoreId(merchantStoreExtEntity.getStoreId());
        merchantStoreExt.setPopView(merchantStoreExtEntity.getPopView());
        merchantStoreExt.setChangePop(merchantStoreExtEntity.getChangePop());
        merchantStoreExt.setFirstLoginPop(merchantStoreExtEntity.getFirstLoginPop());
        merchantStoreExt.setDisplayButton(merchantStoreExtEntity.getDisplayButton());
        merchantStoreExt.setPreRegisterFlag(merchantStoreExtEntity.getPreRegisterFlag());
        merchantStoreExt.setMockLoginFlag(merchantStoreExtEntity.getMockLoginFlag());
        merchantStoreExt.setProvince(merchantStoreExtEntity.getProvince());
        merchantStoreExt.setCity(merchantStoreExtEntity.getCity());
        merchantStoreExt.setArea(merchantStoreExtEntity.getArea());
        merchantStoreExt.setPoiNote(merchantStoreExtEntity.getPoiNote());
        merchantStoreExt.setCreator(merchantStoreExtEntity.getCreator());
        merchantStoreExt.setCreateTime(merchantStoreExtEntity.getCreateTime());
        merchantStoreExt.setUpdateTime(merchantStoreExtEntity.getUpdateTime());
        merchantStoreExt.setUpdater(merchantStoreExtEntity.getUpdater());
        merchantStoreExt.setOperateStatus(merchantStoreExtEntity.getOperateStatus());
        merchantStoreExt.setBusinessLine(merchantStoreExtEntity.getBusinessLine());
        return merchantStoreExt;
    }
}
