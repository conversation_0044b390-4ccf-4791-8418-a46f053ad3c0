package net.xianmu.usercenter.infrastructure.tenant.repository;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.common.util.PageInfoConverter;
import net.xianmu.usercenter.domain.tenant.repository.TenantQueryRepository;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.infrastructure.tenant.converter.TenantConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.TenantDao;
import net.xianmu.usercenter.infrastructure.tenant.model.Tenant;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-05 11:24:18
* @version 1.0
*
*/
@Repository
@Slf4j
public class TenantQueryRepositoryImpl implements TenantQueryRepository {

    @Autowired
    private TenantDao tenantDao;

    @Override
    public TenantEntity getTenantById(Long id) {
        Tenant tenant = tenantDao.getById(id);
        return TenantConverter.toTenantEntity(tenant);
    }

    @Override
    public TenantEntity getTenantByAdminId(Long adminId) {
        List<TenantEntity> tenants = this.getTenants(TenantQueryInput.builder().adminId(adminId).build());
        return CollUtil.isEmpty(tenants) ? null : tenants.get(0);
    }

    @Override
    public List<TenantEntity> getTenantsByIds(List<Long> idList) {
        List<Tenant> tenants = tenantDao.getBaseMapper().selectBatchIds(idList);
        return TenantConverter.toTenantEntityList(tenants);
    }

    @Override
    public PageInfo<TenantAndBusinessEntity> getTenantsPage(TenantQueryInput req, PageQueryInput pageQueryInput) {
        PageHelper.startPage(pageQueryInput.getPageIndex(), pageQueryInput.getPageSize());
        List<TenantAndBusinessModel> tenants = tenantDao.getBaseMapper().getTenantsPage(req);
        return PageInfoConverter.toPageResp(new PageInfo<>(tenants), TenantConverter::toTenantAndBusinessEntity);
    }

    @Override
    public List<TenantEntity> getTenants(TenantQueryInput req) {
        final List<Tenant> tenants = tenantDao.getBaseMapper().selectTenantsByCondition(req);
        return TenantConverter.toTenantEntityList(tenants);
    }

    @Override
    public List<TenantEntity> getTenantsNonFuzzy(TenantQueryInput req) {
        final List<Tenant> tenants = tenantDao.getBaseMapper().selectTenantsByConditionNonFuzzy(req);
        return TenantConverter.toTenantEntityList(tenants);
    }

    @Override
    public List<TenantAndBusinessEntity> getTenantAndCompanyList(TenantQueryInput req) {
        final List<TenantAndBusinessModel> tenantAndBusinessModels = tenantDao.getBaseMapper().selectTenantAndBusinessModelsByCondition(req);
        return TenantConverter.toTenantAndBusinessEntityList(tenantAndBusinessModels);
    }

    @Override
    public List<TenantAndBusinessEntity> getTenantAndCompanyByIds(List<Long> idList) {
        final List<TenantAndBusinessModel> tenantAndBusinessModels = tenantDao.getBaseMapper().selectTenantAndBusinessModelsByIds(idList);
        return TenantConverter.toTenantAndBusinessEntityList(tenantAndBusinessModels);
    }
}