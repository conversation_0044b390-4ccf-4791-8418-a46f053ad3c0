package net.xianmu.usercenter.infrastructure.tenant.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;
import net.xianmu.usercenter.common.enums.TenantAccountEnums;
import net.xianmu.usercenter.infrastructure.tenant.mapper.TenantAccountMapper;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantAccount;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 14:19
 */
@Component
public class TenantAccountDao extends ServiceImpl<TenantAccountMapper, TenantAccount> {

    public TenantAccount selectByAuthUserId(Long authUserId) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(TenantAccount::getAuthUserId, authUserId);
        return getOne(query);
    }


    public List<TenantAccount> selectByAuthUserIdList(List<Long> authUserIdList) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.in(TenantAccount::getAuthUserId, authUserIdList);
        return list(query);
    }


    /**
     * @param
     * @return
     */
    public List<TenantAccount> listByCondition(TenantAccountListQueryInput req) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(req.getTenantId()), TenantAccount::getTenantId, req.getTenantId());
        query.like(!StringUtils.isEmpty(req.getPhone()), TenantAccount::getPhone, req.getPhone());
        query.like(!StringUtils.isEmpty(req.getEmail()), TenantAccount::getEmail, req.getEmail());
        query.like(!StringUtils.isEmpty(req.getNickName()), TenantAccount::getNickname, req.getNickName());
        query.eq(Objects.nonNull(req.getStatus()), TenantAccount::getStatus, req.getStatus());
        query.in(CollUtil.isNotEmpty(req.getAuthUserIds()), TenantAccount::getAuthUserId, req.getAuthUserIds());
        query.in(CollUtil.isNotEmpty(req.getIdList()), TenantAccount::getId, req.getIdList());
        query.eq(Objects.nonNull(req.getNickId()), TenantAccount::getId, req.getNickId());
        query.eq(Objects.nonNull(req.getDeletedFlag()), TenantAccount::getDeletedFlag, req.getDeletedFlag());
        return list(query);
    }


    public TenantAccount selectByPhoneAndTenantId(Long tenantId, String phone) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(TenantAccount::getPhone, phone);
        query.eq(TenantAccount::getTenantId, tenantId);
        return getOne(query);
    }


    public TenantAccount selectByEmailAndTenantId(Long tenantId, String email) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(TenantAccount::getEmail, email);
        query.eq(TenantAccount::getTenantId, tenantId);
        return getOne(query);
    }


    public List<TenantAccount> selectTenantAccountByTenantIdsAndPhone(String phone, List<Long> tenantIds) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(TenantAccount::getPhone, phone);
        query.eq(TenantAccount::getStatus, TenantAccountEnums.status.EFFECTIVE.getCode());
        query.in(!CollectionUtils.isEmpty(tenantIds), TenantAccount::getTenantId, tenantIds);
        return getBaseMapper().selectList(query);
    }


    public void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccount entity) {
        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantAccount::getPhone, entity.getPhone());
        updateWrapper.in(TenantAccount::getTenantId, tenantIds);
        updateWrapper.set(!StringUtils.isEmpty(entity.getNickname()), TenantAccount::getNickname, entity.getNickname());
        updateWrapper.set(!StringUtils.isEmpty(entity.getProfilePicture()), TenantAccount::getProfilePicture, entity.getProfilePicture());
        updateWrapper.set(Objects.nonNull(entity.getStatus()), TenantAccount::getStatus, entity.getStatus());
        updateWrapper.set(TenantAccount::getOperatorPhone, entity.getOperatorPhone());
        updateWrapper.set(TenantAccount::getOperatorTime, entity.getOperatorTime());
        update(updateWrapper);
    }



    public void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccount entity) {
        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantAccount::getAuthUserId, entity.getAuthUserId());
        updateWrapper.in(TenantAccount::getTenantId, tenantIds);
        updateWrapper.set(!StringUtils.isEmpty(entity.getNickname()), TenantAccount::getNickname, entity.getNickname());
        updateWrapper.set(!StringUtils.isEmpty(entity.getProfilePicture()), TenantAccount::getProfilePicture, entity.getProfilePicture());
        updateWrapper.set(Objects.nonNull(entity.getStatus()), TenantAccount::getStatus, entity.getStatus());
        updateWrapper.set(TenantAccount::getOperatorPhone, entity.getOperatorPhone());
        updateWrapper.set(TenantAccount::getOperatorTime, entity.getOperatorTime());
        updateWrapper.set(TenantAccount::getUpdater, entity.getUpdater());
        update(updateWrapper);
    }


    /**
     * @param
     * @return
     */
    public List<TenantAccount> pageByCondition(TenantAccountListQueryInput req) {
        LambdaQueryWrapper<TenantAccount> query = new LambdaQueryWrapper<>();
        query.eq(TenantAccount::getTenantId, req.getTenantId());
        query.like(!StringUtils.isEmpty(req.getPhone()), TenantAccount::getPhone, req.getPhone());
        query.like(!StringUtils.isEmpty(req.getNickName()), TenantAccount::getNickname, req.getNickName());
        query.eq(Objects.nonNull(req.getStatus()), TenantAccount::getStatus, req.getStatus());
        query.in(CollUtil.isNotEmpty(req.getAuthUserIds()), TenantAccount::getAuthUserId, req.getAuthUserIds());
        query.eq(Objects.nonNull(req.getNickId()), TenantAccount::getId, req.getNickId());
        query.eq(TenantAccount::getDeletedFlag, TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getUsername())) {
            query.and(wrapper -> wrapper
                    .like(TenantAccount::getPhone, req.getUsername())
                    .or()
                    .like(TenantAccount::getEmail, req.getUsername())
            );
        }
        return list(query);
    }


    public void remove(Long id) {
        LambdaUpdateWrapper<TenantAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantAccount::getId, id);
        updateWrapper.set(TenantAccount::getDeletedFlag, TenantAccountEnums.DeletedFlag.FAILURE.getCode());
        updateWrapper.set(TenantAccount::getStatus, TenantAccountEnums.status.FAILURE.getCode());
        update(updateWrapper);
    }
}
