package net.xianmu.usercenter.infrastructure.regional.repository;

import net.xianmu.usercenter.common.input.query.RegionalOrganizationQueryInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import net.xianmu.usercenter.infrastructure.regional.converter.RegionalOrganizationConverter;
import net.xianmu.usercenter.infrastructure.regional.dao.RegionalOrganizationDao;
import net.xianmu.usercenter.infrastructure.regional.model.RegionalOrganization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:32:07
* @version 1.0
*
*/
@Repository
public class RegionalOrganizationQueryRepositoryImpl implements RegionalOrganizationQueryRepository {

    @Autowired
    private RegionalOrganizationDao regionalOrganizationDao;


    @Override
    public List<RegionalOrganizationEntity> selectByCondition(RegionalOrganizationQueryInput req) {
       return RegionalOrganizationConverter.toRegionalOrganizationEntityList(regionalOrganizationDao.listByCondition(req));
    }

    @Override
    public RegionalOrganizationEntity selectByAdminAndTenant(Long adminId, Long tenantId) {
        return RegionalOrganizationConverter.toRegionalOrganizationEntity(regionalOrganizationDao.selectByAdminAndTenant(adminId, tenantId));
    }

}