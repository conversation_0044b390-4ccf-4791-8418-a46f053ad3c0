package net.xianmu.usercenter.infrastructure.invoice.converter;

import net.xianmu.usercenter.domain.invoice.entity.InvoiceConfigEntity;
import net.xianmu.usercenter.infrastructure.invoice.model.InvoiceConfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-07-13 13:58:41
 * @version 1.0
 *
 */
public class InvoiceConfigConverter {


    private InvoiceConfigConverter() {
        // 无需实现
    }

    public static List<InvoiceConfigEntity> toInvoiceConfigEntityList(List<InvoiceConfig> invoiceConfigList) {
        if (invoiceConfigList == null) {
            return Collections.emptyList();
        }
        List<InvoiceConfigEntity> invoiceConfigEntityList = new ArrayList<>();
        for (InvoiceConfig invoiceConfig : invoiceConfigList) {
            invoiceConfigEntityList.add(toInvoiceConfigEntity(invoiceConfig));
        }
        return invoiceConfigEntityList;
    }

    public static InvoiceConfigEntity toInvoiceConfigEntity(InvoiceConfig invoiceConfig) {
        if (invoiceConfig == null) {
            return null;
        }
        InvoiceConfigEntity invoiceConfigEntity = new InvoiceConfigEntity();
        invoiceConfigEntity.setId(invoiceConfig.getId());
        invoiceConfigEntity.setTenantId(invoiceConfig.getTenantId());
        invoiceConfigEntity.setBizId(invoiceConfig.getBizId());
        invoiceConfigEntity.setType(invoiceConfig.getType());
        invoiceConfigEntity.setInvoiceTitle(invoiceConfig.getInvoiceTitle());
        invoiceConfigEntity.setTaxNumber(invoiceConfig.getTaxNumber());
        invoiceConfigEntity.setOpenAccount(invoiceConfig.getOpenAccount());
        invoiceConfigEntity.setOpenBank(invoiceConfig.getOpenBank());
        invoiceConfigEntity.setCompanyAddress(invoiceConfig.getCompanyAddress());
        invoiceConfigEntity.setCompanyPhone(invoiceConfig.getCompanyPhone());
        invoiceConfigEntity.setMailAddress(invoiceConfig.getMailAddress());
        invoiceConfigEntity.setCompanyReceiver(invoiceConfig.getCompanyReceiver());
        invoiceConfigEntity.setCompanyEmail(invoiceConfig.getCompanyEmail());
        invoiceConfigEntity.setValidStatus(invoiceConfig.getValidStatus());
        invoiceConfigEntity.setUpdateTime(invoiceConfig.getUpdateTime());
        invoiceConfigEntity.setCreateTime(invoiceConfig.getCreateTime());
        invoiceConfigEntity.setLinkMethod(invoiceConfig.getLinkMethod());
        invoiceConfigEntity.setBusinessLicenseAddress(invoiceConfig.getBusinessLicenseAddress());
        invoiceConfigEntity.setCreator(invoiceConfig.getCreator());
        invoiceConfigEntity.setUpdater(invoiceConfig.getUpdater());
        return invoiceConfigEntity;
    }

    public static List<InvoiceConfig> toInvoiceConfigList(List<InvoiceConfigEntity> invoiceConfigEntityList) {
        if (invoiceConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<InvoiceConfig> invoiceConfigList = new ArrayList<>();
        for (InvoiceConfigEntity invoiceConfigEntity : invoiceConfigEntityList) {
            invoiceConfigList.add(toInvoiceConfig(invoiceConfigEntity));
        }
        return invoiceConfigList;
    }

    public static InvoiceConfig toInvoiceConfig(InvoiceConfigEntity invoiceConfigEntity) {
        if (invoiceConfigEntity == null) {
            return null;
        }
        InvoiceConfig invoiceConfig = new InvoiceConfig();
        invoiceConfig.setId(invoiceConfigEntity.getId());
        invoiceConfig.setTenantId(invoiceConfigEntity.getTenantId());
        invoiceConfig.setBizId(invoiceConfigEntity.getBizId());
        invoiceConfig.setType(invoiceConfigEntity.getType());
        invoiceConfig.setInvoiceTitle(invoiceConfigEntity.getInvoiceTitle());
        invoiceConfig.setTaxNumber(invoiceConfigEntity.getTaxNumber());
        invoiceConfig.setOpenAccount(invoiceConfigEntity.getOpenAccount());
        invoiceConfig.setOpenBank(invoiceConfigEntity.getOpenBank());
        invoiceConfig.setCompanyAddress(invoiceConfigEntity.getCompanyAddress());
        invoiceConfig.setCompanyPhone(invoiceConfigEntity.getCompanyPhone());
        invoiceConfig.setMailAddress(invoiceConfigEntity.getMailAddress());
        invoiceConfig.setCompanyReceiver(invoiceConfigEntity.getCompanyReceiver());
        invoiceConfig.setCompanyEmail(invoiceConfigEntity.getCompanyEmail());
        invoiceConfig.setValidStatus(invoiceConfigEntity.getValidStatus());
        invoiceConfig.setUpdateTime(invoiceConfigEntity.getUpdateTime());
        invoiceConfig.setCreateTime(invoiceConfigEntity.getCreateTime());
        invoiceConfig.setLinkMethod(invoiceConfigEntity.getLinkMethod());
        invoiceConfig.setBusinessLicenseAddress(invoiceConfigEntity.getBusinessLicenseAddress());
        invoiceConfig.setCreator(invoiceConfigEntity.getCreator());
        invoiceConfig.setUpdater(invoiceConfigEntity.getUpdater());
        return invoiceConfig;
    }
}
