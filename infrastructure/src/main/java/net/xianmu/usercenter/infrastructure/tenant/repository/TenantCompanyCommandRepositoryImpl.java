package net.xianmu.usercenter.infrastructure.tenant.repository;

import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.tenant.repository.TenantCompanyCommandRepository;
import net.xianmu.usercenter.infrastructure.tenant.dao.TenantCompanyDao;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantCompany;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-05-23 16:16:29
* @version 1.0
*
*/

@Repository
public class TenantCompanyCommandRepositoryImpl implements TenantCompanyCommandRepository {

    @Autowired
    private TenantCompanyDao dao;

    @Override
    public void insert(BusinessInformationCommandInput input) {
        TenantCompany tenantCompany = new TenantCompany();
        tenantCompany.warp(input);
        dao.save(tenantCompany);
    }

    @Override
    public void update(BusinessInformationCommandInput input) {
        TenantCompany tenantCompany = dao.selectByTenantId(input.getTenantId());
        tenantCompany.warp(input);
        dao.saveOrUpdate(tenantCompany);
    }
}