package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreGroupModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
@Mapper
public interface MerchantStoreGroupMapper extends BaseMapper<MerchantStoreGroup> {


    /**
     * 根据门店id列表查询分组信息
     * tenantId 用于数据隔离，可为空。为空时不做条件筛选
     * @param
     * @return
     */
    List<MerchantStoreGroupModel> queryBatchByStoreIds(@Param("tenantId") Long tenantId, @Param("storeIds") List<Long> storeIds);

    List<MerchantStoreGroupModel> queryBatchByGroupIds(@Param("tenantId") Long tenantId, @Param("groupIds") List<Long> groupIds);

    List<MerchantStoreGroupModel> queryGroupsWithStoreCount(@Param("tenantId") Long tenantId, @Param("name") String name);

    List<MerchantStoreGroup> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput input);

}
