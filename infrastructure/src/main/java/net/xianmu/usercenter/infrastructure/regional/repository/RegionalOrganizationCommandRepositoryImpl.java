package net.xianmu.usercenter.infrastructure.regional.repository;

import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationCommandRepository;
import net.xianmu.usercenter.infrastructure.regional.converter.RegionalOrganizationConverter;
import net.xianmu.usercenter.infrastructure.regional.dao.RegionalOrganizationDao;
import net.xianmu.usercenter.infrastructure.regional.model.RegionalOrganization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:32:07
* @version 1.0
*
*/
@Repository
public class RegionalOrganizationCommandRepositoryImpl implements RegionalOrganizationCommandRepository {

    @Autowired
    private RegionalOrganizationDao regionalOrganizationDao;

    @Override
    public RegionalOrganizationEntity createOrUpdate(RegionalOrganizationEntity entity) {
        RegionalOrganization regionalOrganization = RegionalOrganizationConverter.toRegionalOrganization(entity);
        regionalOrganizationDao.saveOrUpdate(regionalOrganization);
        return RegionalOrganizationConverter.toRegionalOrganizationEntity(regionalOrganization);
    }
}