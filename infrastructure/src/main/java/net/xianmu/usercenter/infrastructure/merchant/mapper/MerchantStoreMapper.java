package net.xianmu.usercenter.infrastructure.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore;
import net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-05-08 14:08:17
 * @version 1.0
 *
 */
@Mapper
public interface MerchantStoreMapper extends BaseMapper<MerchantStore> {

    List<MerchantStore> selectByAccountCondition(MerchantStoreQueryInput req);

    List<MerchantStoreModel> selectMerchantStoreAndAddressPage(MerchantStorePageQueryInput req);

    List<MerchantStoreModel> selectMerchantStorePage(MerchantStorePageQueryInput req);

    List<MerchantStoreEntity> selectMerchantStoreAndExtends(MerchantStoreQueryInput req);

    List<Long> selectMIdListByCondition(MerchantStoreQueryInput req);

    List<MerchantStoreEntity> selectMerchantStorePageForXM(MerchantStorePageQueryInput req);

    List<Long> selectStoreIdForXmPage(MerchantStorePageQueryInput req);

    List<MerchantStoreModel> selectAccountPage(MerchantStoreAccountQueryInput req);

    List<MerchantStore> selectNoPullBlackRecordMerchant();

}
