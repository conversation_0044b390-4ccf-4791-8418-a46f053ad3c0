package net.xianmu.usercenter.infrastructure.merchant.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-06-01 15:42:52
 * @version 1.0
 *
 */
@Data
public class MerchantStoreGroupMapping {
	/**
	 * 主键Id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户Id
	 */
	private Long tenantId;

	/**
	 * 门店Id
	 */
	private Long storeId;

	/**
	 * 租户Id
	 */
	private Long groupId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	
}