package net.xianmu.usercenter.infrastructure.regional.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-06 16:46:41
 * @version 1.0
 *
 */
@Data
public class RegionalOrganizationAccount {
	/**
	 * 主键、自增
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 区域id
	 */
	private Long regionalId;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 账户名称
	 */
	private String name;

	/**
	 * 账户状态： 0正常 1禁用
	 */
	private Integer status;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 操作人名称
	 */
	private String updater;

	

	
}