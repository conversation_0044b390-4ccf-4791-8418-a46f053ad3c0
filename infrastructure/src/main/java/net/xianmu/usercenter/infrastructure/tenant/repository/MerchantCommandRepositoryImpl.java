package net.xianmu.usercenter.infrastructure.tenant.repository;

import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantCommandRepository;
import net.xianmu.usercenter.infrastructure.tenant.converter.MerchantConverter;
import net.xianmu.usercenter.infrastructure.tenant.dao.MerchantDao;
import net.xianmu.usercenter.infrastructure.tenant.model.Merchant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-05-11 10:44:50
* @version 1.0
*
*/
@Repository
public class MerchantCommandRepositoryImpl implements MerchantCommandRepository {

    @Autowired
    private MerchantDao merchantDao;
    @Override
    public MerchantEntity createOrUpdate(MerchantEntity entity) {
        Merchant merchant = MerchantConverter.toMerchant(entity);
        merchantDao.saveOrUpdate(merchant);
        return MerchantConverter.toMerchantEntity(merchant);
    }

    @Override
    public Boolean updateSelective(MerchantEntity entity) {
        Merchant merchant = MerchantConverter.toMerchant(entity);
        return merchantDao.updateById(merchant);
    }
}