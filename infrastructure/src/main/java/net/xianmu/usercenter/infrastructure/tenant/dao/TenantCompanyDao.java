package net.xianmu.usercenter.infrastructure.tenant.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.xianmu.usercenter.infrastructure.tenant.mapper.TenantCompanyMapper;
import net.xianmu.usercenter.infrastructure.tenant.model.TenantCompany;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-23 16:16:29
 */
@Component
public class TenantCompanyDao extends ServiceImpl<TenantCompanyMapper, TenantCompany> {


    public TenantCompany selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantCompany> query = new LambdaQueryWrapper<>();
        query.eq(TenantCompany::getTenantId, tenantId);
        return getOne(query);
    }
}
