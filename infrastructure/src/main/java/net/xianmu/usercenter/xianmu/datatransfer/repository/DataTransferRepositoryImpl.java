package net.xianmu.usercenter.xianmu.datatransfer.repository;

import net.xianmu.usercenter.domain.xianmu.datatransfer.repository.DataTransferRepository;
import net.xianmu.usercenter.xianmu.datatransfer.mapper.DataTransferMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2023-07-14 14:27:46
* @version 1.0
*
*/
@Repository
public class DataTransferRepositoryImpl implements DataTransferRepository {

    @Autowired
    private DataTransferMapper dao;


    @Override
    public Long countAdminAll() {
        return dao.countAdminAll();
    }

    public List<Map<String, Object>> selectAdminList(Long id, Integer offset) {
        return dao.selectAdminList(id, offset);
    }

    @Override
    public Long countMerchantAll() {
        return dao.countMerchantAll();
    }

    @Override
    public List<Map<String, Object>> selectMerchantList(Long id, Integer offset) {
        return dao.selectMerchantList(id, offset);
    }

    @Override
    public List<Map<String, Object>> selectMerchantListByIdList(List<Long> idList) {
        return dao.selectMerchantListByIdList(idList);
    }


    @Override
    public List<Map<String, Object>> selectMerchantAccountList(Long id, Integer offset) {
        return dao.selectMerchantAccountList(id, offset);
    }

    @Override
    public List<Map<String, Object>> selectMerchantAccountListByIdList(List<Long> idList) {
        return dao.selectMerchantAccountListByIdList(idList);
    }

    @Override
    public Long countContactAll() {
        return dao.countContactAll();
    }

    @Override
    public List<Map<String, Object>> selectContactList(Long id, Integer offset) {
        return dao.selectContactList(id, offset);
    }

    @Override
    public List<Map<String, Object>> selectContactListListByIdList(List<Long> idList) {
        return dao.selectContactListListByIdList(idList);
    }


}