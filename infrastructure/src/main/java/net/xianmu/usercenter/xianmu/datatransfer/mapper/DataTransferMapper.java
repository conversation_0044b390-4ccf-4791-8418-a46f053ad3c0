package net.xianmu.usercenter.xianmu.datatransfer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023-07-14 14:27:46
 * @version 1.0
 *
 */
@Mapper
@DS("xm")
public interface DataTransferMapper{

    Long countAdminAll();

    @MapKey("admin_id")
    List<Map<String, Object>> selectAdminList(@Param("adminId") Long id, @Param("offset")Integer offset);

    Long countMerchantAll();

    @MapKey("m_id")
    List<Map<String, Object>> selectMerchantList(@Param("mId") Long id,  @Param("offset")Integer offset);

    Long countMerchantAccountAll();

    @MapKey("m_id")
    List<Map<String, Object>> selectMerchantListByIdList(@Param("idList") List<Long> idList);

    @MapKey("account_id")
    List<Map<String, Object>> selectMerchantAccountList(@Param("accountId") Long id,  @Param("offset")Integer offset);

    @MapKey("account_id")
    List<Map<String, Object>> selectMerchantAccountListByIdList(@Param("idList") List<Long> idList);

    Long countContactAll();

    @MapKey("contact_id")
    List<Map<String, Object>> selectContactList(@Param("contactId") Long id,  @Param("offset")Integer offset);

    @MapKey("contact_id")
    List<Map<String, Object>> selectContactListListByIdList(@Param("idList") List<Long> idList);
}
