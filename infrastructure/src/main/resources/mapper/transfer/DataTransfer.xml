<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.xianmu.datatransfer.mapper.DataTransferMapper">

    <select id="selectAdminList" resultType="map">
        select t.admin_id as id,t.* from admin t where t.admin_id > #{adminId} order by t.admin_id limit #{offset}
    </select>

    <select id="countAdminAll" resultType = "java.lang.Long">
        select count(*) from admin t
    </select>

    <select id="selectMerchantList" resultType="map">
        select t.m_id as id,t.* from merchant t where t.m_id > #{mId} order by t.m_id limit #{offset}
    </select>

    <select id="countMerchantAll" resultType = "java.lang.Long">
        select count(*) from merchant t
    </select>

    <select id="selectMerchantListByIdList" resultType="map">
        select
        t.m_id as id,t.*
        from merchant t where t.m_id in
        <foreach collection="idList"  item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
    </select>

    <select id="selectMerchantAccountList" resultType="map">
        select t.account_id as id,t.* from merchant_sub_account t where t.account_id > #{accountId} order by t.account_id limit #{offset}
    </select>

    <select id="selectMerchantAccountListByIdList" resultType="map">
        select
        t.account_id as id,t.*
        from merchant_sub_account t where t.account_id in
        <foreach collection="idList"  item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
    </select>

    <select id="countMerchantAccountAll" resultType = "java.lang.Long">
        select count(*) from merchant_sub_account t
    </select>

    <select id="selectContactList" resultType="map">
        select t.contact_id as id,t.* from contact t where t.contact_id > #{contactId} order by t.contact_id limit #{offset}
    </select>

    <select id="selectContactListListByIdList" resultType="map">
        select
        t.contact_id as id,t.*
        from contact t where t.contact_id in
        <foreach collection="idList"  item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
    </select>

    <select id="countContactAll" resultType = "java.lang.Long">
        select count(*) from contact t
    </select>
</mapper>