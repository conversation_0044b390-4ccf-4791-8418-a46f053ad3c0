<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.tenant.mapper.TenantMapper">
    <resultMap id="BaseResultMap" type="net.xianmu.usercenter.infrastructure.tenant.model.Tenant">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="belong_DB" jdbcType="VARCHAR" property="belongDb"/>
        <result column="admin_id" property="adminId"/>
        <result column="op_uid" jdbcType="BIGINT" property="opUid"/>
        <result column="op_uname" jdbcType="VARCHAR" property="opUname"/>
        <result column="profit_sharing_switch" jdbcType="TINYINT" property="profitSharingSwitch"/>
        <result column="online_pay_channel" jdbcType="TINYINT" property="onlinePayChannel"/>
    </resultMap>
    <sql id="base_column_list">
        id
        , phone, `password`, tenant_name, `type`, `status`, create_time, update_time, belong_DB ,op_uid,op_uname,
        admin_id,profit_sharing_switch, online_pay_channel
    </sql>

    <sql id="tenant_and_business_model_column_list">
        t.id tenantId, t.type tenantType, t.status, c.id tenantCompanyId, c.company_name companyName, c.contact_name contactName, t.tenant_name tenantName, c.credit_code
        creditCode, t.admin_id adminId,
        t.phone, t.create_time createTime, t.update_time updateTime,t.op_uname, t.online_pay_channel
    </sql>

    <sql id="base_where_rule">
        <if test="id != null">
            and t.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="tenantIdList != null and tenantIdList.size > 0">
            and t.id in
            <foreach collection="tenantIdList" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="adminId != null">
            and t.admin_id = #{adminId}
        </if>
        <if test="tenantName != null and tenantName != ''">
            and t.tenant_name like concat('%',#{tenantName},'%')
        </if>
        <if test="type != null">
            and t.`type` = #{type,jdbcType=INTEGER}
        </if>
        <if test="status != null">
            and t.`status` = #{status,jdbcType=INTEGER}
        </if>
        <if test="phone != null and phone != ''">
            and t.phone like concat('%',#{phone},'%')
        </if>
        <if test="profitSharingSwitch != null">
            and t.profit_sharing_switch = #{profitSharingSwitch}
        </if>
        <if test="onlinePayChannel != null">
            and t.online_pay_channel = #{onlinePayChannel}
        </if>
    </sql>


    <sql id="base_and_company_where_rule">
        <if test="id != null">
            and t.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="tenantIdList != null and tenantIdList.size > 0">
            and t.id in
            <foreach collection="tenantIdList" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="adminId != null">
            and t.admin_id = #{adminId}
        </if>
        <if test="tenantName != null and tenantName != ''">
            and t.tenant_name like concat('%',#{tenantName},'%')
        </if>
        <if test="type != null">
            and t.`type` = #{type,jdbcType=INTEGER}
        </if>
        <if test="status != null">
            and t.`status` = #{status,jdbcType=INTEGER}
        </if>
        <if test="phone != null and phone != ''">
            and t.phone like concat('%',#{phone},'%')
        </if>
        <if test="profitSharingSwitch != null">
            and t.profit_sharing_switch = #{profitSharingSwitch}
        </if>
        <if test="onlinePayChannel != null">
            and t.online_pay_channel = #{onlinePayChannel}
        </if>
        <if test="companyName != null and companyName != ''">
            and c.company_name like concat(#{companyName},'%')
        </if>
        <if test="contactName != null and contactName != ''">
            and c.contact_name like concat(#{contactName},'%')
        </if>
    </sql>

    <select id="selectTenantsByCondition" parameterType="net.xianmu.usercenter.common.input.query.TenantQueryInput"
            resultType="net.xianmu.usercenter.infrastructure.tenant.model.Tenant">
        select
        <include refid="base_column_list"/>
        from tenant t
        <where>
            <include refid="base_where_rule"/>
        </where>
    </select>


    <select id="selectTenantsByConditionNonFuzzy"
            parameterType="net.xianmu.usercenter.common.input.query.TenantQueryInput"
            resultType="net.xianmu.usercenter.infrastructure.tenant.model.Tenant">
        select
        <include refid="base_column_list"/>
        from tenant t
        <where>
            <if test="id != null">
                and t.id = #{id,jdbcType=BIGINT}
            </if>
            <if test="adminId != null">
                and t.admin_id = #{adminId,jdbcType=BIGINT}
            </if>
            <if test="tenantName != null and tenantName != ''">
                and t.tenant_name = #{tenantName}
            </if>
            <if test="type != null">
                and t.`type` = #{type,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and t.`status` = #{status,jdbcType=INTEGER}
            </if>
        </where>
    </select>


    <select id="selectTenantAndBusinessModelsByCondition"
            resultType="net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel">
        select
        <include refid="tenant_and_business_model_column_list"/>
        from tenant t
        left join business_information c on t.id = c.biz_id and c.type = 0
        <where>
            <include refid="base_where_rule"/>
            <if test="companyName != null and companyName != ''">
                and c.company_name like concat('%',#{companyName},'%')
            </if>
        </where>
    </select>


    <select id="selectTenantAndBusinessModelsByIds"
            resultType="net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel">
        select
        <include refid="tenant_and_business_model_column_list"/>
        from tenant t
        left join business_information c on t.id = c.biz_id and c.type = 0
        where t.id in
        <foreach collection="idList" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="getTenantsPage"
            resultType="net.xianmu.usercenter.infrastructure.tenant.model.TenantAndBusinessModel">
        select
        <include refid="tenant_and_business_model_column_list"/>
        from
        tenant t
        left join business_information c on t.id = c.biz_id and c.type = 0
        <where>
            <include refid="base_and_company_where_rule"/>
        </where>
        order by t.${sortWord} ${sortType}
    </select>


    <update id="batcheUpdateOp">
        update tenant
        set op_uid = #{opUid,jdbcType=BIGINT},
        op_uname = #{opUname,jdbcType=VARCHAR}
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

</mapper>