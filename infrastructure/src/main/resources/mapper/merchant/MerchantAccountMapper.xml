<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreAccountMapper">


    <select id="selectXmErrorAccount" resultType="net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreAccount">
        select t.id , t.xm_account_id xmAccountId from merchant_store_account t where t.`tenant_id`  = 1 and t.`store_id`  is null and exists(select m.id from `merchant_store` m where t.m_id = m.`m_id` ) order by t.id limit 20;
    </select>

</mapper>
