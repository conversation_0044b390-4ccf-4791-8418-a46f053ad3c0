<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreMapper">
    <resultMap id="BaseResultMap" type="net.xianmu.usercenter.infrastructure.merchant.model.MerchantStore">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="bill_switch" property="billSwitch"/>
        <result column="online_payment" property="onlinePayment"/>
        <result column="store_no" property="storeNo"/>
        <result column="balance_authority" property="balanceAuthority"/>
        <result column="business_type" property="businessType"/>
        <result column="regional_id" property="regionalId"/>
        <result column="m_id" property="mId"/>
        <result column="channel_code" property="channelCode"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByAccountCondition" resultMap="BaseResultMap">
        select s.*
        from
        merchant_store s
        left join merchant_store_account a on a.tenant_id = s.tenant_id and a.store_id = s.id
        <where>
            <if test="type != null">
                and s.type = #{type}
            </if>
            <if test="status != null">
                and s.status = #{status}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and s.store_no = #{storeNo}
            </if>
            <if test="storeName != null and storeName != ''">
                and s.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="tenantId != null">
                and s.tenant_id = #{tenantId}
            </if>
            <if test="phone != null">
                and a.phone like concat('%',#{phone},'%')
            </if>
        </where>
    </select>


    <select id="selectMerchantStoreAndExtends" resultType="net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity">
        select
        ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        ms.type,
        ms.register_time registerTime,
        ms.status,
        ms.audit_remark auditRemark,
        ms.remark,
        ms.audit_time auditTime,
        ms.create_time createTime,
        ms.update_time updateTime,
        ms.bill_switch billSwitch,
        ms.online_payment onlinePayment,
        ms.store_no storeNo,
        ms.balance_authority balanceAuthority,
        ms.business_type businessType,
        ms.regional_id regionalId,
        ms.m_id mId,
        ms.channel_code channelCode,
        ms.area_no areaNo,
        ext.pop_view popView,
        ext.change_pop changePop,
        ext.first_login_pop firstLoginPop,
        ext.display_button displayButton,
        ext.pre_register_flag preRegisterFlag,
        ext.mock_login_flag mockLoginFlag,
        ext.province,
        ext.city,
        ext.area,
        ext.poi_note poiNote,
        ext.operate_status operateStatus,
        ext.business_line businessLine,
        ro.admin_id adminId,
        ro.size
        from merchant_store ms
        left join merchant_store_ext ext on ext.store_id = ms.id
        left join regional_organization ro on ro.id = ms.regional_id
        <if test="direct !=null ">
        left join xm_merchant_adapter xma on xma.store_id = ms.id
        </if>
        <include refid="selectMerchantStoreAndExtendsWhere"/>
        order by ms.id desc
    </select>


    <select id="selectMIdListByCondition" resultType="long">
        select
        distinct ms.m_id mId 
        from merchant_store ms
        left join merchant_store_ext ext on ext.store_id = ms.id
        left join regional_organization ro on ro.id = ms.regional_id
        <if test="direct !=null ">
            left join xm_merchant_adapter xma on xma.store_id = ms.id
        </if>
        <include refid="selectMerchantStoreAndExtendsWhere"/>
    </select>

    <sql id= "selectMerchantStoreAndExtendsWhere">
        <where>
            <if test="storeId != null">
                and ms.id = #{storeId}
            </if>
            <if test="storeIdList != null and storeIdList.size() >0 ">
                and ms.id in
                <foreach collection="storeIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                and ms.type = #{type}
            </if>
            <if test="status != null">
                and ms.status = #{status}
            </if>
            <if test="phonePrefix != null">
                and exists (select 1 from merchant_store_account msa where msa.store_id = ms.id and msa.tenant_id = ms.tenant_id and msa.delete_flag = 1 and msa.phone like  concat(#{phonePrefix},'%'))
            </if>
            <if test="storeNo != null and storeNo != ''">
                and ms.store_no = #{storeNo}
            </if>
            <if test="storeName != null and storeName != ''">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="exactStoreName != null and exactStoreName != ''">
                and ms.store_name = #{exactStoreName}
            </if>
            <if test="exactStoreNameList != null and exactStoreNameList.size() >0 ">
                and ms.store_name in
                <foreach collection="exactStoreNameList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="startRegisterTime != null">
                and ms.register_time <![CDATA[>=]]> #{startRegisterTime}
            </if>
            <if test="endRegisterTime != null">
                and ms.register_time <![CDATA[<=]]> #{endRegisterTime}
            </if>
            <if test="startAuditTime != null">
                and ms.audit_time <![CDATA[>=]]> #{startAuditTime}
            </if>
            <if test="endAuditTime != null">
                and ms.audit_time <![CDATA[<=]]> #{endAuditTime}
            </if>
            <if test="province != null and province != ''">
                and ext.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ext.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and ext.area = #{area}
            </if>
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="direct !=null ">
                and xma.direct=#{direct}
            </if>
            <if test="areaNo !=null ">
                and ms.area_no =#{areaNo}
            </if>
            <if test="size !=null ">
                and ro.size = #{size}
            </if>
            <if test="mId !=null ">
                and ms.m_id = #{mId}
            </if>
            <if test="mIds != null and mIds.size() >0 ">
                and ms.m_id in
                <foreach collection="mIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="areaNos != null and areaNos.size() >0 ">
                and ms.area_no in
                <foreach collection="areaNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>







    <select id="selectMerchantStoreAndAddressPage" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel">
        select ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        ms.type,
        ms.register_time registerTime,
        ms.status,
        ms.bill_switch billSwitch,
        ms.store_no storeNo,
        ms.audit_time auditTime,
        ms.remark,
        ms.online_payment onlinePayment,
        concat(ma.province, ma.city, ma.area, ma.address, ifnull(ma.house_number, '')) deliveryAddress,
        ma.province,
        ma.city,
        ma.area,
        ma.address
        from merchant_store ms
        left join merchant_store_group_mapping msgm on  msgm.tenant_id = #{tenantId} and msgm.tenant_id = ms.tenant_id and msgm.store_id = ms.id
        left join  merchant_address ma on ma.tenant_id = #{tenantId} and ma.store_id = msgm.store_id
        <where>
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="groupId != null">
                and msgm.group_id = #{groupId}
            </if>
            <if test="id != null">
                and ms.id = #{id}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and ms.store_no = #{storeNo}
            </if>
            <if test="storeName != null">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="status != null">
                and ms.status = #{status}
            </if>
            <if test="type != null">
                and ms.type = #{type}
            </if>
            <if test="storeIds != null and storeIds.size() >0 ">
                and ms.id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="province != null and province != ''">
                and ma.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ma.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and ma.area = #{area}
            </if>
        </where>
        order by ms.id desc
    </select>


    <select id="selectMerchantStorePage" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel">
        select ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        msa.phone,
        ms.type,
        ms.register_time registerTime,
        ms.status,
        ms.bill_switch billSwitch,
        ms.store_no storeNo,
        ms.audit_time auditTime,
        ms.remark,
        ms.online_payment onlinePayment,
        ms.place_order_permission_expiry_time placeOrderPermissionExpiryTime,
        ms.place_order_permission_time_limited placeOrderPermissionTimeLimited,
        ms.enable_offline_payment enableOfflinePayment,
        ms.balance_authority balanceAuthority
        from merchant_store ms
        left join merchant_store_account msa on msa.tenant_id = ms.tenant_id and ms.id = msa.store_id and msa.type = 0
        <where>
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="tenantIds != null and tenantIds.size() >0 ">
                and ms.tenant_id in
                <foreach collection="tenantIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and ms.id = #{id}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and ms.store_no = #{storeNo}
            </if>
            <if test="storeName != null">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="phone != null and phone != ''">
                and msa.phone like concat(#{phone},'%')
            </if>
            <if test="startTime != null and endTime != null">
                and ms.register_time <![CDATA[>=]]> #{startTime}
                and ms.register_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null">
                and ms.status = #{status}
            </if>
            <if test="excludeStatusList != null and excludeStatusList.size() >0 ">
                and ms.status not in
                <foreach collection="excludeStatusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                and ms.type = #{type}
            </if>
            <if test="typeList != null and typeList.size() >0 ">
                and ms.type in
                <foreach collection="typeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="billSwitch != null">
                and ms.bill_switch = #{billSwitch}
            </if>
            <if test="storeIds != null and storeIds.size() >0 ">
                and ms.id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="noMatchingStoreIds != null and noMatchingStoreIds.size() >0 ">
                and ms.id not in
                <foreach collection="noMatchingStoreIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="supplyStoreIds != null and supplyStoreIds.size() >0 ">
                and ms.id
                <if test="supplyStatus == 0">
                    in
                </if>
                <if test="supplyStatus == 1">
                    not in
                </if>
                <foreach collection="supplyStoreIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="placeOrderEnableFlag != null and placeOrderEnableFlag == false ">
                and ms.place_order_permission_expiry_time <![CDATA[<]]> CURDATE()
            </if>
            <if test="placeOrderEnableFlag != null and placeOrderEnableFlag == true ">
                and ms.place_order_permission_expiry_time <![CDATA[>=]]> CURDATE()
            </if>
            <if test="placeOrderPermissionExpiryTime != null">
                and ms.place_order_permission_expiry_time BETWEEN CURDATE() and DATE_ADD(CURDATE(), INTERVAL #{placeOrderPermissionExpiryTime}  DAY)
            </if>
            <if test="enableOfflinePayment != null">
                and ms.enable_offline_payment = #{enableOfflinePayment}
            </if>
            <if test="balanceAuthority != null">
                and ms.balance_authority = #{balanceAuthority}
            </if>
        </where>
        order by ms.id desc
    </select>



    <select id="selectMerchantStorePageForXM" resultType="net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity">
        select
        ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        ms.type,
        ms.register_time registerTime,
        ms.status,
        ms.audit_time auditTime,
        ms.create_time createTime,
        ms.update_time updateTime,
        ms.business_type businessType,
        ms.regional_id regionalId,
        ms.m_id mId,
        ms.channel_code channelCode,
        ms.area_no areaNo,
        ext.province,
        ext.city,
        ext.area,
        ext.poi_note poiNote,
        ext.operate_status operateStatus,
        ro.admin_id adminId,
        ro.size
        from merchant_store ms
        left join merchant_store_ext ext on ext.store_id = ms.id
        left join regional_organization ro on ro.id = ms.regional_id
        left join xm_merchant_adapter xma on xma.store_id = ms.id
        <include refid="selectStoreIdForXmPageWhere"/>
    </select>


    <select id="selectStoreIdForXmPage" resultType="long">
        select
        ms.id
        from merchant_store ms
        left join merchant_store_ext ext on ext.store_id = ms.id
        left join regional_organization ro on ro.id = ms.regional_id
        left join xm_merchant_adapter xma on xma.store_id = ms.id
        <include refid="selectStoreIdForXmPageWhere"/>
        limit #{xmPageDefaultQuerySize}
    </select>

    <sql id = "selectStoreIdForXmPageWhere">
        <where>
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="id != null">
                and ms.id = #{id}
            </if>
            <if test="storeName != null">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="startTime != null">
                and ms.register_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and ms.register_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null">
                <!-- 2024-07-19 鲜沐免审改造，不再有待审核的数据，统一调整为待核验-->
                <choose>
                    <when test="status == 100">
                        and ms.status = 1 and  ext.operate_status = 3
                    </when>
                    <when test="status == 101">
                        and ms.status = 1 and ext.operate_status = 2
                    </when>
                    <when test="status == 1">
                        and ms.status = 1 and ext.operate_status != 3 and ext.operate_status != 2
                    </when>
                    <otherwise>
                        and ms.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="phone != null">
                and exists (select 1 from merchant_store_account msa where msa.store_id = ms.id and msa.tenant_id = ms.tenant_id and msa.delete_flag = 1 and msa.phone like  concat(#{phone},'%'))
            </if>
            <if test="excludeStatusList != null and excludeStatusList.size() >0 ">
                and ms.status not in
                <foreach collection="excludeStatusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                and ms.type = #{type}
            </if>
            <if test="typeList != null and typeList.size() >0 ">
                and ms.type in
                <foreach collection="typeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="direct !=null ">
                and xma.direct=#{direct}
            </if>
            <if test="areaNo !=null ">
                and ms.area_no =#{areaNo}
            </if>
            <if test="size !=null ">
                and ro.size = #{size}
            </if>
            <if test="adminId !=null ">
                and ro.admin_id = #{adminId}
            </if>
            <if test="mId !=null ">
                and ms.m_id = #{mId}
            </if>
            <if test="province != null and province != ''">
                and ext.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ext.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and ext.area = #{area}
            </if>
            <if test="storeIds != null and storeIds.size() >0 ">
                and ms.id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="mIds != null and mIds.size() >0 ">
                and ms.m_id in
                <foreach collection="mIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="areaNos != null and areaNos.size() >0 ">
                and ms.area_no in
                <foreach collection="areaNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="noMatchingStoreIds != null and noMatchingStoreIds.size() >0 ">
                and ms.id not in
                <foreach collection="noMatchingStoreIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ms.m_id desc
    </sql>



    <select id="selectAccountPage" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreModel">
        select ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        ms.status,
        ms.bill_switch billSwitch,
        ms.store_no storeNo,
        msa.id accountId,
        msa.phone,
        msa.type accountType,
        msa.account_name accountName,
        msa.status accountStatus,
        msa.last_login_time lastLoginTime
        from merchant_store ms
        left join merchant_store_account msa on msa.tenant_id = #{tenantId} and ms.id = msa.store_id
        <where>
            and msa.delete_flag = 1
            and ms.status != 3
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="storeName != null">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="storeId != null">
                and ms.id = #{storeId}
            </if>
            <if test="phone != null and phone != ''">
                and msa.phone = #{phone}
            </if>

        </where>
            order by field(ms.status, 2,0,1) desc, ms.id desc
    </select>


    <select id="selectNoPullBlackRecordMerchant" resultMap="BaseResultMap">
        SELECT t.id,t.tenant_id,t.regional_id,t.m_id FROM `merchant_store` t where t.`status` = 4 and not exists (select 1 from merchant_store_change_log m where m.store_id = t.id and m.op_type = 3)
    </select>
</mapper>