<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreGroupMapper">

    <select id="queryBatchByStoreIds" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreGroupModel">
        select m.store_id storeId, g.name merchantStoreGroupName, g.id merchantStoreGroupId
        from merchant_store_group g
        inner join merchant_store_group_mapping m on g.id = m.group_id
        where m.store_id in
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="tenantId != null">
            and m.tenant_id = #{tenantId}
        </if>
    </select>


    <select id="queryBatchByGroupIds" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreGroupModel">
        select m.store_id storeId, g.name merchantStoreGroupName, g.id merchantStoreGroupId
        from merchant_store_group g
        inner join merchant_store_group_mapping m on g.id = m.group_id
        where g.id in
        <foreach collection="groupIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="tenantId != null">
            and g.tenant_id = #{tenantId}
        </if>
    </select>


    <select id="queryGroupsWithStoreCount" resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantStoreGroupModel">
        select count(m.store_id) storeNum, g.name merchantStoreGroupName, g.id merchantStoreGroupId
        from merchant_store_group g
        left join merchant_store_group_mapping m on g.id = m.group_id and g.tenant_id = m.tenant_id
        where g.tenant_id = #{tenantId}
        <if test="name != null">
            and g.`name` like concat('%',#{name},'%')
        </if>
        group by g.id, g.name
    </select>


    <select id="getMerchantStoreGroupPage" resultType="net.xianmu.usercenter.infrastructure.merchant.model.MerchantStoreGroup">
        select
        id, `name`, create_time createTime, update_time updateTime, type
        from
        merchant_store_group
        <where>
            tenant_id = #{tenantId}
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="merchantStoreGroupName != null">
                and `name` like concat('%',#{merchantStoreGroupName},'%')
            </if>
        </where>
        <if test="updateTimeSort != null">
            order by `type` desc, update_time ${updateTimeSort}
        </if>
        <if test="updateTimeSort == null and createTimeSort == null">
            order by `type` desc, id desc
        </if>
        <if test="createTimeSort != null">
            order by `type` desc, id ${createTimeSort}
        </if>
    </select>
</mapper>