<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStoreGroupMappingMapper">

    <select id="countStoreNumByGroupId" resultType="java.lang.Integer">
        select count(1)
        from merchant_store s
        left join merchant_store_group_mapping m on s.id=m.store_id
        where m.tenant_id=#{tenantId}
        and m.group_id=#{groupId}
    </select>

</mapper>
