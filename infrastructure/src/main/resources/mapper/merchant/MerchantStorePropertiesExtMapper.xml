<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantStorePropertiesExtMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.usercenter.infrastructure.merchant.model.MerchantStorePropertiesExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="pro_key" jdbcType="VARCHAR" property="proKey" />
    <result column="pro_value" jdbcType="VARCHAR" property="proValue" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, pro_key, pro_value,store_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_store_properties_ext
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByMidKey"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_store_properties_ext
    where m_id = #{mId} and pro_key = #{proKey}
  </select>

  <select id="selectByMidsKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_store_properties_ext
    where m_id in
    <foreach collection="mids" open="(" close=")" separator="," item="mid">
      #{mid}
    </foreach>
      and pro_key = #{proKey}
  </select>

  <insert id="insertSelectiveOnDuplicate">
    insert into merchant_store_properties_ext (m_id,store_id, pro_key, pro_value)
    values (#{mId,jdbcType=BIGINT},#{storeId}, #{proKey,jdbcType=VARCHAR},#{proValue})
      ON DUPLICATE key update update_time = now(),pro_value = #{proValue}
  </insert>
</mapper>