<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantAddressMapper">
    <!-- 结果集映射 -->
    <resultMap id="AddressAndContactResultMap" type="net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
        <result column="store_id" property="storeId" jdbcType="NUMERIC"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="house_number" property="houseNumber" jdbcType="VARCHAR"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="default_flag" property="defaultFlag" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="xm_contact_id" property="xmContactId" jdbcType="NUMERIC"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="address_remark" property="addressRemark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantAddressColumns">
          t.id,
          t.tenant_id,
          t.store_id,
          t.province,
          t.city,
          t.area,
          t.address,
          t.house_number,
          t.poi_note,
          t.create_time,
          t.update_time,
          t.default_flag,
          t.status,
          t.xm_contact_id,
          t.remark,
          t.address_remark
    </sql>


    <select id="selectDefaultMerchantAddressList"
            resultType="net.xianmu.usercenter.infrastructure.merchant.model.extend.MerchantAddressAndContactModel">
        select
        ma.store_id storeId,
        mc.name contactName,
        concat(ma.province, ma.city, ma.area, ma.address, ifnull(ma.house_number, '')) deliveryAddress,
        ma.province,
        ma.city,
        ma.area,
        ma.address,
        ma.house_number houseNumber,
        mc.phone contactPhone
        from merchant_address ma
        left join merchant_contact mc on ma.id = mc.address_id and mc.tenant_id = #{tenantId} and mc.default_flag = 1
        where ma.tenant_id = #{tenantId}
        and ma.status = 1
        and ma.default_flag = 1
        and ma.store_id in
        <foreach collection="storeIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


    <select id="selectConcatAddressByTenantIdAndStatus" resultType="java.lang.String">
        select distinct(CONCAT(province, "-", city, "-", area))
        from merchant_address t
        where t.tenant_id = #{tenantId}
          and t.status = #{status}
    </select>


    <select id="selectXmErrorAddress" resultType="net.xianmu.usercenter.infrastructure.merchant.model.MerchantAddress">
        select t.id,t.xm_contact_id xmContactId from `merchant_address`  t where t.`store_id` is null and t.`tenant_id` = 1 and exists(select m.id from `merchant_store` m where t.m_id = m.`m_id` ) order by t.id limit 20;
    </select>

</mapper>
