<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.usercenter.infrastructure.merchant.mapper.MerchantContactMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.usercenter.infrastructure.merchant.model.MerchantContact">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="address_id" jdbcType="BIGINT" property="addressId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="default_flag" jdbcType="INTEGER" property="defaultFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, address_id, `name`, phone, default_flag, create_time, update_time
  </sql>

    <select id="selectByStoreId" resultMap="BaseResultMap">
      select mc.*
      from merchant_store ms
             inner join merchant_address ma on ma.store_id = ms.id and ms.tenant_id = ma.tenant_id
             inner join merchant_contact mc on ma.id = mc.address_id and ms.tenant_id = mc.tenant_id
      where ms.id = #{storeId} and ms.tenant_id = #{tenantId}
      order by mc.id asc
    </select>

</mapper>
