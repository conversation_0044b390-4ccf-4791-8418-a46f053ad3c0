package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 13:50:22
* @version 1.0
*
*/
public interface MerchantAddressQueryRepository {


    MerchantAddressEntity selectDefaultAddress(Long tenantId, Long storeId);

    MerchantAddressEntity selectByXmContactId(Long xmContactId);


    MerchantAddressEntity selectById(Long id);


    List<String> selectConcatAddressByTenantIdAndStatus(Long tenantId, Integer status);

    /**
     * 根据指定参数查询列表
     * @param
     * @return
     */
    List<MerchantAddressEntity> selectByCondition(MerchantAddressQueryInput req);



    /**
     * 查询xm门店已存在，但是storeid为空的地址
     *
     * @param
     * @return
     */
    List<MerchantAddressEntity> selectXmErrorAddress();

}