package net.xianmu.usercenter.domain.merchant.entity;


import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.common.constants.MerchantConstant;
import net.xianmu.usercenter.common.enums.MerchantStoreEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-30 14:08:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantStoreExtEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 弹框
     */
    private Integer popView;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    private Integer changePop;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    private Integer firstLoginPop;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 预注册标记.1-代表为预注册
     */
    private Integer preRegisterFlag;

    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;


    /**
     * poi
     */
    private String poiNote;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人名称
     */
    private String updater;


    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    private Integer operateStatus;

    /**
     * 业务线:0=鲜沐;1=pop
     */
    private Integer businessLine;



    public static MerchantStoreExtEntity buildCreateEntityForBinLog(MerchantStoreCommandInput merchantStoreInput){
        MerchantStoreExtEntity entity = new MerchantStoreExtEntity();
        entity.setStoreId(merchantStoreInput.getId());
        entity.setTenantId(merchantStoreInput.getTenantId());
        entity.setPopView(merchantStoreInput.getPopView());
        entity.setChangePop(merchantStoreInput.getChangePop());
        entity.setDisplayButton(merchantStoreInput.getDisplayButton());
        entity.setFirstLoginPop(merchantStoreInput.getFirstLoginPop());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreator(merchantStoreInput.getOpName());
        entity.setPreRegisterFlag(merchantStoreInput.getPreRegisterFlag());
        entity.setMockLoginFlag(merchantStoreInput.getMockLoginFlag());
        entity.setProvince(merchantStoreInput.getProvince());
        entity.setCity(merchantStoreInput.getCity());
        entity.setArea(merchantStoreInput.getArea());
        entity.setPoiNote(merchantStoreInput.getPoiNote());
        entity.setOperateStatus(merchantStoreInput.getOperateStatus());
        entity.setBusinessLine(merchantStoreInput.getBusinessLine());
        return entity;
    }



    public static MerchantStoreExtEntity buildCreateEntityForBinLog(Map<String, String> data){
        Integer businessLine = data.get("business_line") == null ? null : Integer.valueOf(data.get("business_line"));
        return MerchantStoreExtEntity.builder()
                .changePop(Integer.valueOf(data.get("change_pop")))
                .popView(Integer.valueOf(data.get("pop_view")))
                .firstLoginPop(Integer.valueOf(data.get("first_login_pop")))
                .displayButton(Integer.valueOf(data.get("display_button")))
                .preRegisterFlag(converterPreRegisterFlag(data.get("pre_register_flag")))
                .province(data.get("province"))
                .city(data.get("city"))
                .area(data.get("area"))
                .mockLoginFlag(converterMockLoginFlag(data.get("role_id")))
                .poiNote(data.get("poi_note"))
                .operateStatus(Integer.valueOf(data.get("operate_status")))
                .businessLine(businessLine)
                .build();
    }


    public static Integer converterPreRegisterFlag(String source) {
        if(StrUtil.isBlank(source)) {
           return null;
        }
       return Integer.valueOf(source);
    }

    public static Integer converterMockLoginFlag(String source) {
        if(StrUtil.isBlank(source)) {
            return MerchantStoreEnums.MockLoginFlag.NO.getCode();
        }
        if (MerchantConstant.XM_MOCK_LOGIN.equals(Integer.valueOf(source))) {
            return MerchantStoreEnums.MockLoginFlag.YES.getCode();
        }
        return MerchantStoreEnums.MockLoginFlag.NO.getCode();
    }

}