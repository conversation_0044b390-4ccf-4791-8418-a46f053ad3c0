package net.xianmu.usercenter.domain.merchant.domain;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreExtRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 *
 * @Title: 门店拓展信息表业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-08-30 14:08:25
 * @version 1.0
 *
 */
@Service
@Slf4j
public class MerchantStoreExtDomainService {
    @Autowired
    private MerchantStoreExtRepository merchantStoreExtRepository;










    public void createOrUpdateForBinLog(Map<String, String> data, MerchantStoreEntity storeEntity) {
        Long storeId = storeEntity.getId();
        MerchantStoreExtEntity entity = merchantStoreExtRepository.selectByStoreId(storeId);
        MerchantStoreExtEntity updateEntity = MerchantStoreExtEntity.buildCreateEntityForBinLog(data);
        if (null == entity) {
            log.warn("当前门店：storeId ：{}暂无拓展信息,走初始化逻辑", storeId);
            updateEntity.setStoreId(storeId);
            updateEntity.setTenantId(storeEntity.getTenantId());
        } else {
            updateEntity.setId(entity.getId());
        }
        merchantStoreExtRepository.createOrUpdate(updateEntity);
    }

}
