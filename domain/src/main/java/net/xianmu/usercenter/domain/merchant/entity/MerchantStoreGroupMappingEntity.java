package net.xianmu.usercenter.domain.merchant.entity;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-01 18:18:22
 */
@Data
public class MerchantStoreGroupMappingEntity {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 租户Id
     */
    private Long groupId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public static MerchantStoreGroupMappingEntity buildCreateEntity(Long groupId, Long tenantId, Long storeId) {
        MerchantStoreGroupMappingEntity entity = new MerchantStoreGroupMappingEntity();
        entity.setTenantId(tenantId);
        entity.setStoreId(storeId);
        entity.setGroupId(groupId);
        return entity;
    }

}