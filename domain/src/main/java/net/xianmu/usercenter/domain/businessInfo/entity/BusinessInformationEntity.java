package net.xianmu.usercenter.domain.businessInfo.entity;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessInformationEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long bizId;

    /**
     * 类型：0-品牌用户，1-单店用户
     */
    private Integer type;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String phone;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static BusinessInformationEntity buildCommandEntity(BusinessInformationCommandInput input) {
        if (input == null) {
            return null;
        }
        BusinessInformationEntity businessInformationEntity = new BusinessInformationEntity();
        businessInformationEntity.setId(input.getId());
        businessInformationEntity.setTenantId(input.getTenantId());
        businessInformationEntity.setBizId(input.getBizId());
        businessInformationEntity.setType(input.getType());
        businessInformationEntity.setCompanyName(input.getCompanyName());
        businessInformationEntity.setCreditCode(input.getCreditCode());
        businessInformationEntity.setProvince(input.getProvince());
        businessInformationEntity.setCity(input.getCity());
        businessInformationEntity.setArea(input.getArea());
        businessInformationEntity.setAddress(input.getAddress());
        businessInformationEntity.setPhone(getCompletePhone(input));
        businessInformationEntity.setBusinessLicense(input.getBusinessLicense());
        businessInformationEntity.setCreateTime(input.getCreateTime());
        businessInformationEntity.setUpdateTime(input.getUpdateTime());
        businessInformationEntity.setContactName(input.getContactName());
        return businessInformationEntity;
    }


    private static String getCompletePhone(BusinessInformationCommandInput input) {
        String areaPhone = input.getCompanyAreaPhone();
        String phone = input.getCompanyPhone();
        if(StrUtil.isBlank(areaPhone) && StrUtil.isBlank(phone)) {
            return "";
        }
        return StrUtil.isBlank(areaPhone) ? phone : areaPhone + "-" + phone;
    }
}