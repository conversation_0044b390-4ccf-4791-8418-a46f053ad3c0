package net.xianmu.usercenter.domain.merchant.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-12 11:24:49
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XmContactMappingEntity {
	/**
	 * 自增长主键
	 */
	private Long id;

	/**
	 * 鲜沐contact表id
	 */
	private Long contactId;

	/**
	 * 用户中心地址表的id
	 */
	private Long addressId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}