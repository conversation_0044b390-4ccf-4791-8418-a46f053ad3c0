package net.xianmu.usercenter.domain.merchant.domain;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.redis.support.cache.annotation.XmCacheClear;
import net.xianmu.usercenter.common.constants.CommonRedisKey;
import net.xianmu.usercenter.common.enums.MerchantContactEnum;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantContactQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @version 1.0
 * @Title: 门店联系人业务领域
 */
@Service
@Slf4j
public class MerchantContactDomainService {

    @Autowired
    private MerchantContactCommandRepository merchantContactCommandRepository;

    @Autowired
    private MerchantContactQueryRepository merchantContactQueryRepository;

    public MerchantContactEntity create(MerchantContactCommandInput input) {
        // 参数校验
        ValidateUtil.paramValidate(input, "name", "phone");

        // 如果当前为默认联系人，需要将其他的默认改为非默认
        this.handleDefault(input);

        MerchantContactEntity entity = MerchantContactEntity.buildCreateEntity(input);
        entity = merchantContactCommandRepository.createOrUpdate(entity);
        return entity;
    }



    public Boolean createContactForMerchantBatchCreate(List<MerchantContactCommandInput> inputList) {
        if(CollUtil.isEmpty(inputList)) {
            throw new ParamsException("门店地址-联系人信息为空!");
        }
        List<MerchantContactEntity> entities = new ArrayList<>();
        inputList.forEach(input -> entities.add(MerchantContactEntity.buildCreateEntity(input)));
        return merchantContactCommandRepository.createBatch(entities);
    }




    public Boolean update(MerchantContactCommandInput input) {
        // 参数校验
        ValidateUtil.paramValidate(input, "id");

        Long id = input.getId();
        final MerchantContactEntity entity = merchantContactQueryRepository.selectById(id);
        if (Objects.isNull(entity)) {
            log.error("联系人不存在!, id:{}", id);
            throw new BizException("联系人不存在!");
        }

        // 如果当前为默认联系人，需要将其他的默认改为非默认
        // 目前不允许联系人修改地址id
        input.setTenantId(entity.getTenantId());
        input.setAddressId(entity.getAddressId());
        this.handleDefault(input);

        // 更新
        entity.buildUpdateEntity(input);
        return merchantContactCommandRepository.updateSelective(entity);
    }

    /**
     * 默认联系人只能有一个
     *
     * @param input
     */
    private void handleDefault(MerchantContactCommandInput input) {
        if (!MerchantContactEnum.DEFAULT.getType().equals(input.getDefaultFlag())) {
            return;
        }

        //需要将原来的默认联系人设置为非默认
        MerchantContactEntity defaultContact;
        if (input.getTenantId() == null) {
            log.warn("废弃的方法被调用了!");
            defaultContact = merchantContactQueryRepository.selectDefaultContact(input.getAddressId());
        } else {
            defaultContact= merchantContactQueryRepository.selectDefaultContact(input.getTenantId(), input.getAddressId());
        }

        if (Objects.nonNull(defaultContact)) {
            defaultContact.setDefaultFlag(MerchantContactEnum.NOT_DEFAULT.getType());
            merchantContactCommandRepository.createOrUpdate(defaultContact);
        }

    }


    public void createOrUpdateFotBinLog(MerchantContactEntity entity) {
        merchantContactCommandRepository.createOrUpdate(entity);
    }

    @XmCacheClear(prefixKey = CommonRedisKey.Cache.CACHE_DEFAULT_CONTACT, key = "{mId}", includeAppName = false)
    public boolean deleteRedisCacheAddress(long mId) {
        log.info("MerchantContactDomainService[]deleteRedisCacheAddress[]mId:{}", mId);
        return true;
    }
}
