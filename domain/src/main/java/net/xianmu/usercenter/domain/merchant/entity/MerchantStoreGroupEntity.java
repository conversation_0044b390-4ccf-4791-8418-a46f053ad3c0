package net.xianmu.usercenter.domain.merchant.entity;


import lombok.Data;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.input.command.MerchantStoreGroupCommandInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-11 10:55:37
 * @version 1.0
 *
 */
@Data
public class MerchantStoreGroupEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户Id
	 */
	private Long tenantId;

	/**
	 * 分组名称
	 */
	private String name;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 0、非默认分组 1、默认分组
	 */
	private Integer type;

	/**
	 * 门店id
	 */
	private Long storeId;


	/**
	 * 当前分组下的门店数
	 */
	private Integer storeNum;


	public static MerchantStoreGroupEntity toMerchantStoreGroupEntity(MerchantStoreGroupCommandInput input){
		MerchantStoreGroupEntity record = new MerchantStoreGroupEntity();
		record.setTenantId(input.getTenantId());
		record.setName(input.getName());
		record.setType(input.getType());
		return record;
	}

}