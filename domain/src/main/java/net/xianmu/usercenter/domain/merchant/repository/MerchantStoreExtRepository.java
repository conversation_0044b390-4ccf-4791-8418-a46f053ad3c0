package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreConfigEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;

/**
*
* <AUTHOR>
* @date 2023-08-30 14:08:25
* @version 1.0
*
*/
public interface MerchantStoreExtRepository {

    MerchantStoreExtEntity selectByStoreId(Long storeId);

    MerchantStoreExtEntity createOrUpdate(MerchantStoreExtEntity entity);
}