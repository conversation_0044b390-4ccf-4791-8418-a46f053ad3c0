package net.xianmu.usercenter.domain.invoice.entity;


import lombok.Data;
import net.xianmu.usercenter.common.util.DateUtil;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-07-13 13:58:41
 * @version 1.0
 *
 */
@Data
public class InvoiceMerchantRelationEntity {
	/**
	 * 自增长主键
	 */
	private Long id;

	/**
	 * 取自invoice_config表内
	 */
	private Long invoiceId;

	/**
	 * 取自merchant_store表中store_id
	 */
	private Long storeId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建关联关系提交时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改人
	 */
	private String updater;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 状态 0 存在 1 作废
	 */
	private Integer status;


	public static InvoiceMerchantRelationEntity converterToEntityForBinLog(Map<String, String> data) {
		InvoiceMerchantRelationEntity invoiceMerchantRelationEntity = new InvoiceMerchantRelationEntity();
		invoiceMerchantRelationEntity.setId(Long.valueOf(data.get("id")));
		invoiceMerchantRelationEntity.setInvoiceId(Long.valueOf(data.get("invoice_id")));
		//invoiceMerchantRelationEntity.setStoreId(data.get("store_id"));
		invoiceMerchantRelationEntity.setUpdateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), null));
		invoiceMerchantRelationEntity.setCreateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), null));
		invoiceMerchantRelationEntity.setCreator(data.get("creator"));
		invoiceMerchantRelationEntity.setUpdater(data.get("updater"));
		invoiceMerchantRelationEntity.setStatus(Integer.valueOf(data.get("status")));
		return invoiceMerchantRelationEntity;
	}


}