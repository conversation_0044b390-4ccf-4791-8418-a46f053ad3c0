package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 17:08:24
* @version 1.0
*
*/
public interface MerchantContactCommandRepository {

    /**
     * 按实体更新
     * @param
     */
    MerchantContactEntity createOrUpdate(MerchantContactEntity entity);


    /**
     * 按实体更新
     * @param
     */
    Boolean createBatch(List<MerchantContactEntity> entities);

    /**
     * 更新非空
     * @param
     */
    boolean updateSelective(MerchantContactEntity entity);

    /**
     * 物理删除
     */
    boolean delete(Long id);


    boolean deleteBatch(List<Long> idList);
}