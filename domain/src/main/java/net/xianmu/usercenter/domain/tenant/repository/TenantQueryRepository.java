package net.xianmu.usercenter.domain.tenant.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.TenantAndBusinessEntity;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;

import java.util.List;

public interface TenantQueryRepository {

    /**
     * 根据id查询租户信息
     *
     * @param
     * @return
     */
    TenantEntity getTenantById(Long id);


    /**
     * 根据id查询租户信息
     *
     * @param
     * @return
     */
    TenantEntity getTenantByAdminId(Long adminId);


    /**
     * 根据id列表查询租户列表
     *
     * @param
     * @return
     */
    List<TenantEntity> getTenantsByIds(List<Long> idList);

    /**
     * 根据指定参数查询租户列表(带分页)
     * @param
     * @return
     */
    PageInfo<TenantAndBusinessEntity> getTenantsPage(TenantQueryInput req, PageQueryInput pageQueryInput);

    /**
     * 根据指定参数查询租户列表
     * 1.如果只查品牌信息注意指定type
     * 2.名称支持模糊匹配
     * @param
     * @return
     */
    List<TenantEntity> getTenants(TenantQueryInput req);


    /**
     * 根据指定参数查询租户列表(不支持模糊匹配)
     * @param
     * @return
     */
    List<TenantEntity> getTenantsNonFuzzy(TenantQueryInput req);

    /**
     * 根据指定参数查询租户以及工商信息
     * 名称支持模糊匹配
     * @param
     * @return
     */
    List<TenantAndBusinessEntity> getTenantAndCompanyList(TenantQueryInput req);

    /**
     * 根据id列表查询租户以及工商信息
     * @param idList
     * @return
     */
    List<TenantAndBusinessEntity> getTenantAndCompanyByIds(List<Long> idList);
}
