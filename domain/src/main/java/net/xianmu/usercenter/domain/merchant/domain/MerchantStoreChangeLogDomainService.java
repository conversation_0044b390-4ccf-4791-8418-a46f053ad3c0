package net.xianmu.usercenter.domain.merchant.domain;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreChangeLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Title: 门店变更记录表业务逻辑实现类
 * @Description:
 * @date 2023-07-11 10:48:04
 */
@Service
public class MerchantStoreChangeLogDomainService {

    @Autowired
    private MerchantStoreChangeLogRepository merchantStoreChangeLogRepository;

    public MerchantStoreChangeLogEntity createOrUpdate(MerchantStoreChangeLogEntity entity) {
        return merchantStoreChangeLogRepository.createOrUpdate(entity);
    }

    public void createBatch(List<MerchantStoreChangeLogEntity> entities) {
        merchantStoreChangeLogRepository.createBatch(entities);
    }

    /**
     * 修改邀请码：一般用于取消来源
     * @param entity
     * @return
     */
    public void updateChannelCode(Long id, String inviterChannelCode, String merchantChannelCode) {
         merchantStoreChangeLogRepository.updateChannelCode(id, inviterChannelCode, merchantChannelCode);
    }

}
