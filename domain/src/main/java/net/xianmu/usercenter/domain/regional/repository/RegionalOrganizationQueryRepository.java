package net.xianmu.usercenter.domain.regional.repository;

import net.xianmu.usercenter.common.input.query.RegionalOrganizationQueryInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-07-06 16:32:07
* @version 1.0
*
*/
public interface RegionalOrganizationQueryRepository {

    List<RegionalOrganizationEntity> selectByCondition(RegionalOrganizationQueryInput req);

    RegionalOrganizationEntity selectByAdminAndTenant(Long adminId, Long tenantId);

}