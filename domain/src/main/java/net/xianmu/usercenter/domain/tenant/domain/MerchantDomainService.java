package net.xianmu.usercenter.domain.tenant.domain;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.input.command.MerchantCommandInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;
import net.xianmu.usercenter.domain.tenant.repository.MerchantCommandRepository;
import net.xianmu.usercenter.domain.tenant.repository.MerchantQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @Title: 商户表业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-05-26 18:35:35
 * @version 1.0
 *
 */

@Service
@Slf4j
public class MerchantDomainService {

    @Autowired
    private MerchantQueryRepository merchantQueryRepository;

    @Autowired
    private MerchantCommandRepository merchantCommandRepository;


    public MerchantEntity create(MerchantCommandInput input) {
        ValidateUtil.paramValidate(input);
        MerchantEntity entity = MerchantEntity.buildCreateEntity(input);
        return merchantCommandRepository.createOrUpdate(entity);
    }


    public Boolean updateSelective(MerchantCommandInput input) {
        ValidateUtil.paramValidate(input, "id");
        MerchantEntity merchantEntity = merchantQueryRepository.selectById(input.getId());

        if (null == merchantEntity) {
            log.error("品牌不存在，id：{}", input.getId());
            throw new BizException("品牌不存在!");
        }

        // 构建待更新的实体
        merchantEntity = MerchantEntity.buildUpdateEntity(input);
        merchantCommandRepository.updateSelective(merchantEntity);
        return true;
    }

}
