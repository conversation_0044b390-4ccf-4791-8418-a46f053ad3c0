package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-06-01 15:42:52
* @version 1.0
*
*/
public interface MerchantStoreGroupMappingQueryRepository {

    Integer countStoreNumByGroupId(Long groupId, Long tenantId);

    List<MerchantStoreGroupMappingEntity> selectStoresByByGroupId(Long groupId, Long tenantId);

    /**
     * 根据门店Id查询
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantStoreGroupMappingEntity selectByStoreId(Long storeId, Long tenantId);
}