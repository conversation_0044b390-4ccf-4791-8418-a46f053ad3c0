package net.xianmu.usercenter.domain.merchant.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/6/2 16:55
 */
@Data
public class MerchantStoreDto {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 账期开关 0开启 1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付0开启1关闭
     */
    private Integer onlinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 门店编号
     */
    private String storeNo;


    // 门店拓展信息

    /**
     * 弹框
     */
    private Integer popView;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    private Integer changePop;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    private Integer firstLoginPop;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 预注册标记.1-代表为预注册
     */
    private Integer preRegisterFlag;


    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;




    // 下面是子域的信息，这里为了方便交互，并未收缩到一个更小的领域

    // 账户相关
    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 店员名称
     */
    private String accountName;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer accountType;


    /**
     * 账户状态
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer accountStatus;

    /**
     * 账户上次登录时间
     */
    private LocalDateTime lastLoginTime;




    // 地址

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * poi
     */
    private String poiNote;


    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 收货地址
     */
    private String deliveryAddress;

}
