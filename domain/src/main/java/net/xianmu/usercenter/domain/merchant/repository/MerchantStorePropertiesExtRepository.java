package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStorePropertiesExtEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-08-30 14:08:25
* @version 1.0
*
*/
public interface MerchantStorePropertiesExtRepository {

    List<MerchantStorePropertiesExtEntity> selectByMIds(List<Long> mIds, String proKey);

    Boolean createOrUpdate(MerchantStorePropertiesExtEntity entity);
}