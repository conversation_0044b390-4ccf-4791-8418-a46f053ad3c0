package net.xianmu.usercenter.domain.tenant.param.command;

import lombok.Data;
import net.xianmu.authentication.client.input.purview.TenantPrivilegesInput;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.BusinessInformationEnum;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.tenant.valueobject.TenantPrivilegesValueObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 16:46
 */
@Data
public class TenantCommandInput {
    /**
     * 品牌商城ID
     */
    private Long tenantId;

    /**
     * 鲜沐大客户Id
     */
    private Long adminId;

    /**
     * 租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer type;

    /**
     * 登录手机号
     */
    private String loginPhone;

    /**
     * 登陆密码
     */
    private String loginPassword;

    /**
     * 商城名称
     */
    private String merchantName;

    /**
     * 租户联系人名称
     */
    private String contactName;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 街道地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String companyPhone;

    /**
     * 联系电话-区号
     */
    private String companyAreaPhone;

    /**
     * 分账开关0关1开
     */
    private Integer profitSharingSwitch;

    /**
     * 分账渠道 0 微信 1 汇付
     */
    private Integer onlinePayChannel;

    /**
     * 操作人authUserId
     */
    private Long opAuthUserId;

    /**
     * 操作人id
     */
    private Long opUid;

    /**
     * 操作人名称
     */
    private String opUname;


    /**
     * 系统来源,这里是透传给auth用的
     * 之所以额外弄一个字段是为了平衡之前oms，manage对接auth的一些问题
     */
    private Integer systemOrigin;

    /**
     * 权益列表
     */
    private List<TenantPrivilegesValueObject> tenantPrivilegesList;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;


    public static TenantAccountCommandInput converterToTenantAccountCommandInput(TenantCommandInput input) {
        TenantAccountCommandInput accountCommandInput = new TenantAccountCommandInput();
        accountCommandInput.setTenantId(input.getTenantId());
        accountCommandInput.setPhone(input.getLoginPhone());
        accountCommandInput.setLoginPassword(input.getLoginPassword());
        accountCommandInput.setNickname(input.getMerchantName() + TenantDefaultConstant.ADMIN_NICK_SUFFIX);
        accountCommandInput.setSystemOrigin(input.getSystemOrigin());
        accountCommandInput.setTenantPrivilegesList(input.getTenantPrivilegesList());
        accountCommandInput.setEmail(input.getEmail());
        accountCommandInput.setUpdater(input.getOpUname());
        return accountCommandInput;
    }

    public static BusinessInformationCommandInput converterToBusinessInformationCommandInput(TenantCommandInput input) {
        BusinessInformationCommandInput informationCommandInput = new BusinessInformationCommandInput();
        informationCommandInput.setTenantId(input.getTenantId());
        informationCommandInput.setBizId(input.getTenantId());
        informationCommandInput.setType(BusinessInformationEnum.TypeEnum.TENANT.getCode());
        informationCommandInput.setCompanyName(input.getCompanyName());
        informationCommandInput.setCreditCode(input.getCreditCode());
        informationCommandInput.setProvince(input.getProvince());
        informationCommandInput.setCity(input.getCity());
        informationCommandInput.setArea(input.getArea());
        informationCommandInput.setAddress(input.getAddress());
        informationCommandInput.setBusinessLicense(input.getBusinessLicense());
        informationCommandInput.setCompanyAreaPhone(input.getCompanyAreaPhone());
        informationCommandInput.setCompanyPhone(input.getCompanyPhone());
        informationCommandInput.setContactName(input.getContactName());
        return informationCommandInput;
    }


}
