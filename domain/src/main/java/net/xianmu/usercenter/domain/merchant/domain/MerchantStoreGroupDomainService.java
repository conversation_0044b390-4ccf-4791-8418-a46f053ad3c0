package net.xianmu.usercenter.domain.merchant.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.constants.NumberConstant;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.dto.MerchantStoreGroupBatchImportDTO;
import net.xianmu.usercenter.common.input.command.MerchantStoreGroupCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupMappingCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupMappingQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreGroupQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/11 10:54
 */
@Service
@Slf4j
public class MerchantStoreGroupDomainService {

    @Autowired
    private MerchantStoreGroupQueryRepository merchantStoreGroupQueryRepository;
    @Autowired
    private MerchantStoreQueryRepository merchantStoreQueryRepository;
    @Autowired
    private MerchantStoreGroupCommandRepository merchantStoreGroupCommandRepository;
    @Autowired
    private MerchantStoreGroupMappingCommandRepository merchantStoreGroupMappingCommandRepository;
    @Autowired
    private MerchantStoreGroupMappingQueryRepository merchantStoreGroupMappingQueryRepository;




    /**
     * 门店默认分组
     *
     * @param tenantId
     */
    void insertDefaultGroup(Long tenantId) {
        MerchantStoreGroupEntity record = new MerchantStoreGroupEntity();
        record.setTenantId(tenantId);
        record.setName(TenantDefaultConstant.DEFAULT_GROUP);
        record.setType(TenantDefaultConstant.GROUP_TYPE);
        merchantStoreGroupCommandRepository.createOrUpdate(record);
    }




    /**
     * 新增分组
     * @param input
     */
    public MerchantStoreGroupEntity create(MerchantStoreGroupCommandInput input) {
        this.validateGroupName(input.getTenantId(), input.getName(), input.getId());

        // 分组
        MerchantStoreGroupEntity entity = MerchantStoreGroupEntity.toMerchantStoreGroupEntity(input);
        entity = merchantStoreGroupCommandRepository.createOrUpdate(entity);

        // 绑定门店
        if (CollUtil.isNotEmpty(input.getStoreIdList())) {
            this.bindMerchantStoreGroup(entity.getId(), input.getTenantId(), input.getStoreIdList());
        }
        return entity;

    }

    /**
     * 绑定门店到指定分组，分组不存在就绑定默认分组
     * @param groupId
     * @param
     */
    public void bindMerchantStoreGroupByDefault(Long groupId, Long tenantId, Long storeId) {
        if (null == storeId) {
            return;
        }
        if(null == groupId) {
            MerchantStoreGroupEntity defaultGroup = merchantStoreGroupQueryRepository.selectDefaultGroup(tenantId);
            groupId = defaultGroup.getId();
        }

        // 查看门店是否已绑定分组
        MerchantStoreGroupMappingEntity mappingEntity = merchantStoreGroupMappingQueryRepository.selectByStoreId(storeId, tenantId);
        if(null == mappingEntity) {
            // 创建新的映射
            mappingEntity = MerchantStoreGroupMappingEntity.buildCreateEntity(groupId, tenantId, storeId);
            merchantStoreGroupMappingCommandRepository.createOrUpdate(mappingEntity);
        }
        // 移动分组
        merchantStoreGroupMappingCommandRepository.updateGroupIdByStoreIds(groupId, tenantId, Collections.singletonList(storeId));
    }

    /**
     * 绑定门店到指定分组
     * @param storeIdList
     */
    private void bindMerchantStoreGroup(Long groupId, Long tenantId, List<Long> storeIdList) {
        if (CollUtil.isEmpty(storeIdList)) {
            return;
        }
        // 目前一个门店只能属于一个分组，而且门店创建之后都会有一个分组
        // 所以绑定就相当于将门店从原分组移动到新分组
        merchantStoreGroupMappingCommandRepository.updateGroupIdByStoreIds(groupId, tenantId, storeIdList);
    }

    /**
     * 解绑门店
     * @param groupId
     * @param tenantId
     * @param storeIdList
     */
    private void unBindMerchantStoreGroup(Long groupId, Long tenantId, List<Long> storeIdList) {
        if (CollUtil.isEmpty(storeIdList)) {
            return;
        }
        // 目前一个门店只能属于一个分组，所以解除绑定就相当于将门店从原分组恢复到默认分组
        merchantStoreGroupMappingCommandRepository.updateGroupIdByStoreIds(groupId, tenantId, storeIdList);
    }




    /**
     * 修改
     * @param input
     */
    public Boolean update(MerchantStoreGroupCommandInput input) {
        log.info("接收到修改门店分组请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "id", "tenantId");
        Long tenantId = input.getTenantId();
        this.validateGroupName(tenantId, input.getName(), input.getId());

        // 处理门店组信息
        MerchantStoreGroupEntity entity = merchantStoreGroupQueryRepository.selectById(input.getId());
        if (entity == null) {
            throw new BizException("分组不存在!");
        }
        entity.setName(input.getName());
        merchantStoreGroupCommandRepository.createOrUpdate(entity);

        // 处理组下面的门店
        // 查询组下原有的门店
        final List<Long> storeIdList = input.getStoreIdList();
        List<MerchantStoreGroupMappingEntity> mappingEntities = merchantStoreGroupMappingQueryRepository.selectStoresByByGroupId(entity.getId(), tenantId);
        if (CollUtil.isEmpty(mappingEntities)) {
            // 如果原本的分组没有门店，直接添加即可
            this.bindMerchantStoreGroup(input.getId(), tenantId, storeIdList);
            return true;
        }

        // 如果原本的分组已经存在门店
        // 1.先获取当前分组的门店id列表
        List<Long> oldStoreIdList = mappingEntities.stream().map(MerchantStoreGroupMappingEntity::getStoreId).collect(Collectors.toList());
        // 2.查询租户默认分组
        MerchantStoreGroupEntity defaultGroup = merchantStoreGroupQueryRepository.selectDefaultGroup(tenantId);
        if (CollUtil.isEmpty(storeIdList)) {
            // 代表全部删除
            this.unBindMerchantStoreGroup(defaultGroup.getId(), tenantId, oldStoreIdList);
        } else {
            // 新增
            List<Long> insertList = ValidateUtil.getDifference(storeIdList, oldStoreIdList);
            this.bindMerchantStoreGroup(input.getId(), tenantId, insertList);
            // 剔除
            List<Long> deleteList = ValidateUtil.getDifference(oldStoreIdList, storeIdList);
            this.unBindMerchantStoreGroup(defaultGroup.getId(), tenantId, deleteList);
        }

        return true;

    }


    /**
     * 删除分组
     * @param input
     */
    public Boolean remove(MerchantStoreGroupCommandInput input) {
        log.info("接收到删除门店分组请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "id");

        // 处理门店组信息
        MerchantStoreGroupEntity entity = merchantStoreGroupQueryRepository.selectById(input.getId());
        if (entity == null) {
            log.info("分组不存在!id:{}", input.getId());
            return false;
        }
        if (TenantDefaultConstant.GROUP_TYPE.equals(entity.getType())) {
            log.info("默认分组不可删除!entity:{}", JSON.toJSONString(entity));
            throw new BizException("默认分组不可删除!");
        }
        Long tenantId = entity.getTenantId();

        // 解除分组映射
        MerchantStoreGroupEntity defaultGroup = merchantStoreGroupQueryRepository.selectDefaultGroup(tenantId);
        final List<MerchantStoreGroupMappingEntity> mappingEntities = merchantStoreGroupMappingQueryRepository.selectStoresByByGroupId(entity.getId(), tenantId);
        // 获取当前分组的门店id列表
        List<Long> oldStoreIdList = mappingEntities.stream().map(MerchantStoreGroupMappingEntity::getStoreId).collect(Collectors.toList());
        // 门店释放到默认分组
        this.unBindMerchantStoreGroup(defaultGroup.getId(), tenantId, oldStoreIdList);
        // 删除分组
        merchantStoreGroupCommandRepository.delete(entity.getId());

        return true;
    }




    /**
     * 批量导入
     * @param list
     * @return
     */
    public List<MerchantStoreGroupBatchImportDTO> batchCreate(List<MerchantStoreGroupBatchImportDTO> list, Long tenantId) {
        if(CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<MerchantStoreGroupBatchImportDTO> errorList = new ArrayList<>();
        for (MerchantStoreGroupBatchImportDTO dto : list) {
            if (StrUtil.isBlank(dto.getName())) {
                dto.setError("分组名称不能为空");
                errorList.add(dto);
                continue;
            }

            if (dto.getName().length() > NumberConstant.TEN) {
                dto.setError("门店分组名称长度不能大于10");
                errorList.add(dto);
                continue;
            }

            // 根据分组名称查询分组信息
            MerchantStoreGroupEntity groupEntity = merchantStoreGroupQueryRepository.selectByName(tenantId, dto.getName());
            if(null == groupEntity) {
                MerchantStoreGroupEntity merchantStoreGroup = new MerchantStoreGroupEntity();
                merchantStoreGroup.setTenantId(tenantId);
                merchantStoreGroup.setName(dto.getName());
                groupEntity = merchantStoreGroupCommandRepository.createOrUpdate(merchantStoreGroup);
            }

            if(StrUtil.isNotBlank(dto.getStoreName())){
                MerchantStoreEntity merchantStore = merchantStoreQueryRepository.selectByStoreName(tenantId, dto.getStoreName());
                if (Objects.isNull(merchantStore)) {
                    dto.setError("门店暂未注册");
                    errorList.add(dto);
                    continue;
                }

                // 查询门店是否已经绑定分组
                MerchantStoreGroupMappingEntity mappingEntity = merchantStoreGroupMappingQueryRepository.selectByStoreId(merchantStore.getId(), tenantId);
                if(null != mappingEntity) {
                    MerchantStoreGroupEntity oldGroup = merchantStoreGroupQueryRepository.selectById(mappingEntity.getGroupId());
                    // 如果是默认分组的门店
                    if (TenantDefaultConstant.GROUP_TYPE.equals(oldGroup.getType())){
                        mappingEntity.setGroupId(groupEntity.getId());
                        merchantStoreGroupMappingCommandRepository.createOrUpdate(mappingEntity);
                    }else {
                        String errorInfo = mappingEntity.getGroupId().equals(groupEntity.getId()) ? "门店已在当前分组存在" : "门店已在别的分组存在";
                        dto.setError(errorInfo);
                        errorList.add(dto);
                    }
                }
            }
        }
        return errorList;
    }

    /**
     * 校验
     * @param tenantId 租户id
     * @param name 分组名称
     */
    private void validateGroupName(Long tenantId, String name, Long currentId){
        MerchantStoreGroupEntity groupEntity = merchantStoreGroupQueryRepository.selectByName(tenantId, name);
        if(null != groupEntity && !groupEntity.getId().equals(currentId)) {
            log.warn("分组名称重复，name：{}", name);
            throw new BizException("门店分组已存在");
        }
    }




    /**
     * 分页查询
     * @param merchantStoreGroupQueryInput
     * @param pageQueryInput
     * @return
     */
    public PageInfo<MerchantStoreGroupEntity> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput, PageQueryInput pageQueryInput) {
        return merchantStoreGroupQueryRepository.getMerchantStoreGroupPage(merchantStoreGroupQueryInput, pageQueryInput);
    }
}
