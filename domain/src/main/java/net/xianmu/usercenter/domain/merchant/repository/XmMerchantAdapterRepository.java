package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;

/**
*
* <AUTHOR>
* @date 2023-10-23 13:47:58
* @version 1.0
*
*/
public interface XmMerchantAdapterRepository {

    XmMerchantAdapterEntity selectByStoreId(Long storeId);

    void createOrUpdate(XmMerchantAdapterEntity entity);

    /**
     * 根据主键修改，业务字段允许修改为null
     * @param entity
     */
    void update(XmMerchantAdapterEntity entity);
}