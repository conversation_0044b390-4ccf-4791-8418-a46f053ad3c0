package net.xianmu.usercenter.domain.merchant.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.common.enums.MerchantStoreEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class MerchantStoreEntity {

    // ------------ 门店 -----------
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0-直营店 1-加盟店 2-托管店 3-个人店 4-连锁 5-未知
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店4、拉黑 5、注销
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 账期开关 1开启 0关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付1开启0关闭
     */
    private Integer onlinePayment;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;
    /**
     * 门店下单有效期
     * 1=短期
     * 0=长期
     */
    private Integer placeOrderPermissionTimeLimited;
    /**
     * 门店下单失效日期
     */
    private LocalDateTime placeOrderPermissionExpiryTime;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 非现金支付权限 0、关闭 1开启
     */
    private Integer nonCashAuthority;




    // 下面是子域的信息，这里为了方便交互，并未收缩到一个更小的领域

    // ----------------------------- 账户相关 -----------------
    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 店员名称
     */
    private String accountName;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer accountType;


    /**
     * 账户状态
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer accountStatus;

    /**
     * 账户上次登录时间
     */
    private LocalDateTime lastLoginTime;

    // ------------------------- 地址相关 ---------------------------

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 收货地址
     */
    private String deliveryAddress;


    // --------------------START 鲜沐数据迁移 START------------------

    /**
     * 鲜沐merchant表id
     */
    private Long mId;

    /**
     * 门店经营类型
     */
    private String businessType;

    /**
     * 区域组织id
     */
    private Long regionalId;

    /**
     * 门店自己的邀请码，可用于邀请门店、子账号
     */
    private String channelCode;

    /**
     * 运营服务区域
     */
    private Integer areaNo;


    // ------------------ 拓展表 ---------------------

    /**
     * 弹框
     */
    private Integer popView;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    private Integer changePop;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    private Integer firstLoginPop;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 预注册标记.1-代表为预注册
     */
    private Integer preRegisterFlag;

    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;

    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    private Integer operateStatus;

    /**
     * 业务线:0=鲜沐;1=pop
     */
    private Integer businessLine;

    // -----------------区域组织相关--------------

    /**
     * 组织名称(大客户名称/saas品牌名称)
     */
    private String organizationName;

    /**
     * 鲜沐大客户id
     */
    private Long adminId;

    /**
     * 客户类型：1-大客户，2-单店
     */
    private Integer size;

    // --------------------END 鲜沐数据迁移 END------------------




    /**
     * 转换鲜沐门店的类型
     * 默认：未知
     *
     * @return
     */
    public static Integer transTypeFromXm(String sourceType) {
        if (StrUtil.isBlank(sourceType)) {
            return MerchantStoreEnums.Type.UN_KNOW.getCode();
        }
        Integer type;
        switch (sourceType) {
            case "个人店":
                type = MerchantStoreEnums.Type.PERSONAL.getCode();
                break;
            case "托管店":
                type = MerchantStoreEnums.Type.MANAGED.getCode();
                break;
            case "加盟店":
            case "连锁-加盟店":
                type = MerchantStoreEnums.Type.JOINING.getCode();
                break;
            case "直营店":
                type = MerchantStoreEnums.Type.DIRECT.getCode();
                break;
            case "连锁店":
                type = MerchantStoreEnums.Type.CHAIN.getCode();
                break;
            default:
                type = MerchantStoreEnums.Type.UN_KNOW.getCode();
        }
        return type;
    }

    /**
     * 转换鲜沐门店的状态
     * 默认：审核中
     *
     * @return
     */
    public static Integer transStatusFromXm(String sourceStatus) {
        if (StrUtil.isBlank(sourceStatus)) {
            return MerchantStoreEnums.Status.IN_AUDIT.getCode();
        }
        Integer status;
        switch (sourceStatus) {
            case "0":
                status = MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode();
                break;
            case "1":
                status = MerchantStoreEnums.Status.IN_AUDIT.getCode();
                break;
            case "2":
                status = MerchantStoreEnums.Status.AUDIT_FAIL.getCode();
                break;
            case "3":
                status = MerchantStoreEnums.Status.PULL_BLACK.getCode();
                break;
            case "4":
                status = MerchantStoreEnums.Status.CANCEL.getCode();
                break;
            default:
                status = MerchantStoreEnums.Status.IN_AUDIT.getCode();
        }
        return status;
    }






    public static MerchantStoreEntity buildCreateEntity(MerchantStoreCommandInput merchantStoreCommandInput) {
        if (merchantStoreCommandInput == null) {
            return null;
        }
        MerchantStoreEntity merchantStoreEntity = new MerchantStoreEntity();
        merchantStoreEntity.setTenantId(merchantStoreCommandInput.getTenantId());
        merchantStoreEntity.setStoreName(merchantStoreCommandInput.getStoreName());
        merchantStoreEntity.setType(merchantStoreCommandInput.getType());
        merchantStoreEntity.setRegisterTime(merchantStoreCommandInput.getRegisterTime() == null ? LocalDateTime.now() : merchantStoreCommandInput.getRegisterTime());
        // 默认免审
        merchantStoreEntity.setStatus(merchantStoreCommandInput.getStatus() == null ? MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode() : merchantStoreCommandInput.getStatus());
        merchantStoreEntity.setRemark(merchantStoreCommandInput.getRemark());
        merchantStoreEntity.setAuditRemark(merchantStoreCommandInput.getAuditRemark());
        merchantStoreEntity.setAuditTime(merchantStoreCommandInput.getAuditTime());
        merchantStoreEntity.setBillSwitch(merchantStoreCommandInput.getBillSwitch());
        merchantStoreEntity.setOnlinePayment(merchantStoreCommandInput.getOnlinePayment());
        merchantStoreEntity.setStoreNo(merchantStoreCommandInput.getStoreNo());
        merchantStoreEntity.setBalanceAuthority(merchantStoreCommandInput.getBalanceAuthority());
        merchantStoreEntity.setCreateTime(merchantStoreCommandInput.getCreateTime());
        merchantStoreEntity.setUpdateTime(merchantStoreCommandInput.getUpdateTime());
        merchantStoreEntity.setRegionalId(merchantStoreCommandInput.getRegionalId());
        merchantStoreEntity.setPlaceOrderPermissionTimeLimited(merchantStoreCommandInput.getPlaceOrderPermissionTimeLimited ());
        merchantStoreEntity.setPlaceOrderPermissionExpiryTime(merchantStoreCommandInput.getPlaceOrderPermissionExpiryTime ());
        merchantStoreEntity.setEnableOfflinePayment (merchantStoreCommandInput.getEnableOfflinePayment ());
        merchantStoreEntity.setNonCashAuthority (merchantStoreCommandInput.getNonCashAuthority ());
        return merchantStoreEntity;
    }


    public static MerchantStoreEntity buildCreateEntityForBinLog(MerchantStoreCommandInput merchantStoreCommandInput) {
        if (merchantStoreCommandInput == null) {
            return null;
        }
        MerchantStoreEntity merchantStoreEntity = new MerchantStoreEntity();
        merchantStoreEntity.setTenantId(merchantStoreCommandInput.getTenantId());
        merchantStoreEntity.setStoreName(merchantStoreCommandInput.getStoreName());
        merchantStoreEntity.setType(merchantStoreCommandInput.getType());
        merchantStoreEntity.setRegisterTime(merchantStoreCommandInput.getRegisterTime());
        merchantStoreEntity.setStatus(merchantStoreCommandInput.getStatus());
        merchantStoreEntity.setRemark(merchantStoreCommandInput.getRemark());
        merchantStoreEntity.setAuditRemark(merchantStoreCommandInput.getAuditRemark());
        merchantStoreEntity.setAuditTime(merchantStoreCommandInput.getAuditTime());
        merchantStoreEntity.setBillSwitch(merchantStoreCommandInput.getBillSwitch());
        merchantStoreEntity.setOnlinePayment(merchantStoreCommandInput.getOnlinePayment());
        merchantStoreEntity.setStoreNo(merchantStoreCommandInput.getStoreNo());
        merchantStoreEntity.setBalanceAuthority(merchantStoreCommandInput.getBalanceAuthority());
        merchantStoreEntity.setCreateTime(merchantStoreCommandInput.getCreateTime());
        merchantStoreEntity.setUpdateTime(merchantStoreCommandInput.getUpdateTime());
        merchantStoreEntity.setBusinessType(merchantStoreCommandInput.getBusinessType());
        merchantStoreEntity.setRegionalId(merchantStoreCommandInput.getRegionalId());
        merchantStoreEntity.setMId(merchantStoreCommandInput.getMId());
        merchantStoreEntity.setChannelCode(merchantStoreCommandInput.getChannelCode());
        merchantStoreEntity.setAreaNo(merchantStoreCommandInput.getAreaNo());
        return merchantStoreEntity;
    }



    public static void wrapManegeAccount(List<MerchantStoreEntity> entities, List<MerchantStoreAccountEntity> accountEntities){

        if(CollUtil.isEmpty(entities) || CollUtil.isEmpty(accountEntities)) {
            return;
        }


        Map<Long, List<MerchantStoreAccountEntity>> accountMap = accountEntities.stream().collect(Collectors.groupingBy(MerchantStoreAccountEntity::getStoreId));


        entities.forEach(merchant -> {
            List<MerchantStoreAccountEntity> accountList = accountMap.get(merchant.getId());
            if(CollUtil.isNotEmpty(accountList)) {
                // 先取正常的，如果正常的没有，那就已删除的店长
                accountList.sort(Comparator.comparing(MerchantStoreAccountEntity::getDeleteFlag, Comparator.reverseOrder()).thenComparing(MerchantStoreAccountEntity::getStoreId, Comparator.reverseOrder()));
                MerchantStoreAccountEntity account = accountList.get(0);
                merchant.setAccountName(account.getAccountName());
                merchant.setPhone(account.getPhone());
            } else {
                log.warn("门店没有正确的店长信息，storeId: {}, mId: {}", merchant.getId(), merchant.getMId());
            }
        });

    }

}