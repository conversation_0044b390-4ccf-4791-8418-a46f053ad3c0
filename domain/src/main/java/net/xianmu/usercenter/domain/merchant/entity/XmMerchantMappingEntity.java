package net.xianmu.usercenter.domain.merchant.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-06-27 10:54:08
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XmMerchantMappingEntity {
	/**
	 * 自增长主键
	 */
	private Long id;

	/**
	 * 鲜沐merchant表m_id
	 */
	private Long mId;

	/**
	 * 用户中心merchant_store表的store_id
	 */
	private Long storeId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}