package net.xianmu.usercenter.domain.tenant.repository;

import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 11:05
 */
public interface TenantAccountCommandRepository {

    void insertOrUpdate(TenantAccountEntity entity);


    /**
     * 根据租户id列表，手机号批量更新
     * @param tenantIds
     * @param entity
     */
    void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccountEntity entity);



    /**
     * 根据租户id列表，authId
     * @param tenantIds
     * @param entity
     */
    void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountEntity entity);



    /**
     * 移除账户
     */
    void remove(Long id);

}
