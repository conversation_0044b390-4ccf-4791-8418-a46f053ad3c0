package net.xianmu.usercenter.domain.invoice.repository;

import net.xianmu.usercenter.domain.invoice.entity.InvoiceConfigEntity;
import net.xianmu.usercenter.domain.invoice.entity.InvoiceMerchantRelationEntity;

/**
*
* <AUTHOR>
* @date 2023-07-13 13:58:41
* @version 1.0
*
*/
public interface InvoiceMerchantRelationRepository {

    InvoiceMerchantRelationEntity selectById(Long id);

    void createOrUpdate(InvoiceMerchantRelationEntity entity);
}