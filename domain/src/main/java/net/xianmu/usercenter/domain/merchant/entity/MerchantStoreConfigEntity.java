package net.xianmu.usercenter.domain.merchant.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-11 10:48:04
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreConfigEntity {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 弹框
	 */
	private Integer popView;

	/**
	 * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
	 */
	private Integer changePop;

	/**
	 * 首次登录弹窗：0、未弹 1、已弹
	 */
	private Integer firstLoginPop;

	/**
	 * 开关状态 0 开（展示） 1 关（不展示）
	 */
	private Integer displayButton;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 操作人名称
	 */
	private String updater;



	
}