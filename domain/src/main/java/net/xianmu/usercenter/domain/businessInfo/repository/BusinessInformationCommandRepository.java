package net.xianmu.usercenter.domain.businessInfo.repository;

import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:09
 */
public interface BusinessInformationCommandRepository {

    void insert(BusinessInformationCommandInput input);

    void insert(BusinessInformationEntity input);

    /**
     * 全量更新,可将字段更新为null
     * @param input
     */
    void updateByEntity(BusinessInformationEntity input);

    /**
     * 更新非空
     * @param input
     */
    void updateSelective(BusinessInformationEntity input);
}
