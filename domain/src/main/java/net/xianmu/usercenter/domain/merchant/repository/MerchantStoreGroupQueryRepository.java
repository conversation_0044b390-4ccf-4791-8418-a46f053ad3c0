package net.xianmu.usercenter.domain.merchant.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.MerchantStoreGroupQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreGroupEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:38
 */
public interface MerchantStoreGroupQueryRepository {


    /**
     * 根据门店id列表查询分组信息
     * tenantId 用于数据隔离，可为空。为空时不做条件筛选
     * @param
     * @return
     */
    List<MerchantStoreGroupEntity> selectByStoreIds(Long tenantId, List<Long> storeIdList);


    List<MerchantStoreGroupEntity> selectGroupByGroupIds(Long tenantId, List<Long> groupIdList);

    /**
     * 根据id查询默认分组
     * @param
     * @return
     */
    MerchantStoreGroupEntity selectDefaultGroup(Long tenantId);

    /**
     * 根据id查询
     * @param
     * @return
     */
    MerchantStoreGroupEntity selectById(Long id);

    /**
     * 根据name查询分组
     * 不模糊匹配
     * @param tenantId
     * @param name
     * @return
     */
    MerchantStoreGroupEntity selectByName(Long tenantId, String name);

    /**
     * 根据租户+name查询分组以及分组下的门店数
     * @param tenantId
     * @param name
     * @return
     */
    List<MerchantStoreGroupEntity> queryGroupsWithStoreCount(Long tenantId, String name);

    /**
     * 根据指定参数查询门店列表
     * @param
     * @return
     */
    List<MerchantStoreGroupEntity> selectByCondition(MerchantStoreGroupQueryInput input);


    PageInfo<MerchantStoreGroupEntity> getMerchantStoreGroupPage(MerchantStoreGroupQueryInput merchantStoreGroupQueryInput, PageQueryInput pageQueryInput);
}
