package net.xianmu.usercenter.domain.regional.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.common.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.common.input.command.RegionalOrganizationCommandInput;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-06 16:32:07
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionalOrganizationEntity {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 组织名称
	 */
	private String organizationName;

	/**
	 * 组织来源：0-saas,1-鲜沐
	 */
	private Integer source;

	/**
	 * 客户类型：1-大客户，2-单店, 3-saas品牌客户
	 */
	private Integer size;

	/**
	 * 组织状态： 0正常 1禁用
	 */
	private Integer status;

	/**
	 * 鲜沐大客户id
	 */
	private Long adminId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 操作人名称
	 */
	private String updater;

	public static RegionalOrganizationEntity buildCreateEntity(RegionalOrganizationCommandInput regionalOrganizationCommandInput) {
		RegionalOrganizationEntity regionalOrganizationEntity = new RegionalOrganizationEntity();
		regionalOrganizationEntity.setId(regionalOrganizationCommandInput.getId());
		regionalOrganizationEntity.setTenantId(regionalOrganizationCommandInput.getTenantId());
		regionalOrganizationEntity.setPhone(regionalOrganizationCommandInput.getPhone());
		regionalOrganizationEntity.setOrganizationName(regionalOrganizationCommandInput.getOrganizationName());
		regionalOrganizationEntity.setSource(regionalOrganizationCommandInput.getSource());
		regionalOrganizationEntity.setSize(regionalOrganizationCommandInput.getSize());
		regionalOrganizationEntity.setStatus(regionalOrganizationCommandInput.getStatus());
		regionalOrganizationEntity.setAdminId(regionalOrganizationCommandInput.getAdminId());
		regionalOrganizationEntity.setCreateTime(regionalOrganizationCommandInput.getCreateTime());
		regionalOrganizationEntity.setUpdateTime(regionalOrganizationCommandInput.getUpdateTime());
		regionalOrganizationEntity.setCreator(regionalOrganizationCommandInput.getCreator());
		regionalOrganizationEntity.setUpdater(regionalOrganizationCommandInput.getUpdater());
		return regionalOrganizationEntity;
	}


	public static RegionalOrganizationEntity buildCreateEntity(TenantCommandInput input) {
		RegionalOrganizationEntity regionalOrganizationEntity = new RegionalOrganizationEntity();
		regionalOrganizationEntity.setTenantId(input.getTenantId());
		regionalOrganizationEntity.setPhone(input.getLoginPhone());
		regionalOrganizationEntity.setOrganizationName(input.getMerchantName());
		regionalOrganizationEntity.setSource(RegionalOrganizationEnums.Source.SAAS.getCode());
		regionalOrganizationEntity.setSize(RegionalOrganizationEnums.Size.SAAS.getCode());
		regionalOrganizationEntity.setStatus(RegionalOrganizationEnums.Status.NORMAL.getCode());
		regionalOrganizationEntity.setAdminId(input.getAdminId());
		regionalOrganizationEntity.setCreator(input.getOpUname());
		regionalOrganizationEntity.setUpdater(input.getOpUname());
		return regionalOrganizationEntity;
	}


	
}