package net.xianmu.usercenter.domain.xianmu.datatransfer.repository;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-14 14:27:46
 */
public interface DataTransferRepository {

    Long countAdminAll();

    /**
     * id的开始位置，偏移量
     *
     * @param adminId
     * @param offset
     * @return
     */
    List<Map<String, Object>> selectAdminList(Long adminId, Integer offset);


    Long countMerchantAll();

    /**
     * id的开始位置，偏移量
     *
     * @param adminId
     * @param offset
     * @return
     */
    List<Map<String, Object>> selectMerchantList(Long adminId, Integer offset);



    List<Map<String, Object>> selectMerchantListByIdList(List<Long> idList);

    /**
     * id的开始位置，偏移量
     *
     * @param adminId
     * @param offset
     * @return
     */
    List<Map<String, Object>> selectMerchantAccountList(Long adminId, Integer offset);


    List<Map<String, Object>> selectMerchantAccountListByIdList(List<Long> idList);



    Long countContactAll();

    /**
     * id的开始位置，偏移量
     *
     * @param adminId
     * @param offset
     * @return
     */
    List<Map<String, Object>> selectContactList(Long adminId, Integer offset);

    List<Map<String, Object>> selectContactListListByIdList(List<Long> idList);

}