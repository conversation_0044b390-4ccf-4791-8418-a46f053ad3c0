package net.xianmu.usercenter.domain.merchant.entity;


import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.MerchantAddressEnums;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 13:50:22
 */
@Data
public class MerchantAddressEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 们拍好
     */
    private String houseNumber;

    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 是否是默认地址：0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
     */
    private Integer status;


    /**
     * 鲜沐contact联系人表id
     */
    private Long xmContactId;

    /**
     * 鲜沐merchant表id,冗余字段便于鲜沐接入
     */
    private Long mId;


    /**
     * 审核备注
     */
    private String remark;

    /**
     * 地址备注
     */
    private String addressRemark;





    // 联系人相关

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 完整收货地址
     */
    private String deliveryAddress;

    /**
     * 地址对应的联系人
     */
    private List<MerchantContactEntity> contactList;







    /**
     * 仅更新非空
     * @param input
     */
    public void buildUpdateEntity(MerchantAddressCommandInput input) {
        this.province = input.getProvince() == null ? province : input.getProvince();
        this.city = input.getCity() == null ? city : input.getCity();
        this.area = input.getArea() == null ? area : input.getArea();
        this.address = input.getAddress() == null ? address : input.getAddress();
        this.houseNumber = input.getHouseNumber() == null ? houseNumber : input.getHouseNumber();
        this.poiNote = input.getPoiNote() == null ? poiNote : input.getPoiNote();
        this.status = input.getStatus() == null ? status : input.getStatus();
        this.defaultFlag = input.getDefaultFlag() == null ? defaultFlag : input.getDefaultFlag();
        this.addressRemark = input.getAddressRemark() == null ? addressRemark : input.getAddressRemark();
    }


    public static MerchantAddressEntity buildCreateEntity(MerchantAddressCommandInput input) {
        MerchantAddressEntity addressEntity = new MerchantAddressEntity();
        addressEntity.setTenantId(input.getTenantId());
        addressEntity.setStoreId(input.getStoreId());
        addressEntity.setProvince(input.getProvince());
        addressEntity.setCity(input.getCity());
        addressEntity.setArea(input.getArea());
        addressEntity.setAddress(input.getAddress());
        addressEntity.setHouseNumber(input.getHouseNumber());
        addressEntity.setPoiNote(input.getPoiNote());
        addressEntity.setDefaultFlag(input.getDefaultFlag() == null ? MerchantAddressEnums.DefaultFlag.NOT_DEFAULT.getCode() : input.getDefaultFlag());
        // 默认免审
        addressEntity.setStatus(input.getStatus() == null ? MerchantAddressEnums.status.NORMAL.getCode() : input.getStatus());
        addressEntity.setAddressRemark(input.getAddressRemark());
        return addressEntity;
    }


    public static MerchantAddressEntity converterToEntityForBinLog(Map<String, String> data) {
        MerchantAddressEntity addressEntity = new MerchantAddressEntity();
        addressEntity.setTenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID);
        //addressEntity.setStoreId(input.getStoreId());
        addressEntity.setProvince(data.get("province"));
        addressEntity.setCity(data.get("city"));
        addressEntity.setArea(data.get("area"));
        addressEntity.setAddress(data.get("address"));
        addressEntity.setHouseNumber(data.get("house_number"));
        addressEntity.setPoiNote(data.get("poi_note"));
        addressEntity.setDefaultFlag(Integer.valueOf(data.get("is_default")));
        addressEntity.setStatus(Integer.valueOf(data.get("status")));
        addressEntity.setXmContactId(Long.valueOf(data.get("contact_id")));
        addressEntity.setRemark(data.get("remark"));
        addressEntity.setAddressRemark(data.get("address_remark"));
        addressEntity.setMId(Long.valueOf(data.get("m_id")));
        return addressEntity;
    }

    public static void warpMerchantAddressEntity (List<MerchantAddressEntity> addressEntities, List<MerchantContactEntity> contactEntities) {
        if(CollUtil.isEmpty(addressEntities) || CollUtil.isEmpty(contactEntities)) {
            return;
        }

        Map<Long, List<MerchantContactEntity>> collect = contactEntities.stream().collect(Collectors.groupingBy(MerchantContactEntity::getAddressId));
        addressEntities.forEach(a -> a.setContactList(collect.get(a.getId())));
    }

}