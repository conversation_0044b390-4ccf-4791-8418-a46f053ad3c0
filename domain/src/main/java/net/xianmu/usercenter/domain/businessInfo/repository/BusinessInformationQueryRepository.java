package net.xianmu.usercenter.domain.businessInfo.repository;

import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:10
 */

public interface BusinessInformationQueryRepository {

    BusinessInformationEntity getBusinessInfoByBizIdAndType(Long bizId, Integer type);

    BusinessInformationEntity selectById(Long id);

    List<BusinessInformationEntity> selectByCondition(BusinessInformationQueryInput req);
}
