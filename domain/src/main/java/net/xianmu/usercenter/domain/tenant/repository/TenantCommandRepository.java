package net.xianmu.usercenter.domain.tenant.repository;

import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TenantCommandRepository {

    TenantEntity createOrUpdate(TenantEntity entity);

    /**
     * 批量修改操作人
     *
     * @param ids
     * @param opUname
     * @param opUid
     */
    void batchUpdateOp(@Param("ids") List<Long> ids, @Param("opUname") String opUname, @Param("opUid") Long opUid);
}
