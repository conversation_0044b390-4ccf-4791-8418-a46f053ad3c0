package net.xianmu.usercenter.domain.merchant.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-07-11 16:46:09
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XmMerchantAccountMappingEntity {
	/**
	 * 自增长主键
	 */
	private Long id;

	/**
	 * 鲜沐merchant_sub_account表id
	 */
	private Long xmAccountId;

	/**
	 * 用户中心merchant_store_account表的id
	 */
	private Long accountId;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}