package net.xianmu.usercenter.domain.regional.domain;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationCommandRepository;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @Title: 区域组织表业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-07-06 16:32:07
 * @version 1.0
 *
 */
@Service
@Slf4j
public class RegionalOrganizationDomainService {

    @Autowired
    private RegionalOrganizationQueryRepository regionalOrganizationQueryRepository;

    @Autowired
    private RegionalOrganizationCommandRepository regionalOrganizationCommandRepository;


    public RegionalOrganizationEntity create(TenantCommandInput input) {
        RegionalOrganizationEntity entity = RegionalOrganizationEntity.buildCreateEntity(input);
        return this.create(entity);
    }

    public RegionalOrganizationEntity create(RegionalOrganizationEntity entity) {
        return regionalOrganizationCommandRepository.createOrUpdate(entity);
    }

    public RegionalOrganizationEntity createForBinLog(RegionalOrganizationEntity input) {
        return regionalOrganizationCommandRepository.createOrUpdate(input);
    }


    public boolean updateForBinLog(RegionalOrganizationEntity input) {
        regionalOrganizationCommandRepository.createOrUpdate(input);
        return true;
    }
}
