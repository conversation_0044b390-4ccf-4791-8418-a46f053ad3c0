package net.xianmu.usercenter.domain.merchant.domain;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.usercenter.common.enums.MerchantAddressEnums;
import net.xianmu.usercenter.common.input.command.MerchantAddressCommandInput;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantAddressQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class MerchantAddressDomainService {

    @Autowired
    private MerchantContactDomainService merchantContactDomainService;
    @Autowired
    private MerchantAddressCommandRepository merchantAddressCommandRepository;
    @Autowired
    private MerchantAddressQueryRepository merchantAddressQueryRepository;


    public Long create(MerchantAddressCommandInput input) {
        log.info("领域层接收到新建门店地址请求：input:{}", JSON.toJSONString(input));
        this.paramValidate(input);

        // 门店地址
        MerchantAddressEntity addressEntity = MerchantAddressEntity.buildCreateEntity(input);
        // 处理默认地址
        this.handleDefaultAddress(addressEntity);

        addressEntity = merchantAddressCommandRepository.createOrUpdate(addressEntity);
        Long addressId = addressEntity.getId();

        // 联系人
        List<MerchantContactCommandInput> merchantContactList = input.getMerchantContactList();
        if (CollUtil.isNotEmpty(merchantContactList)) {
            merchantContactList.forEach(entity -> entity.setAddressId(addressId));
            this.handleContact(merchantContactList);
        }
        return addressId;
    }


    public void createAddressForMerchantBatchCreate(List<MerchantAddressCommandInput> inputList) {
        log.info("领域层接收到批量新建门店地址请求：input:{}", JSON.toJSONString(inputList));
        if(CollUtil.isEmpty(inputList)) {
            throw new ParamsException("门店地址信息为空!");
        }

        inputList.forEach(input -> {
            MerchantAddressEntity addressEntity = MerchantAddressEntity.buildCreateEntity(input);
            addressEntity = merchantAddressCommandRepository.createOrUpdate(addressEntity);
            Long addressId = addressEntity.getId();
            // 联系人
            List<MerchantContactCommandInput> merchantContactList = input.getMerchantContactList();
            if (CollUtil.isNotEmpty(merchantContactList)) {
                merchantContactList.forEach(entity -> entity.setAddressId(addressId));
            }
            merchantContactDomainService.createContactForMerchantBatchCreate(merchantContactList);
        });
    }






    public boolean update(MerchantAddressCommandInput input) {
        log.info("领域层接收到修改门店地址请求：input:{}", JSON.toJSONString(input));
        this.paramValidate(input);

        // 更新门店
        MerchantAddressEntity addressEntity = merchantAddressQueryRepository.selectById(input.getId());
        if (null == addressEntity) {
            log.error("门店地址不存在!, addressId:{}", input.getId());
            throw new BizException("门店地址不存在!");
        }
        // 构建更新参数
        addressEntity.buildUpdateEntity(input);
        // 处理默认地址
        this.handleDefaultAddress(addressEntity);
        merchantAddressCommandRepository.createOrUpdate(addressEntity);

        // 是否需要这样的交互？更新联系人列表
        this.handleContact(input.getMerchantContactList());
        return true;
    }


    /**
     * todo :这里目前的循环插入是如果存在较多的联系人会存在性能问题，不过当前鲜沐都是单联系人
     */
    private void handleContact(List<MerchantContactCommandInput> contactCommandInputList) {
        if (CollUtil.isEmpty(contactCommandInputList)) {
            return;
        }
        contactCommandInputList.forEach(input -> {
            if (null == input.getId()) {
                merchantContactDomainService.create(input);
            } else {
                merchantContactDomainService.update(input);
            }
        });
    }


    /**
     * 状态为正常地默认地址只能有一个
     */
    private void handleDefaultAddress(MerchantAddressEntity entity) {
        if (MerchantAddressEnums.DefaultFlag.NOT_DEFAULT.getCode().equals(entity.getDefaultFlag())) {
            return;
        }
        if (!MerchantAddressEnums.status.NORMAL.getCode().equals(entity.getStatus())) {
            return;
        }

        final MerchantAddressEntity addressEntity = merchantAddressQueryRepository.selectDefaultAddress(entity.getTenantId(), entity.getStoreId());
        // 当前存在状态为正常的默认地址，并且不是自己
        if (Objects.nonNull(addressEntity) && !Objects.equals(addressEntity.getId(), entity.getId())) {
            addressEntity.setDefaultFlag(MerchantAddressEnums.DefaultFlag.NOT_DEFAULT.getCode());
            merchantAddressCommandRepository.createOrUpdate(addressEntity);
        }
    }

    private void paramValidate(MerchantAddressCommandInput input) {
        ValidateUtil.paramValidate(input, "storeId", "tenantId");
        if (!ValidateUtil.isAddress(input.getAddress()) || !ValidateUtil.isAddress(input.getProvince()) || !ValidateUtil.isAddress(input.getCity())) {
            throw new BizException("门店地址不符合条件");
        }
        if (!StringUtils.isEmpty(input.getHouseNumber()) && !ValidateUtil.isHouseNumber(input.getHouseNumber())) {
            throw new BizException("门牌号不符合条件");
        }
        // 地址备注格式校验？ todo
    }


    public boolean remove(MerchantAddressCommandInput input) {
        ValidateUtil.paramValidate(input, "id");
        MerchantAddressEntity addressEntity = merchantAddressQueryRepository.selectById(input.getId());
        if (null == addressEntity) {
            log.error("门店地址不存在,无需删除, addressId:{}", input.getId());
            return false;
        }

        if (MerchantAddressEnums.status.DELETED.getCode().equals(addressEntity.getStatus())) {
            log.info("门店地址已删除，无需重复操作。addressEntity：{}", JSON.toJSONString(addressEntity));
            return true;
        } else if (MerchantAddressEnums.status.NORMAL.getCode().equals(addressEntity.getStatus())) {
            // 门店必须有一个状态正常的地址
            Long storeId = addressEntity.getStoreId();
            Long tenantId = addressEntity.getTenantId();
            MerchantAddressQueryInput build = MerchantAddressQueryInput.builder().tenantId(tenantId).storeId(storeId).status(MerchantAddressEnums.status.NORMAL.getCode()).build();
            final List<MerchantAddressEntity> merchantAddressEntities = merchantAddressQueryRepository.selectByCondition(build);
            if (merchantAddressEntities.size() == 1) {
                log.error("当前门店仅有一个可用地址，不能删除！addressEntity:{}", JSON.toJSONString(addressEntity));
                throw new BizException("当前门店仅有一个可用地址，不能删除！");
            }
        }

        // 逻辑删除
        addressEntity.setStatus(MerchantAddressEnums.status.DELETED.getCode());
        merchantAddressCommandRepository.createOrUpdate(addressEntity);
        return true;
    }

/*    public MerchantAddressEntity getDefaultMerchantAddress(MerchantAddressQueryInput req) {
        ValidateUtil.paramValidate(req);
        Long storeId = req.getStoreId();
        Long tenantId = req.getTenantId();
        if (null == storeId) {
            log.warn("请求参数门店id为空!");
            return null;
        }

        MerchantAddressQueryInput queryInput = MerchantAddressQueryInput.builder()
                .storeId(storeId)
                .tenantId(tenantId)
                .status(MerchantAddressEnums.status.NORMAL.getCode())
                .defaultFlag(MerchantAddressEnums.DefaultFlag.DEFAULT.getCode())
                .build();
        List<MerchantAddressEntity> merchantAddressEntities = merchantAddressQueryRepository.selectByCondition(queryInput);
        if (CollUtil.isNotEmpty(merchantAddressEntities)) {
            return merchantAddressEntities.get(0);
        }

        // 如果没有默认地址，从所有正常的地址中选最近添加一条
        queryInput = MerchantAddressQueryInput.builder()
                .storeId(storeId)
                .tenantId(tenantId)
                .status(MerchantAddressEnums.status.NORMAL.getCode())
                .build();
        merchantAddressEntities = merchantAddressQueryRepository.selectByCondition(queryInput);
        if (CollUtil.isEmpty(merchantAddressEntities)) {
            log.warn("门店数据异常：门店{}无可用地址", storeId);
            return null;
        }
        CollUtil.sort(merchantAddressEntities, Comparator.comparing(MerchantAddressEntity::getCreateTime, Comparator.reverseOrder()));
        return merchantAddressEntities.get(0);
    }*/



    public MerchantAddressEntity createOrUpdateFotBinLog(MerchantAddressEntity merchantAddressEntity) {
        return merchantAddressCommandRepository.createOrUpdate(merchantAddressEntity);
    }

}
