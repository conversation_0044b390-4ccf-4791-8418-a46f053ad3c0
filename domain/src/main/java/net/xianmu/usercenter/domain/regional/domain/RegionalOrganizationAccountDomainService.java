package net.xianmu.usercenter.domain.regional.domain;

import net.xianmu.usercenter.common.input.command.RegionalOrganizationCommandInput;
import net.xianmu.usercenter.domain.regional.entity.RegionalOrganizationEntity;
import net.xianmu.usercenter.domain.regional.repository.RegionalOrganizationCommandRepository;
import org.springframework.stereotype.Service;

/**
 *
 * @Title: 区域组织账户表业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-07-06 16:46:41
 * @version 1.0
 *
 */
@Service
public class RegionalOrganizationAccountDomainService {


}
