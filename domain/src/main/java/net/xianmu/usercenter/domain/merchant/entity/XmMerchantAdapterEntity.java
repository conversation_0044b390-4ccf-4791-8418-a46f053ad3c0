package net.xianmu.usercenter.domain.merchant.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.util.MapUtil;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-10-23 13:47:58
 * @version 1.0
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XmMerchantAdapterEntity {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 1-账期，2-现结
	 */
	private Integer direct;

	/**
	 * 创建人名称
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 操作人名称
	 */
	private String updater;



	public static XmMerchantAdapterEntity buildCreateEntityForBinLog(MerchantStoreCommandInput merchantStoreInput){
		XmMerchantAdapterEntity entity = new XmMerchantAdapterEntity();
		entity.setTenantId(merchantStoreInput.getTenantId());
		entity.setDirect(merchantStoreInput.getDirect());
		entity.setCreateTime(LocalDateTime.now());
		entity.setUpdateTime(LocalDateTime.now());
		entity.setCreator(merchantStoreInput.getOpName());
		return entity;
	}



	public static XmMerchantAdapterEntity buildCreateEntityForBinLog(Map<String, String> data){
		return XmMerchantAdapterEntity.builder()
				.direct(MapUtil.getInteger(data, "direct"))
				.build();
	}


	
}