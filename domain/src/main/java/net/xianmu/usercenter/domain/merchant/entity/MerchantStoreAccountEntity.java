package net.xianmu.usercenter.domain.merchant.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.enums.login.UserStatusEnum;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.input.user.AuthUserUpdateInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.common.util.DateUtil;

import javax.swing.border.EmptyBorder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreAccountEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 账户状态 0、审核中 1、审核通过 2、审核拒绝 3、关店 4-注销
     */
    private Integer status;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 0 已删除 1 正常使用
     */
    private Integer deleteFlag;


    /**
     * 鲜沐merchant表id(冗余字段，用于业务兼容)
     */
    private Long mId;

    /**
     * 鲜沐merchant_sub_account表id(冗余字段，用于业务兼容)
     */
    private Long xmAccountId;

    /**
     * 公众号OpenId
     */
    private String oaOpenId;

    /**
     * 登录账号名称
     */
    private String username;


    public static MerchantStoreAccountEntity buildCreateEntity(MerchantStoreAccountCommandInput input) {
        MerchantStoreAccountEntity storeAccount = new MerchantStoreAccountEntity();
        storeAccount.setTenantId(input.getTenantId());
        storeAccount.setStoreId(input.getStoreId());
        storeAccount.setAccountName(input.getAccountName());
        storeAccount.setPhone(input.getPhone());
        storeAccount.setType(input.getType());
        storeAccount.setRegisterTime(LocalDateTime.now());
        storeAccount.setOpenId(input.getOpenId());
        storeAccount.setUnionId(input.getUnionId());
        storeAccount.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        storeAccount.setStatus(input.getStatus() == null ? MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode() : input.getStatus());
        storeAccount.setUsername(input.getUsername());
        return storeAccount;
    }



    public static MerchantStoreAccountEntity buildUpdateEntity(MerchantStoreAccountCommandInput merchantStoreAccountCommandInput) {
        if (merchantStoreAccountCommandInput == null) {
            return null;
        }
        MerchantStoreAccountEntity merchantStoreAccountEntity = new MerchantStoreAccountEntity();
        merchantStoreAccountEntity.setId(merchantStoreAccountCommandInput.getId());
        merchantStoreAccountEntity.setAccountName(merchantStoreAccountCommandInput.getAccountName());
        merchantStoreAccountEntity.setPhone(merchantStoreAccountCommandInput.getPhone());
        merchantStoreAccountEntity.setType(merchantStoreAccountCommandInput.getType());
        merchantStoreAccountEntity.setRegisterTime(merchantStoreAccountCommandInput.getRegisterTime());
        merchantStoreAccountEntity.setAuditTime(merchantStoreAccountCommandInput.getAuditTime());
        merchantStoreAccountEntity.setOpenId(merchantStoreAccountCommandInput.getOpenId());
        merchantStoreAccountEntity.setUnionId(merchantStoreAccountCommandInput.getUnionId());
        merchantStoreAccountEntity.setUpdateTime(merchantStoreAccountCommandInput.getUpdateTime());
        merchantStoreAccountEntity.setStatus(merchantStoreAccountCommandInput.getStatus());
        merchantStoreAccountEntity.setLastLoginTime(merchantStoreAccountCommandInput.getLastLoginTime());
        merchantStoreAccountEntity.setDeleteFlag(merchantStoreAccountCommandInput.getDeleteFlag());
        merchantStoreAccountEntity.setOaOpenId(merchantStoreAccountCommandInput.getOaOpenId());
// Not mapped TO fields:
// mId
// Not mapped FROM fields:
// systemOrigin
        return merchantStoreAccountEntity;
    }



    /**
     * binlog 数据转换
     * @param data
     * @return
     */
    public static MerchantStoreAccountEntity converterToEntityForBinLog(Map<String, String> data){
        MerchantStoreAccountEntity storeAccount = new MerchantStoreAccountEntity();
        storeAccount.setTenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID);
        // storeAccount.setStoreId(input.getStoreId());
        storeAccount.setAccountName(data.get("contact"));
        storeAccount.setPhone(data.get("phone"));
        storeAccount.setType(Integer.valueOf(data.get("type")));
        storeAccount.setRegisterTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("register_time"), null));
        storeAccount.setAuditTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("audit_time"), null));
        storeAccount.setOpenId(data.get("mp_openid"));
        storeAccount.setUnionId(data.get("unionid"));
        storeAccount.setOaOpenId(data.get("openid"));
        storeAccount.setStatus(transStatusFromXm(data.get("status")));
        storeAccount.setCreateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), null));
        storeAccount.setUpdateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), null));
        storeAccount.setLastLoginTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("login_time"), null));
        storeAccount.setDeleteFlag(Integer.valueOf(data.get("delete_flag")));
        storeAccount.setMId(Long.valueOf(data.get("m_id")));
        storeAccount.setXmAccountId(Long.valueOf(data.get("account_id")));
        return storeAccount;
    }


    public static UserBase buildUserBase(MerchantStoreAccountEntity accountEntity){
        UserBase base = new UserBase();
        base.setPhone(accountEntity.getPhone());
        base.setUsername(StrUtil.isBlank(accountEntity.getUsername()) ? accountEntity.getPhone() : accountEntity.getUsername());
        base.setTenantId(accountEntity.getTenantId());
        base.setStatus(converterStatusForAuth(accountEntity.getDeleteFlag()));

        // 生成并设置密码
        String password = generatePassword(accountEntity);
        base.setPassword(password);

        return base;
    }

    /**
     * 根据账户信息生成密码
     * 规则：
     * - 手机号格式：租户ID_手机号后6位
     * - 邮箱格式：租户ID_@符号前的内容
     * - 默认情况：租户ID_用户名后6位
     */
    private static String generatePassword(MerchantStoreAccountEntity accountEntity) {
        Long tenantId = accountEntity.getTenantId();
        String username = StrUtil.isBlank(accountEntity.getUsername()) ? accountEntity.getPhone() : accountEntity.getUsername();

        if (StrUtil.isBlank(username)) {
            username = accountEntity.getPhone();
        }

        // 判断是否为手机号格式
        if (isPhoneNumber(username)) {
            // 手机号格式：租户ID_手机号后6位
            String phoneSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
            return tenantId + "_" + phoneSuffix;
        }

        // 判断是否为邮箱格式
        if (isEmail(username)) {
            // 邮箱格式：租户ID_@符号前的内容
            String emailPrefix = username.substring(0, username.indexOf('@'));
            return tenantId + "_" + emailPrefix;
        }

        // 默认情况：按照手机号逻辑处理（取用户名后6位）
        String usernameSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
        return tenantId + "_" + usernameSuffix;
    }

    /**
     * 判断是否为手机号格式
     */
    private static boolean isPhoneNumber(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        return str.matches("^1[0-9]\\d{9}$");
    }

    /**
     * 判断是否为邮箱格式
     */
    private static boolean isEmail(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }


    public static BaseUserExtend buildExtend(MerchantStoreAccountEntity accountEntity){
        BaseUserExtend base = new BaseUserExtend();
        base.setBizUserId(accountEntity.getId());
        base.setMpOpenid(accountEntity.getOpenId());
        base.setOpenid(accountEntity.getOaOpenId());
        base.setUnionId(accountEntity.getUnionId());
        base.setMpUnionId(accountEntity.getUnionId());
        base.setAuditStatus(accountEntity.getStatus());
        base.setLastLoginTime(DateUtil.toDate(accountEntity.getLastLoginTime()));
        return base;
    }

    private static Integer converterStatusForAuth(Integer deleteFlag) {
        if(null == deleteFlag) {
            return null;
        }
        if(MerchantAccountEnums.DeleteFlag.DELETED.getCode().equals(deleteFlag)) {
            return UserStatusEnum.IN_VALID.getStatus();
        }
        return UserStatusEnum.VALID.getStatus();
    }

    public static AuthUserUpdateInput buildAuthUserUpdateInput(MerchantStoreAccountEntity accountEntity) {
        AuthUserUpdateInput input = new AuthUserUpdateInput();
        input.setBizId(accountEntity.getId());
        input.setTenantId(accountEntity.getTenantId());
        input.setPhone(accountEntity.getPhone());
        input.setStatus(converterStatusForAuth(accountEntity.getDeleteFlag()));
        input.setAuditStatus(accountEntity.getStatus());
        input.setOpenId(accountEntity.getOaOpenId());
        return input;
    }

    public static Integer transStatusFromXm(String sourceStatus) {
        if (StrUtil.isBlank(sourceStatus)) {
            return MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode();
        }
        Integer status;
        switch (sourceStatus) {
            case "0":
                status = MerchantAccountEnums.Status.AUDITING.getCode();
                break;
            case "1":
                status = MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode();
                break;
            case "2":
                status = MerchantAccountEnums.Status.CANCEL.getCode();
                break;
            default:
                status = MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode();
        }
        return status;
    }


    public static void warpEntityFromAuth(List<AuthUserResp> sourceList, List<MerchantStoreAccountEntity> targetList){
        if(CollUtil.isEmpty(sourceList) || CollUtil.isEmpty(targetList)) {
            return;
        }

        Map<Long, AuthUserResp> collect = sourceList.stream().collect(Collectors.toMap(AuthUserResp::getBizUserId, Function.identity()));
        AuthUserResp temp;
        for (MerchantStoreAccountEntity entity : targetList) {
            // 鲜沐数据
            temp = TenantDefaultConstant.XIAN_MU_TENANT_ID.equals(entity.getTenantId()) ? collect.get(entity.getXmAccountId()) : collect.get(entity.getId());
            if(null != temp) {
                entity.setOpenId(temp.getMpOpenid());
                entity.setOaOpenId(temp.getOpenid());
                entity.setUnionId(temp.getUnionid());
                entity.setLastLoginTime(DateUtil.toLocalDateTime(temp.getLastLoginTime()));
                entity.setUsername(temp.getUsername());
            }
        }
    }
}