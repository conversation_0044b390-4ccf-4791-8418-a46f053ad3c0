package net.xianmu.usercenter.domain.tenant.param.command;

import lombok.Data;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.purview.TenantPrivilegesInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.domain.tenant.valueobject.TenantPrivilegesValueObject;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 11:03
 */
@Data
public class TenantAccountCommandInput {

    private Long id;

    /**
     * auth用户Id
     */
    private Long authUserId;

    /**
     * 品牌方Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 登陆密码
     */
    private String loginPassword;
    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 角色Id
     */
    private List<Long> roleIds;

    /**
     * 0有效1失效
     */
    private Integer status;


    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * 操作人authUserId
     */
    private Long opAuthUserId;

    /**
     * 系统来源,默认oms
     */
    private Integer systemOrigin;

    /**
     * 权益列表
     */
    private List<TenantPrivilegesValueObject> tenantPrivilegesList;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;

    /**
     * 操作人
     */
    private String updater;



    public static UserBase toUserBase(TenantAccountCommandInput tenantAccountCommandInput) {
        if (tenantAccountCommandInput == null) {
            return null;
        }
        UserBase userBase = new UserBase();
        userBase.setId(tenantAccountCommandInput.getAuthUserId());
        userBase.setLoginPassword(tenantAccountCommandInput.getLoginPassword());
        userBase.setNickname(tenantAccountCommandInput.getNickname());
        userBase.setPhone(tenantAccountCommandInput.getPhone());
        userBase.setEmail(tenantAccountCommandInput.getEmail());
        userBase.setTenantId(tenantAccountCommandInput.getTenantId());
        userBase.setSystemOrigin(tenantAccountCommandInput.getSystemOrigin());
        userBase.setStatus(tenantAccountCommandInput.getStatus());
        userBase.setRoleIds(tenantAccountCommandInput.getRoleIds());
        String username = tenantAccountCommandInput.getAccountLoginType() == 0 ? tenantAccountCommandInput.getPhone() : tenantAccountCommandInput.getEmail();
        userBase.setUsername(username);
        userBase.setPassword(tenantAccountCommandInput.getLoginPassword());
        return userBase;
    }

    public static BaseUserExtend toUserBaseExtend(TenantAccountCommandInput tenantAccountCommandInput) {
        if (tenantAccountCommandInput == null) {
            return null;
        }
        BaseUserExtend extend = new BaseUserExtend();
        extend.setAuthEquityInputs(toTenantPrivilegesInputList(tenantAccountCommandInput.getTenantPrivilegesList()));
        return extend;
    }


    public static List<TenantPrivilegesInput> toTenantPrivilegesInputList(List<TenantPrivilegesValueObject> tenantPrivilegesValueObjectList) {
        if (tenantPrivilegesValueObjectList == null) {
            return Collections.emptyList();
        }
        List<TenantPrivilegesInput> tenantPrivilegesInputList = new ArrayList<>();
        for (TenantPrivilegesValueObject tenantPrivilegesValueObject : tenantPrivilegesValueObjectList) {
            tenantPrivilegesInputList.add(toTenantPrivilegesInput(tenantPrivilegesValueObject));
        }
        return tenantPrivilegesInputList;
    }

    public static TenantPrivilegesInput toTenantPrivilegesInput(TenantPrivilegesValueObject tenantPrivilegesValueObject) {
        if (tenantPrivilegesValueObject == null) {
            return null;
        }
        TenantPrivilegesInput tenantPrivilegesInput = new TenantPrivilegesInput();
        tenantPrivilegesInput.setMenuId(tenantPrivilegesValueObject.getMenuId());
        tenantPrivilegesInput.setExpireTime(tenantPrivilegesValueObject.getExpireTime());
        return tenantPrivilegesInput;
    }



}
