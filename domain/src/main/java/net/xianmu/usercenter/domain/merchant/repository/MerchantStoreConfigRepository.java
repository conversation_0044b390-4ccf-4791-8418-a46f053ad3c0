package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreConfigEntity;

/**
*
* <AUTHOR>
* @date 2023-07-10 17:23:10
* @version 1.0
*
*/
public interface MerchantStoreConfigRepository {

    MerchantStoreConfigEntity selectByStoreId(Long storeId);

    MerchantStoreConfigEntity createOrUpdate(MerchantStoreConfigEntity entity);

    void updateSelective(MerchantStoreConfigEntity entity);

}