package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 13:50:22
* @version 1.0
*
*/
public interface MerchantAddressCommandRepository {


    /**
     * 按实体更新
     * @param
     */
    MerchantAddressEntity createOrUpdate(MerchantAddressEntity entity);


    /**
     * 更新非空
     * @param
     */
    void updateSelectiveBatch(List<MerchantAddressEntity> entities);


    /**
     * 物理删除
     * @param
     */
    Boolean delete(Long id);


}