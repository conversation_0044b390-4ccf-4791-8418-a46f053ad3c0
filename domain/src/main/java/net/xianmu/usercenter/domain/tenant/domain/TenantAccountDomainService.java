package net.xianmu.usercenter.domain.tenant.domain;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.enums.TenantAccountLoginTypeEnum;
import net.xianmu.usercenter.domain.tenant.entity.TenantEntity;
import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;
import net.xianmu.usercenter.common.util.StringUtils;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;
import net.xianmu.usercenter.common.enums.TenantAccountEnums;
import net.xianmu.usercenter.domain.tenant.repository.TenantAccountCommandRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantAccountQueryRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantQueryRepository;
import net.xianmu.usercenter.facade.auth.AuthUserFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:37
 */
@Service
@Slf4j
public class TenantAccountDomainService {

    @Autowired
    private TenantAccountQueryRepository tenantAccountQueryRepository;

    @Autowired
    private TenantQueryRepository tenantQueryRepository;

    @Autowired
    private TenantAccountCommandRepository tenantAccountCommandRepository;

    @Autowired
    private AuthUserFacade authUserFacade;


    public TenantAccountEntity selectTenantAccountByAuthUserId(Long authUserId) {
        return tenantAccountQueryRepository.selectByAuthUserId(authUserId);
    }


    public TenantAccountEntity selectTenantAccountById(Long id) {
        return tenantAccountQueryRepository.selectById(id);
    }

    public PageInfo<TenantAccountEntity> getTenantAccountsPage(TenantAccountListQueryInput req, PageQueryInput pageQueryInput) {
        ValidateUtil.paramValidate(req, "tenantId");
        return tenantAccountQueryRepository.getTenantAccountPage(req, pageQueryInput);
    }


    public Long create(TenantAccountCommandInput req) {
        log.info("接收到创建租户账户请求，req:{}", JSON.toJSONString(req));
        TenantEntity tenantEntity = tenantQueryRepository.getTenantById(req.getTenantId());
        TenantAccountEntity entity;

        if(TenantAccountLoginTypeEnum.PHONE_LOGIN.getCode().equals(tenantEntity.getAccountLoginType())) {
            if(StrUtil.isBlank(req.getPhone())) {
                throw new BizException("手机号不能为空");
            }
            entity = tenantAccountQueryRepository.selectByPhoneAndTenantId(req.getTenantId(), req.getPhone());
        } else {
            if(StrUtil.isBlank(req.getEmail())) {
                throw new BizException("邮箱不能为空!");
            }
            entity = tenantAccountQueryRepository.selectByEmailAndTenantId(req.getTenantId(), req.getEmail());
        }


        String password = req.getLoginPassword();
        if(null == password) {
            password = StringUtils.createInitPassword();
        }

        if (null == entity) {
            // 发送短信通知用户注册密码或把密码返回
            req.setLoginPassword(password);
            req.setAccountLoginType(tenantEntity.getAccountLoginType());
            // 创建auth服务账号
            Long authUserId = authUserFacade.createUser(TenantAccountCommandInput.toUserBase(req), TenantAccountCommandInput.toUserBaseExtend(req));
            entity = TenantAccountEntity.toTenantAccountEntity(req);
            entity.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
            entity.setId(authUserId);
            entity.setAuthUserId(authUserId);
            tenantAccountCommandRepository.insertOrUpdate(entity);
        } else if (TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode().equals(entity.getDeletedFlag())) {
            // 如果已存在有效的账号
            throw new BizException("账号已存在");
        } else {
            // 启用之前删除的账号
            // 更新auth
            req.setAccountLoginType(tenantEntity.getAccountLoginType());
            req.setLoginPassword(password);
            req.setAuthUserId(entity.getAuthUserId());
            req.setTenantId(entity.getTenantId());
            req.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
            authUserFacade.updateUser(TenantAccountCommandInput.toUserBase(req));
            // 更新自己
            entity.setNickname(req.getNickname());
            entity.setOperatorPhone(req.getOperatorPhone());
            entity.setUpdater(req.getUpdater());
            entity.setOperatorTime(LocalDateTime.now());
            entity.setDeletedFlag(TenantAccountEnums.DeletedFlag.EFFECTIVE.getCode());
            entity.setStatus(TenantAccountEnums.status.EFFECTIVE.getCode());
            tenantAccountCommandRepository.insertOrUpdate(entity);
        }
        return entity.getId();
    }


    public void updateByTenantIdsAndPhone(List<Long> tenantIds, TenantAccountCommandInput req) {
        // 参数校验
        ValidateUtil.paramValidate(req,  "phone");
        tenantAccountCommandRepository.updateByTenantIdsAndPhone(tenantIds, TenantAccountEntity.buildUpdateEntity(req));
    }

    public void updateByTenantIdsAndAuthId(List<Long> tenantIds, TenantAccountCommandInput req) {
        // 参数校验
        ValidateUtil.paramValidate(req,  "authUserId");
        tenantAccountCommandRepository.updateByTenantIdsAndAuthId(tenantIds, TenantAccountEntity.buildUpdateEntity(req));
    }


    public void remove(TenantAccountCommandInput req) {
        // 参数校验
        ValidateUtil.paramValidate(req,  "id");

        // 账号校验
        final TenantAccountEntity entity = tenantAccountQueryRepository.selectById(req.getId());
        if (Objects.isNull(entity)) {
            throw new BizException("该账号不存在");
        }

        // 变更状态
        tenantAccountCommandRepository.remove(entity.getId());
    }


    public void updatePassword(TenantAccountCommandInput req) {
        // 参数校验
        ValidateUtil.paramValidate(req, "id");

        final TenantAccountEntity entity = tenantAccountQueryRepository.selectById(req.getId());
        if (Objects.isNull(entity)) {
            throw new BizException("该账号不存在");
        }
        req.setAuthUserId(entity.getAuthUserId());
        authUserFacade.updateUser(TenantAccountCommandInput.toUserBase(req));
    }
    
}
