package net.xianmu.usercenter.domain.merchant.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.common.constants.NumberConstant;
import net.xianmu.usercenter.common.enums.MerchantAccountEnums;
import net.xianmu.usercenter.common.enums.UserEnums;
import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.util.ValidateUtil;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAccountMappingEntity;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountCommandRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreAccountQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.MerchantStoreQueryRepository;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAccountMappingRepository;
import net.xianmu.usercenter.facade.auth.AuthUserFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 17:00
 */
@Service
@Slf4j
public class MerchantStoreAccountDomainService {

    @Autowired
    private MerchantStoreQueryRepository storeQueryRepository;
    @Autowired
    private MerchantStoreAccountQueryRepository queryRepository;
    @Autowired
    private MerchantStoreAccountCommandRepository commandRepository;
    @Autowired
    private AuthUserFacade authUserFacade;
    @Autowired
    private XmMerchantAccountMappingRepository xmMerchantAccountMappingRepository;


    public List<MerchantStoreAccountEntity> getMerchantStoreAccounts(MerchantStoreAccountQueryInput req) {
        List<MerchantStoreAccountEntity> entities = queryRepository.selectByCondition(req);
        this.wrapWithAuth(entities);
        return entities;
    }


    private void wrapWithAuth(List<MerchantStoreAccountEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return;
        }


        Map<Long, List<Long>> map = entities.stream().collect(Collectors.groupingBy(MerchantStoreAccountEntity::getTenantId, Collectors.mapping(MerchantStoreAccountEntity::getId, Collectors.toList())));
        map.forEach((k, v) -> {

            // 调用auth
            List<AuthUserResp> authUserResp = authUserFacade.queryAuthUserList(AuthUserFacade.getAuthSystemOriginByType(UserEnums.SizeEnum.MERCHANT, k), k, v);
            MerchantStoreAccountEntity.warpEntityFromAuth(authUserResp, entities);
        });
    }

    public PageInfo<MerchantStoreEntity> getAccountPageWithAuth(MerchantStoreAccountQueryInput req, PageQueryInput page) {
        return storeQueryRepository.selectAccountPage(req, page);
    }





    public MerchantStoreAccountEntity createAccount(MerchantStoreAccountCommandInput input) {
        log.info("领域层接收到新建账户请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "accountName", "phone", "tenantId");
        Long tenantId = input.getTenantId();

        // 校验username只能为 数字、大小写字母、邮箱符号(@._-)
        if (input.getUsername() != null && !input.getUsername().matches("[a-zA-Z0-9.@_-]{6,50}")) {
            throw new BizException("登录账号只能为 6到50位的数字、大小写字母、邮箱符号(@._-)");
        }

        // 校验手机号是否合法
        if (!input.getPhone().matches("^1[0-9]\\d{9}$")) {
            throw new BizException("手机号格式不正确");
        }


        // 门店账号最大数量限制
        List<MerchantStoreAccountEntity> accountEntities = queryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(tenantId).storeId(input.getStoreId()).deleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode()).build());
        if (!CollectionUtils.isEmpty(accountEntities) && accountEntities.size() > NumberConstant.TEN) {
            throw new BizException("最多有一个店长十个店员");
        }

        // 重复校验
        List<MerchantStoreAccountEntity> entities = queryRepository.selectByCondition(MerchantStoreAccountQueryInput.builder().tenantId(tenantId).storeId(input.getStoreId()).phone(input.getPhone()).build());
        if (CollUtil.isNotEmpty(entities)) {
            MerchantStoreAccountEntity entity = entities.get(0);

            if (MerchantAccountEnums.DeleteFlag.NORMAL.getCode().equals(entity.getDeleteFlag())) {
                throw new BizException("该手机号已经是当前门店的员工，请勿重复提交");
            }

            // 更新该账号为正常使用
            entity.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
            entity.setAccountName(input.getAccountName());
            this.createOrUpdate(entity);
            return entity;
        }

        // 新增账号
        MerchantStoreAccountEntity merchantStoreAccountEntity = MerchantStoreAccountEntity.buildCreateEntity(input);
        if (null == merchantStoreAccountEntity.getStatus()) {
            // 默认状态为正常
            merchantStoreAccountEntity.setStatus(MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode());
        }
        merchantStoreAccountEntity = this.createOrUpdate(merchantStoreAccountEntity);
        return merchantStoreAccountEntity;
    }


    public void createAccountForMerchantBatchCreate(List<MerchantStoreAccountCommandInput> inputList) {
        log.info("领域层接收到批量新建账户请求：input:{}", JSON.toJSONString(inputList));
        if (CollUtil.isEmpty(inputList)) {
            throw new ParamsException("门店账户信息为空!");
        }
        inputList.forEach(input -> {

            ValidateUtil.paramValidate(input, "accountName", "phone", "tenantId");
            // 新增账号
            MerchantStoreAccountEntity merchantStoreAccountEntity = MerchantStoreAccountEntity.buildCreateEntity(input);
            if (null == merchantStoreAccountEntity.getStatus()) {
                // 默认状态为正常
                merchantStoreAccountEntity.setStatus(MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode());
            }
            this.createOrUpdate(merchantStoreAccountEntity);
        });

    }



    /**
     * 更新账户
     *
     * @param input
     */
    public Boolean updateAccount(MerchantStoreAccountCommandInput input) {
        log.info("领域层接收到修改账户请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "id");
        MerchantStoreAccountEntity merchantStoreAccountEntity = queryRepository.selectById(input.getId());
        if (null == merchantStoreAccountEntity) {
            throw new BizException("账号不存在！");
        }

        MerchantStoreAccountEntity update = MerchantStoreAccountEntity.buildUpdateEntity(input);
        this.createOrUpdate(update);

        // 转交店长
        if (MerchantAccountEnums.Type.MANAGER.getCode().equals(input.getType())
                && MerchantAccountEnums.Type.CLERK.getCode().equals(merchantStoreAccountEntity.getType())) {
            // 将原本的店长账户改为店员
            commandRepository.updateManagerToClerk(merchantStoreAccountEntity.getStoreId(), merchantStoreAccountEntity.getId());
        }
        return true;
    }


    public Boolean deleteAccount(MerchantStoreAccountCommandInput input) {
        log.info("领域层接收到删除账户请求：input:{}", JSON.toJSONString(input));
        ValidateUtil.paramValidate(input, "id");
        MerchantStoreAccountEntity merchantStoreAccountEntity = queryRepository.selectById(input.getId());
        if (null == merchantStoreAccountEntity) {
            throw new BizException("账号不存在！");
        }

        if (MerchantAccountEnums.Type.MANAGER.getCode().equals(merchantStoreAccountEntity.getType())) {
            throw new BizException("店长被不能删除！");
        }


        // 逻辑删除
        merchantStoreAccountEntity.setDeleteFlag(MerchantAccountEnums.DeleteFlag.DELETED.getCode());
        this.createOrUpdate(merchantStoreAccountEntity);
        return true;
    }

    public MerchantStoreAccountEntity createOrUpdate(MerchantStoreAccountEntity entity) {
        // 先更新auth
        Integer systemOrigin = AuthUserFacade.getAuthSystemOriginByType(UserEnums.SizeEnum.MERCHANT, entity.getTenantId());
        if (null == entity.getId()) {
            // 新增
            UserBase userBase = authUserFacade.createAccount(SystemOriginEnum.getSystemOriginByType(systemOrigin), MerchantStoreAccountEntity.buildUserBase(entity), MerchantStoreAccountEntity.buildExtend(entity));
            entity.setId(userBase.getId());
            return commandRepository.create(entity);
        }

        // 修改
        authUserFacade.updateAccount(SystemOriginEnum.getSystemOriginByType(systemOrigin), MerchantStoreAccountEntity.buildAuthUserUpdateInput(entity));
        commandRepository.updateSelective(entity);
        return entity;
    }


    public void createOrUpdateForBinLog(MerchantStoreAccountEntity merchantStoreAccountEntity, Long xmAccountId) {
        // 创建的时候记录映射
        if (null == merchantStoreAccountEntity.getId()) {
            MerchantStoreAccountEntity entity = commandRepository.create(merchantStoreAccountEntity);
            XmMerchantAccountMappingEntity xmMappingEntity = XmMerchantAccountMappingEntity.builder().xmAccountId(xmAccountId).accountId(entity.getId()).build();
            xmMerchantAccountMappingRepository.createOrUpdate(xmMappingEntity);
            return;
        }
        commandRepository.updateSelective(merchantStoreAccountEntity);
    }


    public void deleteForBinLog(Long id) {
        commandRepository.delete(id);
    }
}

