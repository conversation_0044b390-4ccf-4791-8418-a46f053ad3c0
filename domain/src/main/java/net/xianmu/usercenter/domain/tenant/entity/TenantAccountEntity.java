package net.xianmu.usercenter.domain.tenant.entity;

import lombok.Data;
import net.xianmu.usercenter.domain.tenant.param.command.TenantAccountCommandInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:39
 */
@Data
public class TenantAccountEntity {
    /**
     * primary key
     */
    private Long id;

    /**
     * 外部用户系统用户id
     */
    private Long authUserId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 状态0有效1失效
     */
    private Integer status;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 删除标识0 有效1已删除
     */
    private Integer deletedFlag;

    /**
     * email
     */
    private String email;

    /**
     * 操作人
     */
    private String updater;


    public static TenantAccountEntity toTenantAccountEntity(TenantAccountCommandInput input) {
        TenantAccountEntity entity = new TenantAccountEntity();
        entity.setPhone(input.getPhone());
        entity.setNickname(input.getNickname());
        entity.setOperatorPhone(input.getOperatorPhone());
        entity.setOperatorTime(LocalDateTime.now());
        entity.setStatus(input.getStatus());
        entity.setTenantId(input.getTenantId());
        entity.setProfilePicture(input.getProfilePicture());
        entity.setEmail(input.getEmail());
        entity.setUpdater(input.getUpdater());
        return entity;
    }


    public static TenantAccountEntity buildUpdateEntity(TenantAccountCommandInput input) {
        TenantAccountEntity entity = new TenantAccountEntity();
        entity.setId(input.getId());
        entity.setPhone(input.getPhone());
        entity.setAuthUserId(input.getAuthUserId());
        entity.setNickname(input.getNickname());
        entity.setOperatorPhone(input.getOperatorPhone());
        entity.setOperatorTime(LocalDateTime.now());
        entity.setStatus(input.getStatus());
        entity.setTenantId(input.getTenantId());
        entity.setProfilePicture(input.getProfilePicture());
        entity.setUpdater(input.getUpdater());
        return entity;
    }

}
