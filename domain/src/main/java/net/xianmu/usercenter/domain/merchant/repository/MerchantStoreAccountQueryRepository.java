package net.xianmu.usercenter.domain.merchant.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:10
 */
public interface MerchantStoreAccountQueryRepository {

    /**
     * 根据id查询门店信息
     *
     * @param
     * @return
     */
    MerchantStoreAccountEntity selectById(Long id);

    /**
     * 根据id列表查询门店信息列表
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectByIds(List<Long> idList);


    /**
     * 根据门店id查询
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectByStoreId(Long tenantId, Long id);


    /**
     * 根据鲜沐账户id查询
     *
     * @param
     * @return
     */
    MerchantStoreAccountEntity selectByXmAccountId(Long xmAccountId);


    /**
     * 根据指定参数查询门店列表
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectByCondition(MerchantStoreAccountQueryInput req);

    /**
     * 根据指定参数查询门店列表
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectByConditionWithFuzzy(MerchantStoreAccountQueryInput req);


    /**
     * 查询saas的所有账户
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectSaasAccount();


    /**
     * 查询xm门店已存在，但是storeid为空的账户
     *
     * @param
     * @return
     */
    List<MerchantStoreAccountEntity> selectXmErrorAccount();


}
