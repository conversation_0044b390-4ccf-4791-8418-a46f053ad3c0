package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.common.input.command.MerchantStoreAccountCommandInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreAccountEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:09
 */
public interface MerchantStoreAccountCommandRepository {


    /**
     * 按实体更新
     * @param
     */
    @Deprecated
    MerchantStoreAccountEntity createOrUpdate(MerchantStoreAccountEntity entity);

    /**
     * 按实体更新
     * @param
     */
    MerchantStoreAccountEntity create(MerchantStoreAccountEntity entity);

    /**
     * 更新非空
     * @param
     */
    void updateSelective(MerchantStoreAccountEntity entity);


    /**
     * 更新非空
     * @param
     */
    void updateSelectiveBatch(List<MerchantStoreAccountEntity> entities);


    /**
     * 更新当前门店下的店长为非店长
     * @param
     */
    void updateManagerToClerk(Long storeId, Long currentAccountId);

    /**
     * 批量更新账户状态
     * @param idList
     * @param status
     */
    Boolean updateStatusBatch(List<Long> idList, Integer status);

    /**
     * 物理删除
     * @param id
     */
    void delete (Long id);
}
