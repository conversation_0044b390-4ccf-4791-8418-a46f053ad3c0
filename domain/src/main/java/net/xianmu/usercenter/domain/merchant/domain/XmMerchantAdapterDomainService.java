package net.xianmu.usercenter.domain.merchant.domain;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreExtEntity;
import net.xianmu.usercenter.domain.merchant.entity.XmMerchantAdapterEntity;
import net.xianmu.usercenter.domain.merchant.repository.XmMerchantAdapterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 *
 * @Title: 鲜沐门店适配表（临时适配鲜沐业务）业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-10-23 13:47:58
 * @version 1.0
 *
 */
@Service
@Slf4j
public class XmMerchantAdapterDomainService {


    @Autowired
    private XmMerchantAdapterRepository xmMerchantAdapterRepository;


    public void createOrUpdateForBinLog(Map<String, String> data, MerchantStoreEntity storeEntity) {
        Long storeId = storeEntity.getId();
        XmMerchantAdapterEntity entity = xmMerchantAdapterRepository.selectByStoreId(storeId);
        XmMerchantAdapterEntity updateEntity = XmMerchantAdapterEntity.buildCreateEntityForBinLog(data);
        if (null == entity) {
            log.warn("当前门店：storeId ：{}暂无拓展信息,走初始化逻辑", storeId);
            updateEntity.setStoreId(storeId);
            updateEntity.setTenantId(storeEntity.getTenantId());
            xmMerchantAdapterRepository.createOrUpdate(updateEntity);
        } else {
            updateEntity.setId(entity.getId());
            xmMerchantAdapterRepository.update(updateEntity);
        }

    }

}
