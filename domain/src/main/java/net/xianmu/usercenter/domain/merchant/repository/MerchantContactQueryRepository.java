package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.common.input.query.MerchantAddressQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantContactQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantAddressEntity;
import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-05-29 17:08:24
* @version 1.0
*
*/
public interface MerchantContactQueryRepository {

    MerchantContactEntity selectById(Long id);

    /**
     * 查询当前地址的默认联系人
     * @param addressId
     * @return
     */
    @Deprecated
    MerchantContactEntity selectDefaultContact(Long addressId);

    /**
     * 查询当前地址的默认联系人
     * @param addressId
     * @return
     */
    MerchantContactEntity selectDefaultContact(Long tenantId, Long addressId);

    /**
     * 根据指定参数查询列表
     * @param
     * @return
     */
    List<MerchantContactEntity> selectByCondition(MerchantContactQueryInput req);


    List<MerchantContactEntity> selectByStoreId(Long tenantId,Long storeId);
}