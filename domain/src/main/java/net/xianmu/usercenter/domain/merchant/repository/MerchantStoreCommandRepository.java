package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:09
 */
public interface MerchantStoreCommandRepository {

    MerchantStoreEntity createOrUpdate(MerchantStoreEntity entity);

    Boolean updateSelective(MerchantStoreEntity entity);

    Boolean delete(Long id);

    Boolean updateSelective(MerchantStoreCommandInput input);
}
