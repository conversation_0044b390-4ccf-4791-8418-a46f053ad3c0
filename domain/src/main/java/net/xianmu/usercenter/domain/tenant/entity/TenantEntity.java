package net.xianmu.usercenter.domain.tenant.entity;

import lombok.Data;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.domain.tenant.param.command.TenantCommandInput;

import java.time.LocalDateTime;

@Data
public class TenantEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型：0-供应商,1-品牌方,2-帆台
     */
    private Integer type;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 大客户Id
     */
    private Long adminId;
    /**
     * 操作人id
     */
    private Long opUid;

    /**
     * 操作人名称
     */
    private String opUname;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 归属DB
     */
    private String belongDB;

    /**
     * 微信分账开关
     */
    private Integer profitSharingSwitch;
    /**
     * 分账渠道 0 微信 1 汇付
     */
    private Integer onlinePayChannel;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;


    public static TenantEntity buildCreateEntity(TenantCommandInput input){
        TenantEntity tenant = new TenantEntity();
        tenant.setPhone(input.getLoginPhone());
        tenant.setTenantName(input.getMerchantName());
        tenant.setAdminId(input.getAdminId());
        tenant.setType(input.getType() == null ? TenantDefaultConstant.DEFAULT_TYPE : input.getType());
        tenant.setStatus(TenantDefaultConstant.DEFAULT_STATUS);
        tenant.setProfitSharingSwitch(TenantDefaultConstant.DEFAULT_SHARING_SWITCH);
        tenant.setOpUid(input.getOpUid());
        tenant.setOpUname(input.getOpUname());
        tenant.setEmail(input.getEmail());
        tenant.setAccountLoginType(input.getAccountLoginType());
        return tenant;
    }
}