package net.xianmu.usercenter.domain.tenant.valueobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @descripton 租户权益值对象
 * @date 2023/12/27 16:53
 */

@Data
public class TenantPrivilegesValueObject implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 过期时间
     */
    private Date expireTime;
}
