package net.xianmu.usercenter.domain.merchant.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.command.MerchantStoreCommandInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreAccountQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStorePageQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreEntity;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:10
 */
public interface MerchantStoreQueryRepository {

    /**
     * 根据id查询门店信息
     *
     * @param
     * @return
     */
    MerchantStoreEntity selectById(Long id);

    /**
     * 根据鲜沐m_id查询门店信息
     *
     * @param
     * @return
     */
    MerchantStoreEntity selectByMId(Long mId, Long tenantId);

    /**
     * 根据id列表查询门店信息列表
     *
     * @param
     * @return
     */
    List<MerchantStoreEntity> selectByIds(List<Long> idList);


    /**
     * 名称精确匹配
     *
     * @param
     * @return
     */
    MerchantStoreEntity selectByStoreName(Long tenantId, String storeName);


    /**
     * 查看租户维度下门店个数
     * @param tenantId
     * @return
     */
    Integer countStoreNum(@Param("tenantId") Long tenantId);


    /**
     * 查看租户维度下门店个数
     * @param tenantId
     * @return
     */
    Integer countStoreNum(MerchantStoreQueryInput req);


    /**
     * 根据指定参数查询门店列表
     * 1.支持店铺名称模糊匹配
     *
     * @param
     * @return
     */
    List<MerchantStoreEntity> selectByCondition(MerchantStoreQueryInput req);

    /**
     * 根据指定参数查询门店列表
     * 1.支持店铺名称模糊匹配
     * 2.支持手机号模糊匹配
     * @param
     * @return
     */
    List<MerchantStoreEntity> selectByAccountCondition(MerchantStoreQueryInput req);


    /**
     * saas 分组新增门店查询列表
     * @param input
     * @param pageQueryInput
     * @return
     */
    PageInfo<MerchantStoreEntity> selectMerchantStoreAndAddressPage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput);


    /**
     * saas-门店列表
     * @param input
     * @param pageQueryInput
     * @return
     */
    PageInfo<MerchantStoreEntity> selectMerchantStorePage(MerchantStorePageQueryInput input, PageQueryInput pageQueryInput);

    /**
     * saas-mall切换门店-分页查询账户
     *
     * @param
     * @return
     */
    PageInfo<MerchantStoreEntity> selectAccountPage(MerchantStoreAccountQueryInput req, PageQueryInput page);

    List<MerchantStoreEntity> selectNoPullBlackRecordMerchant();


    List<MerchantStoreEntity> selectMerchantStoreAndExtends(MerchantStoreQueryInput req);

    List<Long> selectMIdListByCondition(MerchantStoreQueryInput req);

    List<MerchantStoreEntity> selectMerchantStorePageForXM(MerchantStorePageQueryInput input);

    List<Long> selectStoreIdForXmPage(MerchantStorePageQueryInput input);
}
