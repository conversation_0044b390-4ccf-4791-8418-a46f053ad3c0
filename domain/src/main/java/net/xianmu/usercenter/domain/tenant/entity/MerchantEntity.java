package net.xianmu.usercenter.domain.tenant.entity;


import lombok.Data;
import net.xianmu.usercenter.common.input.command.MerchantCommandInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-11 10:44:50
 */
@Data
public class MerchantEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户信息
     */
    private Long tenantId;

    /**
     * 品牌名称
     */
    private String merchantName;

    /**
     * logo
     */
    private String logoImage;

    /**
     * 背景图
     */
    private String backgroundImage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public static MerchantEntity buildByTenantIdAndName(Long tenantId, String merchantName) {
        MerchantEntity merchant = new MerchantEntity();

        merchant.setTenantId(tenantId);
        merchant.setMerchantName(merchantName);

        return merchant;
    }

    public static MerchantEntity buildCreateEntity(MerchantCommandInput merchantCommandInput) {
        MerchantEntity merchant = new MerchantEntity();
        merchant.setId(merchantCommandInput.getId());
        merchant.setTenantId(merchantCommandInput.getTenantId());
        merchant.setMerchantName(merchantCommandInput.getMerchantName());
        merchant.setLogoImage(merchantCommandInput.getLogoImage());
        merchant.setBackgroundImage(merchantCommandInput.getBackgroundImage());
        merchant.setCreateTime(merchantCommandInput.getCreateTime());
        merchant.setUpdateTime(merchantCommandInput.getUpdateTime());
        return merchant;
    }


    /**
     * 仅更新业务需要的字段
     * @param merchantCommandInput
     */
    public static MerchantEntity buildUpdateEntity(MerchantCommandInput merchantCommandInput) {
        MerchantEntity merchant = new MerchantEntity();
        merchant.setId(merchantCommandInput.getId());
        merchant.setMerchantName(merchantCommandInput.getMerchantName());
        merchant.setLogoImage(merchantCommandInput.getLogoImage());
        merchant.setBackgroundImage(merchantCommandInput.getBackgroundImage());
        return merchant;
    }
}