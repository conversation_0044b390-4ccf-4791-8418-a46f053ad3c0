package net.xianmu.usercenter.domain.businessInfo.domain;

import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.common.input.command.BusinessInformationCommandInput;
import net.xianmu.usercenter.common.input.query.BusinessInformationQueryInput;
import net.xianmu.usercenter.common.input.query.MerchantStoreQueryInput;
import net.xianmu.usercenter.domain.businessInfo.entity.BusinessInformationEntity;
import net.xianmu.usercenter.domain.businessInfo.repository.BusinessInformationCommandRepository;
import net.xianmu.usercenter.domain.businessInfo.repository.BusinessInformationQueryRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantCommandRepository;
import net.xianmu.usercenter.domain.tenant.repository.TenantCompanyCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 17:00
 */
@Service
public class BusinessInformationDomainService {

    @Autowired
    private BusinessInformationQueryRepository queryRepository;
    @Autowired
    private BusinessInformationCommandRepository commandRepository;
    @Autowired
    private TenantCompanyCommandRepository tenantCompanyCommandRepository;

    public BusinessInformationEntity getBusinessInfoByBizIdAndType(BusinessInformationQueryInput input) {
        return queryRepository.getBusinessInfoByBizIdAndType(input.getBizId(), input.getType());
    }


    public void insert(BusinessInformationCommandInput input) {
        final BusinessInformationEntity entity = queryRepository.getBusinessInfoByBizIdAndType(input.getBizId(), input.getType());
        if (null != entity) {
            throw new BizException("工商信息已存在");
        }
        commandRepository.insert(input);
    }

    public void updateWithNull(BusinessInformationCommandInput input) {
        BusinessInformationEntity entity = queryRepository.getBusinessInfoByBizIdAndType(input.getBizId(), input.getType());
        if (null == entity) {
            throw new BizException("工商信息不存在");
        }
        BusinessInformationEntity commandEntity = BusinessInformationEntity.buildCommandEntity(input);
        commandEntity.setId(entity.getId());
        commandRepository.updateByEntity(commandEntity);
    }

    public void update(BusinessInformationCommandInput input) {
        BusinessInformationEntity entity = queryRepository.getBusinessInfoByBizIdAndType(input.getBizId(), input.getType());
        if (null == entity) {
            throw new BizException("工商信息不存在");
        }
        BusinessInformationEntity commandEntity = BusinessInformationEntity.buildCommandEntity(input);
        commandEntity.setId(entity.getId());
        commandRepository.updateSelective(commandEntity);
    }

    public void createOrUpdateForBinLog(BusinessInformationEntity input) {
        BusinessInformationEntity entity = queryRepository.getBusinessInfoByBizIdAndType(input.getBizId(), input.getType());
        if (null == entity) {
            commandRepository.insert(input);
        } else {
            input.setId(entity.getId());
            commandRepository.updateSelective(input);
        }
    }
}
