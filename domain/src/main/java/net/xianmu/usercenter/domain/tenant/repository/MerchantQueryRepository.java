package net.xianmu.usercenter.domain.tenant.repository;

import net.xianmu.usercenter.common.input.query.MerchantQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/25 11:08
 */
public interface MerchantQueryRepository {

    MerchantEntity selectByTenantId(Long tenantId);

    MerchantEntity selectById(Long id);

    List<MerchantEntity> selectByCondition(MerchantQueryInput input);
}
