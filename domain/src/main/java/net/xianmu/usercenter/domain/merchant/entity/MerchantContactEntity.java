package net.xianmu.usercenter.domain.merchant.entity;


import lombok.Data;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.enums.MerchantContactEnum;
import net.xianmu.usercenter.common.input.command.MerchantContactCommandInput;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-05-29 17:08:24
 */
@Data
public class MerchantContactEntity {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 联系人名称
     */
    private String name;

    /**
     * 联系人手机号
     */
    private String phone;

    /**
     * 是否是默认联系人：0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public void buildUpdateEntity(MerchantContactCommandInput input) {
        this.name = input.getName() == null ? name : input.getName();
        this.phone = input.getPhone() == null ? phone : input.getPhone();
        this.defaultFlag = input.getDefaultFlag() == null ? defaultFlag : input.getDefaultFlag();
    }

    public static MerchantContactEntity buildCreateEntity(MerchantContactCommandInput input) {
        MerchantContactEntity merchantContact = new MerchantContactEntity();
        merchantContact.setTenantId(input.getTenantId());
        merchantContact.setAddressId(input.getAddressId());
        merchantContact.setName(input.getName());
        merchantContact.setPhone(input.getPhone());
        merchantContact.setDefaultFlag(input.getDefaultFlag() == null ? MerchantContactEnum.NOT_DEFAULT.getType() : input.getDefaultFlag());
        return merchantContact;
    }

    public static MerchantContactEntity converterToEntityForBinLog(Map<String, String> data){
        MerchantContactEntity merchantContact = new MerchantContactEntity();
        merchantContact.setTenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID);
        merchantContact.setName(data.get("contact"));
        merchantContact.setPhone(data.get("phone"));
        merchantContact.setDefaultFlag(MerchantContactEnum.DEFAULT.getType());
        return merchantContact;
    }
}