package net.xianmu.usercenter.domain.invoice.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;
import net.xianmu.usercenter.common.util.DateUtil;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-13 13:58:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceConfigEntity {
    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 根据类型的不同，取自regional_organization/merchant_store表id
     */
    private Long bizId;


    /**
     * 0:门店自有抬头；1:区域组织下的抬头
     */
    private Integer type;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户账号
     */
    private String openAccount;

    /**
     * 开户银行
     */
    private String openBank;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司电话
     */
    private String companyPhone;

    /**
     * 邮寄地址
     */
    private String mailAddress;

    /**
     * 收件人
     */
    private String companyReceiver;

    /**
     * 邮箱
     */
    private String companyEmail;

    /**
     * 0:生效中（默认), 1:(失效)
     */
    private Integer validStatus;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建抬头提交时间
     */
    private LocalDateTime createTime;

    /**
     * 联系方式
     */
    private String linkMethod;

    /**
     * 营业执照地址
     */
    private String businessLicenseAddress;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    public static InvoiceConfigEntity converterToEntityForBinLog(Map<String, String> data) {
        InvoiceConfigEntity invoiceConfigEntity = new InvoiceConfigEntity();
        invoiceConfigEntity.setId(Long.valueOf(data.get("id")));
        invoiceConfigEntity.setTenantId(TenantDefaultConstant.XIAN_MU_TENANT_ID);
        invoiceConfigEntity.setType(Integer.valueOf(data.get("type")));
        invoiceConfigEntity.setInvoiceTitle(data.get("invoice_title"));
        invoiceConfigEntity.setTaxNumber(data.get("tax_number"));
        invoiceConfigEntity.setOpenAccount(data.get("open_account"));
        invoiceConfigEntity.setOpenBank(data.get("open_bank"));
        invoiceConfigEntity.setCompanyAddress(data.get("company_address"));
        invoiceConfigEntity.setCompanyPhone(data.get("company_phone"));
        invoiceConfigEntity.setMailAddress(data.get("mail_address"));
        invoiceConfigEntity.setCompanyReceiver(data.get("company_receiver"));
        invoiceConfigEntity.setCompanyEmail(data.get("company_email"));
        invoiceConfigEntity.setValidStatus(Integer.valueOf(data.get("valid_status")));
        invoiceConfigEntity.setUpdateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("update_time"), null));
        invoiceConfigEntity.setCreateTime(DateUtil.getTimeWithBinLogPatternDefault(data.get("create_time"), null));
        invoiceConfigEntity.setLinkMethod(data.get("link_method"));
        invoiceConfigEntity.setBusinessLicenseAddress(data.get("business_license_address"));
        invoiceConfigEntity.setCreator(data.get("creator"));
        invoiceConfigEntity.setUpdater(data.get("updater"));
        return invoiceConfigEntity;
    }
}