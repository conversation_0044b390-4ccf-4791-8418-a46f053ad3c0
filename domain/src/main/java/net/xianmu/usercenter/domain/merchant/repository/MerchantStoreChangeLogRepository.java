package net.xianmu.usercenter.domain.merchant.repository;

import net.xianmu.usercenter.common.input.query.MerchantStoreChangeLogQueryInput;
import net.xianmu.usercenter.domain.merchant.entity.MerchantStoreChangeLogEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-10 17:23:10
 */
public interface MerchantStoreChangeLogRepository {

    MerchantStoreChangeLogEntity createOrUpdate(MerchantStoreChangeLogEntity entity);

    void createBatch(List<MerchantStoreChangeLogEntity> entities);

    void updateChannelCode(Long id, String inviterChannelCode, String merchantChannelCode);

    MerchantStoreChangeLogEntity selectByStoreId(Long storeId, Integer opType);

    List<MerchantStoreChangeLogEntity> selectByCondition(MerchantStoreChangeLogQueryInput input);

}