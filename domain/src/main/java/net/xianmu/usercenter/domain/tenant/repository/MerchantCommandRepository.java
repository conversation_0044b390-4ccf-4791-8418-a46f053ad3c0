package net.xianmu.usercenter.domain.tenant.repository;

import net.xianmu.usercenter.domain.merchant.entity.MerchantContactEntity;
import net.xianmu.usercenter.domain.tenant.entity.MerchantEntity;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/11 10:46
 */
public interface MerchantCommandRepository {

    /**
     * 按实体更新
     * @param
     */
    MerchantEntity createOrUpdate(MerchantEntity entity);

    /**
     * 按实体更新
     * @param
     */
    Boolean updateSelective(MerchantEntity entity);
}
