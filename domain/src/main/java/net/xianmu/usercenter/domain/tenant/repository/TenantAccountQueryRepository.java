package net.xianmu.usercenter.domain.tenant.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.common.input.query.PageQueryInput;
import net.xianmu.usercenter.common.input.query.TenantAccountListQueryInput;
import net.xianmu.usercenter.domain.tenant.entity.TenantAccountEntity;

import java.util.List;

public interface TenantAccountQueryRepository {

    /**
     * 根据auth_user_id查询账户信息
     *
     * @param
     * @return
     */
    TenantAccountEntity selectByAuthUserId(Long authUserId);

    /**
     * 根据auth_user_id查询账户
     * @return
     */
    List<TenantAccountEntity> selectByAuthUserIds(List<Long> list);


    /**
     * 根据指定参数查询账户
     * @return
     */
    List<TenantAccountEntity> selectByByCondition(TenantAccountListQueryInput req);


    /**
     * 根据id查询账户信息
     *
     * @param
     * @return
     */
    TenantAccountEntity selectById(Long id);


    /**
     * 查询
     *
     * @param tenantId
     * @param phone
     * @return
     */
    TenantAccountEntity selectByPhoneAndTenantId(Long tenantId, String phone);


    /**
     * 查询
     *
     * @param tenantId
     * @param phone
     * @return
     */
    TenantAccountEntity selectByEmailAndTenantId(Long tenantId, String email);



    /**
     * 根据租户id列表和phone查询所有(正常的账户)
     *
     * @param tenantId
     * @param phone
     * @return
     */
    List<TenantAccountEntity> getTenantAccountByTenantIdsAndPhone(List<Long> tenantIdList, String phone);


    /**
     * 根据指定参数查询租户列表(带分页)
     * @param
     * @return
     */
    PageInfo<TenantAccountEntity> getTenantAccountPage(TenantAccountListQueryInput req, PageQueryInput pageQueryInput);

}
