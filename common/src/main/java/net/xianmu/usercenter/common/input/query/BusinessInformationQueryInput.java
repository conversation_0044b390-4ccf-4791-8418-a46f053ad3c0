package net.xianmu.usercenter.common.input.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 17:22
 */

@Data
public class BusinessInformationQueryInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id：tenantId或者storeId
     */
    private Long bizId;

    /**
     * 类型：0-品牌用户，1-单店用户
     */
    private Integer type;

    /**
     * 用户id：tenantId或者storeId
     */
    private List<Long> bizIdList;
}
