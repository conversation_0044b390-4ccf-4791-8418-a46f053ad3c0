package net.xianmu.usercenter.common.input.command;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/26 11:07
 */
@Data
public class MerchantContactCommandInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 主键、自增
     */
    private List<Long> idList;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 地址id
     */
    private Long addressId;
    /**
     * 联系人名称
     */
    private String name;
    /**
     * 联系人手机号
     */
    private String phone;
    /**
     * 是否是默认地址：0、否 1、是
     */
    private Integer defaultFlag;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOrigin;
}
