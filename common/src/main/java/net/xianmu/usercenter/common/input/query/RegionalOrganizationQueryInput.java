package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/6 16:41
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionalOrganizationQueryInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户id
     */
    private List<Long> tenantIds;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 组织来源：0-saas,1-鲜沐
     */
    private Integer source;

    /**
     * 客户类型：1-大客户，2-单店
     */
    private Integer size;

    /**
     * 组织状态： 0正常 1禁用
     */
    private Integer status;

    /**
     * 鲜沐大客户id
     */
    private Long adminId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 操作人名称
     */
    private String updater;
}
