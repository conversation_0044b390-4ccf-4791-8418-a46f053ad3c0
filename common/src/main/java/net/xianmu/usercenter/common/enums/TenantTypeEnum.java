package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/27
 */
@Getter
@AllArgsConstructor
public enum TenantTypeEnum {
    /**
     * 品牌方
     */
    BRAND(0, "品牌方"),
    /**
     * 供应商
     */
    SUPPLY(1, "供应商"),
    /**
     * 帆台
     */
    FANTAI(2, "帆台"),
    /**
     * 3-外单客户
     */
    OUTER_ORDER(3, "外单客户");

    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;
}
