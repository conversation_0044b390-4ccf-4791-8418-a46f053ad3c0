package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.usercenter.common.constants.TenantDefaultConstant;

/**
 * <AUTHOR>
 * @descripton 用户体系枚举
 * @date 2023/8/25 14:21
 */
public interface UserEnums {

    @Getter
    @AllArgsConstructor
    enum TypeEnum {
        SAAS(1, "saas"),
        XIANMU(0, "xianmu");
        private Integer code;
        private String desc;


        public static TypeEnum getByTenantId(Long tenantId) {
            if (TenantDefaultConstant.XIAN_MU_TENANT_ID.equals(tenantId)) {
                return XIANMU;
            }
            return SAAS;
        }
    }


    @Getter
    @AllArgsConstructor
    enum SizeEnum {
        TENANT(0, "租户"),
        REGIONAL(1, "区域组织（大客户）"),
        MERCHANT(1, "门店");
        private Integer code;
        private String desc;
    }
}
