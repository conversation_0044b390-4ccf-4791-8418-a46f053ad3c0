package net.xianmu.usercenter.common.input.query;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-10 17:23:10
 * @version 1.0
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreChangeLogQueryInput {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 区域id
	 */
	private Long regionalId;

	/**
	 * 门店id
	 */
	private Long storeId;

	/**
	 * 门店id
	 */
	private List<Long> storeIdList;


	/**
	 * 记录是哪个销售邀请的
	 */
	private String inviterChannelCode;

	/**
	 * 记录是哪个门店邀请的
	 */
	private String merchantChannelCode;

	/**
	 * 操作人名称
	 */
	private String opName;

	/**
	 * 操作类型，0-创建，1-审核，2-关店，3-拉黑
	 */
	private Integer opType;

	/**
	 * 操作备注
	 */
	private String opRemark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}