package net.xianmu.usercenter.common.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/25 10:58
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantCommandInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户信息
     */
    private Long tenantId;

    /**
     * 品牌名称
     */
    private String merchantName;

    /**
     * logo
     */
    private String logoImage;

    /**
     * 背景图
     */
    private String backgroundImage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
