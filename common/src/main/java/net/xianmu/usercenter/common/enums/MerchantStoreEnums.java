package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface MerchantStoreEnums {
    /**
     * 店铺类型
     */
    @Getter
    @AllArgsConstructor
    public enum Type {
        /**
         * 0-直营店
         */
        DIRECT(0, "直营店"),
        /**
         * 1-加盟店
         */
        JOINING(1, "加盟店"),
        /**
         * 2-托管店
         */
        MANAGED(2, "托管店"),
        /**
         * 3-托管店
         */
        PERSONAL(3, "个人店"),
        /**
         * 4-未知
         */
        CHAIN(4, "连锁店"),
        /**
         * 5-未知
         */
        UN_KNOW(5, "未知");

        /**
         * 店铺类型编码
         */
        private Integer code;
        /**
         * 店铺类型描述
         */
        private String desc;

        /**
         * get status
         * @param desc
         * @return
         */
        public static Type getStatus(String desc) {
            for (Type type : Type.values()) {
                if (type.desc.equals(desc)) {
                    return type;
                }
            }

            return null;
        }

        /**
         * get desc
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (Type type : Type.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }
    }

    /**
     * 状态
     */
    @Getter
    @AllArgsConstructor
    public enum Status {
        /**
         * 0-审核中
         */
        IN_AUDIT(0, "审核中"),
        /**
         * 1-审核成功
         */
        AUDIT_SUCCESS(1, "审核成功"),
        /**
         * 2-审核失败
         */
        AUDIT_FAIL(2, "审核失败"),

        CLOSE(3, "关店"),
        /**
         * 4-拉黑
         */
        PULL_BLACK(4, "拉黑"),

        CANCEL(5, "注销");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnums.Status statusEnum : MerchantStoreEnums.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }


    /**
     * 列表查询状态
     */
    @Getter
    @AllArgsConstructor
    public enum PageQueryStatus {
        /**
         * 0-审核中
         */
        IN_AUDIT(0, "审核中"),
        /**
         * 1-审核成功
         */
        AUDIT_SUCCESS(1, "审核成功"),
        /**
         * 2-审核失败
         */
        AUDIT_FAIL(2, "审核失败"),

        CLOSE(3, "关店"),
        /**
         * 4-拉黑
         */
        PULL_BLACK(4, "拉黑"),

        CANCEL(5, "注销"),

        PENDING_VERIFICATION(100, "待核验"),

        /**
         * 待提交核验
         */
        PENDING_SUBMISSION(101, "待提交核验");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnums.Status statusEnum : MerchantStoreEnums.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }



    /**
     * 鲜沐门店表的状态
     */
    @Getter
    @AllArgsConstructor
    public enum XmStatus {

        AUDIT_SUCCESS(0, "审核成功"),

        IN_AUDIT(1, "审核中"),

        AUDIT_FAIL(2, "审核未通过"),

        PULL_BLACK(3, "拉黑"),

        CANCEL(4, "注销");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnums.Status statusEnum : MerchantStoreEnums.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }



    /**
     * 鲜沐门店表的状态
     */
    @Getter
    @AllArgsConstructor
    public enum MockLoginFlag {

        YES(1, "可以模拟登录"),

        NO(0, "不能模拟登录");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnums.Status statusEnum : MerchantStoreEnums.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }
}
