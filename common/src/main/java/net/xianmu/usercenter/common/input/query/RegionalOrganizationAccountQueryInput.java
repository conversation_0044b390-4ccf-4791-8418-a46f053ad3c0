package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/6 16:50
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionalOrganizationAccountQueryInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 区域id
     */
    private Long regionalId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账户名称
     */
    private String name;

    /**
     * 账户状态： 0正常 1禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 操作人名称
     */
    private String updater;
}
