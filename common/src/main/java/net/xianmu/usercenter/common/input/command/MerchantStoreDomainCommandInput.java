package net.xianmu.usercenter.common.input.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/26 11:28
 */
@Data
public class MerchantStoreDomainCommandInput {

    /*
     * 门店id
     */
    private Long storeId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店分组Id
     */
    private Long groupId;

    /**
     * 审核标识 0、不通过 1、通过
     * 为空不做处理
     */
    private Integer auditFlag;

    /**
     * true-批量导入（由于auth没有批量接口，所以在外置做数据库校验优化性能），false-正常注册
     */
    private boolean batchCreateFlag;

    /**
     * 批量导入时的行号
     */
    private Long batchCreateRowNum;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 门店信息
     */
    private MerchantStoreCommandInput merchantStore;

    /**
     * 门店地址，里面包含联系人信息
     */
    private List<MerchantAddressCommandInput> merchantAddressList;

    /**
     * 账户列表
     */
    private List<MerchantStoreAccountCommandInput> merchantStoreAccountList;
}
