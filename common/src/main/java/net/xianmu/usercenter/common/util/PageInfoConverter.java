package net.xianmu.usercenter.common.util;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

public class PageInfoConverter {
    public static <T, R> PageInfo<R> toPageResp(PageInfo<T> page, Function<? super T, ? extends R> function) {
        PageInfo resp = new PageInfo();
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        resp.setTotal(page.getTotal());
        resp.setSize(page.getSize());
        resp.setStartRow(page.getStartRow());
        resp.setEndRow(page.getEndRow());
        resp.setPages(page.getPages());
        resp.setPrePage(page.getPrePage());
        resp.setNextPage(page.getNextPage());
        resp.setIsFirstPage(page.isIsFirstPage());
        resp.setIsLastPage(page.isIsLastPage());
        resp.setHasNextPage(page.isHasNextPage());
        resp.setHasPreviousPage(page.isHasPreviousPage());
        resp.setNavigatePages(page.getNavigatePages());
        resp.setNavigatepageNums(page.getNavigatepageNums());
        resp.setNavigateFirstPage(page.getNavigateFirstPage());
        resp.setNavigateLastPage(page.getNavigateLastPage());
        List<R> collect = page.getList().stream().map(function).collect(toList());
        resp.setList(collect);
        return resp;
    }
}
