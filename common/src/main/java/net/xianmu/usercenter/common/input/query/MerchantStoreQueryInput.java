package net.xianmu.usercenter.common.input.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.common.result.DubboResponse;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 14:22
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class MerchantStoreQueryInput extends BasePageInput {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店id列表
     */
    private List<Long> storeIdList;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称（模糊匹配）
     */
    private String storeName;

    /**
     * 门店名称(精确匹配)
     */
    private String exactStoreName;

    /**
     * 门店名称列表(精确匹配)
     */
    private List<String> exactStoreNameList;


    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店编号列表
     */
    private List<String> storeNoList;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号前缀，用于右模糊
     */
    private String phonePrefix;

    /**
     *
     */
    private Long mId;

    /**
     *
     */
    private Integer areaNo;

    /**
     * 1-大客户 2-单店
     */
    private Integer size;

    /**
     1 账期 2  现结
     */
    private Integer direct;

    /**
     * 门店ids
     */
    private List<Long> mIds;

    /**
     * areaNos 数据权限
     */
    private List<Integer> areaNos;

    /**
     * 门店自己的邀请码，可用于邀请门店、子账号等
     */
    private String channelCode;


    /**
     * 注册开始时间
     */
    private LocalDateTime startRegisterTime;

    /**
     * 注册结束时间
     */
    private LocalDateTime endRegisterTime;


    /**
     * 审核开始时间
     */
    private LocalDateTime startAuditTime;

    /**
     * 审核结束时间
     */
    private LocalDateTime endAuditTime;


    /**
     * 是否获取主账户信息
     */
    private boolean queryManageAccount;

    private Integer enableOfflinePayment;


    public void addStoreIdList(List<Long> list) {
        List<Long> temp = new ArrayList<>();
        if (CollUtil.isNotEmpty(this.storeIdList)) {
            temp.addAll(storeIdList);
        }
        temp.addAll(list);
        storeIdList = temp;
    }


    public static void paramValidate(MerchantStoreQueryInput input, int defaultMaxSize){
        if(input == null) {
            log.error("请求核心参数缺失!");
            throw new BizException("请求核心参数缺失!");
        }
        // 校验核心参数非空
        if(input.getStoreId() == null && CollUtil.isEmpty(input.getStoreIdList())
                && input.getMId() == null && CollUtil.isEmpty(input.getMIds())
                && input.getPhone() == null && input.getPhonePrefix() == null
                && input.getStoreNo() == null && CollUtil.isEmpty(input.getStoreNoList())
                && input.getChannelCode() == null && input.getExactStoreName() == null
                && StrUtil.isBlank(input.getStoreName()) && CollUtil.isEmpty(input.getExactStoreNameList())
                ){
            log.error("请求核心参数缺失!");
            throw new BizException("请求核心参数缺失!");
        }
        // 校验批量数据大小
        if((CollUtil.isNotEmpty(input.getStoreIdList()) && input.getStoreIdList().size() > defaultMaxSize) ||
                (CollUtil.isNotEmpty(input.getMIds()) && input.getMIds().size() > defaultMaxSize)
                ||(CollUtil.isNotEmpty(input.getStoreNoList()) && input.getStoreNoList().size() > defaultMaxSize)
                || (CollUtil.isNotEmpty(input.getExactStoreNameList()) && input.getExactStoreNameList().size() > defaultMaxSize)
        ){
            log.error("单次请求数据量过大，目前单次最大支持：{}条", defaultMaxSize);
            throw new BizException("单次请求数据量过大!");
        }

    }

}
