package net.xianmu.usercenter.common.input.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:06
 */
@Data
public class MerchantStoreGroupQueryInput {


    /**
     * 分组id
     */
    private Long id;

    /**
     * 分组id列表
     */
    private List<Long> idList;


    /**
     * 分组名称
     */
    private String merchantStoreGroupName;

    /**
     * 分组类型
     */
    private Integer type;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建时间排序
     */
    private String createTimeSort;
    /**
     * 更新时间排序
     */
    private String updateTimeSort;


}
