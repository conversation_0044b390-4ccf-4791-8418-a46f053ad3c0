package net.xianmu.usercenter.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Pattern;



/**
 * <AUTHOR>
 * @description
 * @date 2022/5/17 19:23
 */
@Slf4j
public class StringUtils extends org.springframework.util.StringUtils {
    //特殊字符
    public static final String SPEC_CHARACTERS = " !\"#$%&'()*+,-./:;<=>?@\\]\\[^_`{|}~";
    // 纯字母
    public static final String character = "[a-zA-Z]{1,}$";
    // 纯数字
    public static final String numberic = "[0-9]{1,}$";
    // 字母和数字
    public static final String number_and_character = "((^[a-zA-Z]{1,}[0-9]{1,}[a-zA-Z0-9]*)+)" +
        "|((^[0-9]{1,}[a-zA-Z]{1,}[a-zA-Z0-9]*)+)$";
    // 字母或数字
    public static final String number_or_character = "[a-zA-Z0-9]+$";
    // 字母数字下划线
    public static final String ncw = "\\w+$";

    /**
     * 分隔符
     */
    public static final String SEPARATING_IN_LINE = "-";

    private static Logger logger = LoggerFactory.getLogger(StringUtils.class);

    public static String encodeParams(String area, String city, Long skuId) {
        try {
            return "area=" + URLEncoder.encode(area, "UTF-8") + "&city=" + URLEncoder.encode(city, "UTF-8") + "&" + "invId=" + skuId;
        } catch (UnsupportedEncodingException e) {
            logger.error(e.getMessage());
        }
        return "";
    }

    public static final String orderRandomNum() {
        Random random = new Random();
        int rs = random.nextInt(99);
        return rs < 10 ? "0" + rs : rs + "";
    }

    public static boolean isBlank(Object... objects) {
        boolean result = false;
        if (null == objects) {
            return true;
        } else {
            Object[] var2 = objects;
            int var3 = objects.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                Object object = var2[var4];
                if (null == object || "".equals(object.toString().trim()) || "null".equals(object.toString().trim())) {
                    result = true;
                    break;
                }
            }

            return result;
        }
    }

    /**
     * 校验门店名称是否规范
     *
     * @param storeName
     * @return
     */
    public static boolean isStoreName(String storeName) {
        return isEmpty(storeName) ? false : storeName.matches("[\\u4e00-\\u9fa5a-zA-Z0-9]{1,20}");
    }

    /**
     * 校验地址是否规范
     *
     * @param storeAddress
     * @return
     */
    public static boolean isAddress(String storeAddress) {
        return isEmpty(storeAddress) ? false : storeAddress.matches("[\\u4e00-\\u9fa5a-zA-Z0-9]{1,50}");
    }

    /**
     * 校验门牌号是否规范
     *
     * @return
     */
    public static boolean isHouseNumber(String houseNumber) {
        return houseNumber.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-]{1,50}");
    }

    public static boolean checkPassword(String targetString) {
        String opStr = targetString;
        boolean isLegal = false;
        boolean hasSpecChar = false;
        char[] charArray = opStr.toCharArray();
        for (char c : charArray) {
            if (SPEC_CHARACTERS.contains(String.valueOf(c))) {
                hasSpecChar = true;
                // 替换此字符串
                opStr = opStr.replace(c, ' ');
            }
        }
        String excSpecCharStr = opStr.replace(" ", "");
        boolean isPureNum = Pattern.compile(numberic).matcher(excSpecCharStr).matches();
        boolean isPureChar = Pattern.compile(character).matcher(excSpecCharStr).matches();
        boolean isNumAndChar = Pattern.compile(number_and_character).matcher(excSpecCharStr).matches();
        boolean length = targetString.length() <= 20 && targetString.length() >= 8;
        isLegal = ((isPureNum && hasSpecChar)
            || (isPureChar && hasSpecChar) || isNumAndChar && hasSpecChar) || isNumAndChar;
        return isLegal && length;
    }

    public static String createInitPassword() {
        // 创建一个密码，8位字母+数字
        return UUID.randomUUID().toString().replaceAll(SEPARATING_IN_LINE, "").substring(0, 8);
    }

}
