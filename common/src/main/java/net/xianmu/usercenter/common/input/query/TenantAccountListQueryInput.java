package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/6 11:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantAccountListQueryInput {

    /**
     * 主键id
     */
    private Long nickId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 删除标识0有效1删除
     */
    private Integer deletedFlag;

    /**
     * authUserIds
     */
    private List<Long> authUserIds;

    /**
     * accountIds
     */
    private List<Long> idList;

    /**
     * email
     */
    private String email;

    /**
     * 登录账号：手机号或者密码
     */
    private String username;
}
