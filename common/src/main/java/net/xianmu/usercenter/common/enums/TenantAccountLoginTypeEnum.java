package net.xianmu.usercenter.common.enums;

public enum TenantAccountLoginTypeEnum {
    PHONE_LOGIN(0, "手机号登录"),
    EMAIL_LOGIN(1, "邮箱登录");

    private final Integer code;
    private final String desc;

    TenantAccountLoginTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举实例
     */
    public static TenantAccountLoginTypeEnum fromCode(Integer code) {
        for (TenantAccountLoginTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的登录类型编码: " + code);
    }

    /**
     * 判断编码是否有效
     */
    public static boolean isValid(Integer code) {
        for (TenantAccountLoginTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
