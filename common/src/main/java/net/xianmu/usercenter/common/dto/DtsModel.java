package net.xianmu.usercenter.common.dto;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import net.xianmu.usercenter.common.enums.BinlogEventEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Description: binlog 同步mq数据接受实体；测试环境和生成环境字段略有不同，DtsModel不要如非必要不要加字段。
 */
@Data
public class DtsModel {
    /**
     * 记录ID（生产环境同时是mq 消息key）
     */
    private String id;
    /**
     * 数据库
     */
    private String database;

    /**
     * 表
     */
    private String table;

    /**
     * 操作类型：INSERT、UPDATE、DELETE
     */
    private String type;

    /**
     * 影响数据
     */
    private List<Map<String, String>> data;

    /**
     * 更新字段数据
     */
    private List<Map<String, String>> old;

    /**
     * 字段容易产生歧义，不对外提供getId()方法
     * @return ID
     */
    private String getId(){
        return this.id;
    }

    /**
     * 查询消息key
     * @return id、msg-key
     */
    public String getMsgKey(){
        return this.id;
    }


    /**
     * 查询变更的字段
     *
     * @param index 第几条
     * @return 数据库字段
     */
    public Set<String> getChangeFieldName(int index) {
        if (index < 0 || index >= old.size()) {
            return null;
        }
        if (BinlogEventEnum.UPDATE.getEvent().equals(this.type)) {
            if (!CollectionUtil.isEmpty(this.old)) {
                Map<String, String> changeMap = this.old.get(index);
                return changeMap.keySet();
            }
        }

        return null;
    }

    /**
     * @param consumer 处理数据
     */
    public void consumerData(Consumer<Map<String, String>> consumer) {
        if (CollectionUtil.isEmpty(this.data)) {
            return;
        }
        for (Map<String, String> el : this.data) {
            consumer.accept(el);
        }
    }

    /**
     *
     * @param consumer 处理变更数据
     */
    public void consumerOld(Consumer<Map<String, String>> consumer) {
        if (CollectionUtil.isEmpty(this.old)) {
            return;
        }
        for (Map<String, String> el : this.old) {
            consumer.accept(el);
        }
    }
}
