
package net.xianmu.usercenter.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * SpringBean工具类
 *
 */
@Component
public class SpringContextHolder implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    private static final Logger logger = LoggerFactory.getLogger(SpringContextHolder.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        logger.debug("inject applicationContext:{}", applicationContext.getApplicationName());
        if (SpringContextHolder.applicationContext != null) {
            logger.warn("ApplicationContext is override, old ApplicationContext:" + SpringContextHolder.applicationContext.getApplicationName());
        }

        SpringContextHolder.applicationContext = applicationContext;
    }

    /**
     * getApplicationContext:获取当前运行的Spring容器
     *
     * @return
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * getBean:根据类型从容器中获取bean对象
     *
     */
    public static <T> T getBean(String name, Class<T> requiredType) {
        return getApplicationContext().getBean(name, requiredType);
    }

    /**
     * getBean:根据类型从容器中获取bean对象
     */
    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * getBean:根据类型从容器中获取bean对象
     */
    public static <T> T getBean(Class<T> requiredType) {
        return getApplicationContext().getBean(requiredType);
    }

}
