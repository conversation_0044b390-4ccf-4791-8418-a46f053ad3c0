package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/12 15:16
 */
public interface BusinessInformationEnum {

    @Getter
    @AllArgsConstructor
    enum TypeEnum {

        TENANT(0, "品牌用户"),
        MERCHANT_STORE(1, "单店用户"),
        REGIONAL_ORGANIZATION(2, "区域组织")
        ;

        private Integer code;
        private String desc;

    }
}
