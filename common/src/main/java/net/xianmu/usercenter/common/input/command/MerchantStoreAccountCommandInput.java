package net.xianmu.usercenter.common.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/31 11:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreAccountCommandInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 主键、自增
     */
    private List<Long> idList;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 0 已删除 1 正常使用
     */
    private Integer deleteFlag;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * oa公众号Id
     */
    private String oaOpenId;

    /**
     * 登录账号名称
     */
    private String username;
}
