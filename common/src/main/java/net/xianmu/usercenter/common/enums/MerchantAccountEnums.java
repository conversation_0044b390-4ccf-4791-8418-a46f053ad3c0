package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/31 13:41
 */
public interface MerchantAccountEnums {

    @Getter
    @AllArgsConstructor
    enum Type {
        /**
         * 店长
         */
        MANAGER(0, "店长"),

        /**
         * 店员
         */
        CLERK(1, "店员");


        private Integer code;
        private String desc;
    }


    @Getter
    @AllArgsConstructor
    enum Status {
        /**
         * 审核中
         */
        AUDITING(0, "审核中"),

        /**
         * 审核通过
         */
        AUDIT_SUCCESS(1, "审核通过"),

        /**
         * 审核拒绝
         */
        AUDIT_REFUSE(2, "审核拒绝"),

        /**
         * 关店
         */
        CLOSE(3, "已关店"),

        /**
         * 注销
         */
        CANCEL(4, "注销");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum DeleteFlag {
        /**
         * 已删除
         */
        DELETED(0, "已删除"),

        /**
         * 正常
         */
        NORMAL(1, "正常使用");

        private Integer code;


        private String desc;
    }
}
