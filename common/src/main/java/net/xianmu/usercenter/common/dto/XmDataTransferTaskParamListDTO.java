package net.xianmu.usercenter.common.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 15:22
 */
@Data
public class XmDataTransferTaskParamListDTO {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 起始id
     */
    private Long startId;

    /**
     * 结束id
     */
    private Long endId;

    /**
     * 单个批次的处理量
     */
    private Integer offset;

    /**
     * 并发数
     */
    private Integer threadNum;

    /**
     * 每个批次的睡眠时间
     */
    private Integer sleep;
}
