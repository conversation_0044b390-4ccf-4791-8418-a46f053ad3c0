package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/13 14:41
 */
public interface InvoiceConfigEnums {

    @Getter
    @AllArgsConstructor
    enum TypeEnum {

        MERCHANT_STORE(0, "门店自有抬头"),
        REGIONAL_ORGANIZATION(1, "区域组织抬头")
        ;

        private Integer code;
        private String desc;

    }
}
