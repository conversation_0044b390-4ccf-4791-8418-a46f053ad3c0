package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 16:20
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreAccountQueryInput {

    /**
     * id
     */
    private Long id;


    /**
     * id
     */
    private List<Long> idList;


    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店id列表
     */
    private List<Long> storeIdList;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号前缀，用于右模糊
     */
    private String phonePrefix;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 0、已刪除 1、正常
     */
    private Integer deleteFlag;

    private String storeName;

    /**
     * 鲜沐merchant_sub_account表id
     */
    private Long xmAccountId;

    /**
     * 鲜沐merchant表id(冗余字段，用于业务兼容)
     */
    private Long mId;


    /**
     * mid列表
     */
    private List<Long> mIdList;

    /**
     * xm账户id列表
     */
    private List<Long> xmAccountIdList;

}
