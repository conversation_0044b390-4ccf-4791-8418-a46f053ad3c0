package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/8/30 14:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantStoreExtQueryInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 弹框
     */
    private Integer popView;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    private Integer changePop;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    private Integer firstLoginPop;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 预注册标记.1-代表为预注册
     */
    private Integer preRegisterFlag;

    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人名称
     */
    private String updater;


    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    private Integer operateStatus;

}
