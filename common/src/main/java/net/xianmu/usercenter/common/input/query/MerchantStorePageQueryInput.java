package net.xianmu.usercenter.common.input.query;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantStorePageQueryInput extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户列表
     */
    private List<Long> tenantIds;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     *门店类型：0、直营店 1、加盟店 2、托管店
     */
    private List<Integer> typeList;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店 4、拉黑 5、注销
     */
    private Integer status;


    /**
     * 要排除的状态列表
     */

    private List<Integer> excludeStatusList;


    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 门店Id
     */
    private List<Long> storeIds;

    /**
     * 是否供应
     */
    private Integer supplyStatus;

    /**
     * 是否供应门店Id
     */
    private List<Long> supplyStoreIds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 不需要匹配门店Id
     */
    private List<Long> noMatchingStoreIds;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 分组id
     */
    private Long groupId;


    /**
     *
     */
    private Long mId;

    /**
     *
     */
    private Integer areaNo;

    /**
     * 1大客户 2-单店
     */
    private Integer size;


    /**
     1 账期 2  现结
     */
    private Integer direct;

    /**
     * 门店ids
     */
    private List<Long> mIds;

    /**
     * areaNos 数据权限
     */
    private List<Integer> areaNos;

    /**
     * 大客户id
     */
    private Long adminId;

    private Integer xmPageDefaultQuerySize;

    /**
     * 门店下单是否可下单
     * true = 可下单
     * false = 不可下单
     */
    private Boolean placeOrderEnableFlag;
    /**
     * 门店下单失效日期倒计时限制
     */
    private Integer placeOrderPermissionExpiryTime;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;
    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;
    public void retainStoreIdList(List<Long> list) {
        if(CollUtil.isEmpty(list)) {
            return;
        }

        if (this.storeIds == null) {
            storeIds = list;
        } else {
            storeIds.retainAll(list);
        }
    }
}
