package net.xianmu.usercenter.common.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreCommandInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;


    /**
     * 账期开关 0开启 1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付0开启1关闭
     */
    private Integer onlinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;


    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 门店下单有效期
     * 1=短期
     * 0=长期
     */
    private Integer placeOrderPermissionTimeLimited;
    /**
     * 门店下单失效日期
     */
    private LocalDateTime placeOrderPermissionExpiryTime;
    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;









    // --------------------START 鲜沐数据迁移 START------------------

    /**
     * 鲜沐merchant表id
     */
    private Long mId;

    /**
     * 门店经营类型
     */
    private String businessType;

    /**
     * 区域组织id
     */
    private Long regionalId;

    /**
     * 门店自己的邀请码，可用于邀请门店、子账号
     */
    private String channelCode;


    /**
     * 运营服务区域
     */
    private Integer areaNo;


    // ----------------拓展表信息------------------
    /**
     * 弹框
     */
    private Integer popView;

    /**
     * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
     */
    private Integer changePop;

    /**
     * 首次登录弹窗：0、未弹 1、已弹
     */
    private Integer firstLoginPop;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 预注册标记.1-代表为预注册
     */
    private Integer preRegisterFlag;

    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * poi
     */
    private String poiNote;

    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    private Integer operateStatus;

    /**
     * 业务线:0=鲜沐;1=pop
     */
    private Integer businessLine;


    // ---------------- 鲜沐业务适配----------

    /**
     * 1-账期，2-现结
     */
    private Integer direct;


    // ----------------变更记录表信息------------------

    /**
     * 记录是哪个销售邀请的
     */
    private String inviterChannelCode;

    /**
     * 记录是哪个门店邀请的
     */
    private String merchantChannelCode;

    /**
     * 操作人名称
     */
    private String opName;

    /**
     * 操作类型，0-创建，1-审核，2-关店，3-拉黑
     */
    private String opType;

    /**
     * 操作备注
     */
    private String opRemark;
    // --------------------END 鲜沐数据迁移 END------------------

    /**
     * 非现金账户权限 0、关闭 1、开启
     */
    private Integer nonCashAuthority;
}
