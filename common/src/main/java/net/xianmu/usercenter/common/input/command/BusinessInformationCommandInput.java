package net.xianmu.usercenter.common.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/8 17:21
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessInformationCommandInput {

    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id：tenantId或者storeId
     */
    private Long bizId;

    /**
     * 类型：0-品牌用户，1-单店用户
     */
    private Integer type;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String companyPhone;
    /**
     * 联系电话-区号
     */
    private String companyAreaPhone;

    /**
     * 营业执照
     */
    private String businessLicense;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
