package net.xianmu.usercenter.common.input.query;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class TenantQueryInput {

    /**
     * 租户id
     */
    private Long id;

    /**
     * 租户id列表
     */
    private List<Long> tenantIdList;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户名称
     */
    private List<String> exactTenantNameList;

    /**
     * 租户类型：0-供应商,1-品牌方,2-帆台
     */
    private Integer type;

    /**
     * 大客户Id
     */
    private Long adminId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;

    /**
     * 微信分账开关
     */
    private Integer profitSharingSwitch;

    /**
     * 分账渠道 0 微信 1 汇付
     */
    private Integer onlinePayChannel;

    /**
     * 公司名称,对应工商信息表
     */
    private String companyName;

    /**
     * 租户联系人名称,对应工商信息表
     */
    private String contactName;



    /**
     * 排序ascending=升序，descending=降序
     */
    private String sortWord;

    /**
     * 排序字段 create_time,update_time
     */
    private String sortType;


    public static void paramValidate(TenantQueryInput input, int defaultMaxSize, boolean tenantQueryValidateSwitch){
        if(input == null) {
            log.error("请求核心参数缺失!");
            throw new BizException("请求核心参数缺失!");
        }
        if(!tenantQueryValidateSwitch){
            log.info("校验开关关闭，暂时不做校验");
            return;
        }

        if(( input.getId() == null && CollUtil.isEmpty(input.getTenantIdList())
                && input.getTenantName() == null && CollUtil.isEmpty(input.getExactTenantNameList())
                && input.getType() == null && input.getAdminId() == null
                && input.getPhone() == null)) {
            log.error("请求核心参数缺失!");
            throw new BizException("请求核心参数缺失!");
        }


        List<Long> tenantIdList = input.getTenantIdList();
        List<String> exactTenantNameList = input.getExactTenantNameList();
        if((CollUtil.isNotEmpty(tenantIdList) && tenantIdList.size() > defaultMaxSize)
                || (CollUtil.isNotEmpty(exactTenantNameList) && exactTenantNameList.size() > defaultMaxSize)) {
            log.error("单次请求数据量过大，目前单次最大支持：{}条", defaultMaxSize);
            throw new BizException("单次请求数据量过大!");
        }
    }
}
