package net.xianmu.usercenter.common.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 15:22
 */
@Data
public class XmDataFixTaskParamDTO {

    public static final String pull_black = "pull_black";
    public static final String refresh_merchant_data = "refresh_merchant_data";
    public static final String refresh_account_data = "refresh_account_data";
    public static final String refresh_address_data = "refresh_address_data";
    public static final String delete_merchant_data = "delete_merchant_data";
    public static final String delete_account_data = "delete_account_data";


    /**
     * 操作类型
     */
    String opType;

    Map<String, Object> paramMap;
}