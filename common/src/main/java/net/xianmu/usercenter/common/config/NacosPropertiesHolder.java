package net.xianmu.usercenter.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Nacos动态配置；
 */
@Data
@Slf4j
@Component
@NacosConfigurationProperties(dataId = "${spring.application.name}",
        type = ConfigType.PROPERTIES,
        prefix = "dynamic",
        autoRefreshed = true,
        ignoreInvalidFields = true)
public class NacosPropertiesHolder implements InitializingBean {

    /**
     * 账户脏手机号列表
     */
    private List<String> dirtyPhoneList;

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    @Value("${default-query-size:200}")
    private Integer defaultQuerySize;

    @Value("${max-query-size:1000}")
    private Integer maxQuerySize;

    @Value("${xm-page-default-query-size:100}")
    private Integer xmPageDefaultQuerySize;

    @Value("${merchantBatchCreateSize:3}")
    private Integer merchantBatchCreateSize;

    @Value("${tenantQueryValidateSwitch:true}")
    private boolean tenantQueryValidateSwitch;


    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
