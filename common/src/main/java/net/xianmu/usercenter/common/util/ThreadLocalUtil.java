package net.xianmu.usercenter.common.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/11 16:22
 */
public class ThreadLocalUtil {

    private static ThreadLocal<Map<String, Object>> map = new ThreadLocal();

    public static Object get(String key) {
        if (null == map.get()) {
            map.set(new HashMap<>());
        }
        return map.get().get(key);
    }

    public static void put(String key, Object value) {
        if (null == map.get()) {
            map.set(new HashMap<>());
        }
        map.get().put(key, value);
    }

    public static void remove() {
        map.remove();
    }

    public static Integer getSystemOrigin() {
        return (Integer) get("systemOrigin");
    }

}
