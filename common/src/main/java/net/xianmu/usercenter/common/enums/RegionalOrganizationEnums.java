package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/7 16:55
 */
public interface RegionalOrganizationEnums {

    @AllArgsConstructor
    @Getter
    public enum Source {
        SAAS(0, "saas"),
        XIANMU(1, "鲜沐");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }


    @AllArgsConstructor
    @Getter
    public enum Status {
        NORMAL(0, "正常"),
        DISABLED(1, "禁用");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }

    @AllArgsConstructor
    @Getter
    public enum Size {
        ADMIN(1, "大客户"),
        MERCHANT(2, "单店"),
        SAAS(3, "saas品牌客户");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }
}
