package net.xianmu.usercenter.common.input.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/15 10:06
 */
@Data
public class MerchantStoreGroupCommandInput {


    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 0、非默认分组 1、默认分组
     */
    private Integer type;

    /**
     * 绑定门店Id
     */
    private List<Long> storeIdList;



}
