package net.xianmu.usercenter.common.constants;

import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/3/28 15:40
 * @Description:
 */
public interface TenantDefaultConstant {

    // *********** 新增品牌商城  start  *********** //
    /**
     * 默认租户类型：入驻商户
     */
    public static final Integer DEFAULT_TYPE = 0;
    /**
     * 默认租户状态：启用
     */
    public static final Integer DEFAULT_STATUS = 1;
    /**
     * 默认分账开关：关闭
     */
    public static final Integer DEFAULT_SHARING_SWITCH = 0;
    /**
     * 自营仓类型
     */
    public static final Integer PROPRIETARY_TYPE = 0;
    /**
     * 三方仓类型
     */
    public static final Integer THREE_PARTIES_TYPE = 1;
    /**
     * 运费规则时用到的默认金额/费用
     */
    public static final BigDecimal DELIVERY_DEFAULT_FEE = BigDecimal.ZERO;
    /**
     * 运费规则类型：每单
     */
    public static final Integer ORDER_RULE_TYPE = 0;
    /**
     * 运费规则类型：每天
     */
    public static final Integer DAY_RULE_TYPE = 1;
    /**
     * 运费规则的默认价格类型:固定
     */
    public static final Integer DEFAULT_PRICE_TYPE = 0;
    /**
     * 品牌方运费规则默认类型:采用鲜沐运费
     */
    public static final Integer TENANT_DELIVERY_TYPE = 0;
    /**
     * 默认分组
     */
    public static final String DEFAULT_GROUP = "默认分组";
    /**
     * 分组类型 1-默认分组
     */
    public static final Integer GROUP_TYPE = 1;

    /**
     * 分组类型 0-非默认分组
     */
    public static final Integer GROUP_TYPE_NOT_DEFAULT = 0;
    /**
     * 代仓费用：规则类型。默认 1-按件数
     */
    public static final Integer RULE_TYPE = 1;
    /**
     * 代仓费用：规则
     */
    public static final String RULE = "[{\"amount\":0,\"count\":1}]";
    /**
     * 自动加价标识。默认 0-关闭
     */
    public static final Integer PRICE_FLAG = 0;
    /**
     * 订单售后规则  默认类型:配送仓库
     */
    public static final Integer AFTER_SALE_TYPE = 0;
    /**
     * 订单售后规则 默认类型 0-默认
     */
    public static final Integer AFTER_SALE_FLAG_DEFAULT = 0;
    /**
     * 订单售后规则 默认类型 1-非默认
     */
    public static final Integer AFTER_SALE_FLAG_DEFAULT_NON = 1;
    /**
     * 订单售后规则 默认规则的规则内容
     */
    public static final String AFTER_SALE_DEFAULT_RULE = "{\"applyEndTime\":48,\"autoFinishedTime\":7,\"orderStatusType\":5}";
    /**
     * 订单售后规则 非默认规则的规则内容
     */
    public static final String AFTER_SALE_NON_DEFAULT_RULE = "[{\"applyEndTime\":48,\"autoFinishedTime\":7,\"deliveryType\":0,\"orderStatusType\":5},{\"applyEndTime\":48,\"autoFinishedTime\":7,\"deliveryType\":1,\"orderStatusType\":5}]";
    /**
     * 管理员名称
     */
    public static final String ADMIN_NICK_SUFFIX = "管理员";
    /**
     * 超级管理员角色
     */
    public static final String ADMIN_NAME = "超级管理员";
    /**
     * super_admin 超级管理员
     */
    public static final Byte ADMIN_FLAG = 1;


    // *********** 新增品牌商城  end  *********** //

    /**
     * 鲜沐租户id
     */
    Long XIAN_MU_TENANT_ID = 1L;
}
