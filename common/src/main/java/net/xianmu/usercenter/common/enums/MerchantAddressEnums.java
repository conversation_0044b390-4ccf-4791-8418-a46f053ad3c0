package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/29 15:03
 */
public interface MerchantAddressEnums {

    @Getter
    @AllArgsConstructor
    enum status{

        NORMAL (1,"正常"),
        DELETED (2,"失效"),
        PENDING_APPROVAL (3,"待审核"),
        APPROVAL_REJECTED (4,"审核拒绝");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }

    /**
     * 是否默认地址
     */
    @Getter
    @AllArgsConstructor
    enum DefaultFlag{

        /**
         * 非默认
         */
        NOT_DEFAULT(0,"非默认"),
        /**
         * 失效
         */
        DEFAULT(1,"默认");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }

    /**
     * 是否需要审核
     */
    @Getter
    @AllArgsConstructor
    enum AuditFlag{

        /**
         * 不需要审核
         */
        NOT_NEED(0,"不需要审核"),
        /**
         * 需要审核
         */
        NEED(1,"需要审核");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }
}
