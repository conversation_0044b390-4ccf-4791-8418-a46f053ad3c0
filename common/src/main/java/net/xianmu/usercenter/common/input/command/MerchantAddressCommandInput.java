package net.xianmu.usercenter.common.input.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/29 14:01
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantAddressCommandInput {

    /**
     * 主键、自增
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 们拍好
     */
    private String houseNumber;
    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否是默认地址：0、否 1、是
     */
    private Integer defaultFlag;
    /**
     * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
     */
    private Integer status;

    /**
     * 地址备注
     */
    private String addressRemark;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 联系人列表
     */
    List<MerchantContactCommandInput> merchantContactList;
}
