package net.xianmu.usercenter.common.util;


import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * RedisLockUtil
 */

@Slf4j
public class RedisLockUtil {

    private static RedissonClient redissonClient;

    static {
        redissonClient = SpringContextHolder.getBean(RedissonClient.class);
    }

    /**
     * 避免重载
     * @param client client
     */
    private void setRedissonClient(RedissonClient client) {
        redissonClient = client;
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @return
     */
    public static RLock lock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        return lock;
    }

    /**
     * 释放锁
     *
     * @param lockKey
     */
    public static void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.unlock();
    }


    /**
     * 如果被当前线程持有就释放锁
     *
     * @param lockKey
     */
    public static void unlockIfHeldByCurrentThread(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        if(lock.isHeldByCurrentThread()) {
            lock.unlock();
            log.info("锁释放成功, key: {}, time:{}", lockKey, System.currentTimeMillis());
        } else {
            log.warn("释放锁失败,当前key:{}所对应的锁正被其他线程持有!", lockKey);
        }

    }

    /**
     * 释放锁
     *
     * @param lock
     */
    public static void unlock(RLock lock) {
        lock.unlock();
    }

    /**
     * 带超时的锁
     *
     * @param lockKey
     * @param timeout 超时时间   单位：秒
     */
    public static RLock lock(String lockKey, int timeout) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(timeout, TimeUnit.SECONDS);
        return lock;
    }

    /**
     * 获取锁状态
     *
     * @param lockKey
     * @return true 锁住 false-没锁
     */
    public static boolean isLocked(String lockKey) {
        return redissonClient.getLock(lockKey).isLocked();
    }



    /**
     * 是否被当前线程持有
     *
     * @param lockKey
     * @return true 锁住 false-没锁
     */
    public static boolean isHeldByCurrentThread(String lockKey) {
        return redissonClient.getLock(lockKey).isHeldByCurrentThread();
    }


    /**
     * 获取RLock对象
     *
     * @param lockKey
     * @return
     */
    public static RLock getLock(String lockKey) {
        return redissonClient.getLock(lockKey);
    }

    /**
     * 带超时的锁
     *
     * @param lockKey
     * @param unit    时间单位
     * @param timeout 超时时间
     */
    public static RLock lock(String lockKey, TimeUnit unit, int timeout) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(timeout, unit);
        return lock;
    }


    /**
     * 尝试获取锁
     *
     * @param lockKey
     * @param waitTime  最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public static boolean tryLock(String lockKey, int waitTime, int leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            return false;
        }
    }

    /**
     * 尝试获取锁
     *
     * @param lockKey
     * @param waitTime  最多等待时间，秒
     * @return
     */
    public static boolean tryLock(String lockKey, int waitTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            return false;
        }
    }

    /**
     * 尝试获取锁
     *
     * @param lockKey
     * @param unit      时间单位
     * @param waitTime  最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @return
     */
    public static boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            return false;
        }
    }


    /**
     * 获取对象桶
     *
     * @param objectName
     * @param <T>
     * @return
     */
    public static <T> RBucket<T> getRBucket(String objectName) {
        return redissonClient.getBucket(objectName);
    }


}
