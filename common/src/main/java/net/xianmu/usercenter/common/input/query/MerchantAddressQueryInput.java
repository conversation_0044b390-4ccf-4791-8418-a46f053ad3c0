package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/29 14:01
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantAddressQueryInput extends BasePageInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店id列表
     */
    List<Long> storeIdList;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;
    /**
     * 城市列表
     */
    private List<String> cityList;

    /**
     * 区
     */
    private String area;

    /**
     * 区列表
     */
    private List<String> areaList;


    /**
     * 是否是默认地址：0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 状态(1-正常或审核通过、2-删除、3-待审核、4-审核不通过)
     */
    private Integer status;

    /**
     * 鲜沐联系人id
     */
    private Long xmContactId;

    /**
     * 鲜沐merchant表id,冗余字段便于鲜沐接入
     */
    private Long mId;

    /**
     * 鲜沐contact联系人表id列表
     */
    private List<Long> xmContactIdList;
}
