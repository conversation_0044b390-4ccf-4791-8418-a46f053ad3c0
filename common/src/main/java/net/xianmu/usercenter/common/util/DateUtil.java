package net.xianmu.usercenter.common.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/6/27 18:07
 */
public class DateUtil {

    public static LocalDateTime getTimeWithBinLogPatternDefault(String date, LocalDateTime defaultTime){
        if(StrUtil.isBlank(date)){
            return defaultTime;
        }
        LocalDateTime time;
        try {
            time = LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.0"));
        } catch (DateTimeParseException e) {
            time = LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return time;
    }


    public static Date toDate(LocalDateTime defaultTime) {
        if (defaultTime == null) {
            return null;
        }
        return Date.from(defaultTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    public static LocalDateTime toLocalDateTime(Date data) {
        if (data == null) {
            return null;
        }
        return LocalDateTime.ofInstant(data.toInstant(), ZoneId.systemDefault());
    }


    public static LocalDateTime toLocalDateTime(DateTime data) {
        if (data == null) {
            return null;
        }
        return LocalDateTime.ofInstant(data.toInstant(), ZoneId.systemDefault());
    }
}
