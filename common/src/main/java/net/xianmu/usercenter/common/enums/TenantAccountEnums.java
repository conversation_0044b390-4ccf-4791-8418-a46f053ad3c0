package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
public interface TenantAccountEnums {

    @Getter
    @AllArgsConstructor
    enum status{
        /**
         * 有效
         */
        EFFECTIVE(0,"有效"),
        /**
         * 失效
         */
        FAILURE(1,"失效");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum Role{
        /**
         * 普通用户
         */
        ORDINARY(0,"普通用户"),
        /**
         * 超级管理员
         */
        SUPER_ADMIN(1,"超级管理员");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum DeletedFlag{
        /**
         * 有效
         */
        EFFECTIVE(0,"有效"),
        /**
         * 失效
         */
        FAILURE(1,"失效");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }
}
