package net.xianmu.usercenter.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.client.protocol.decoder.ObjectMapDecoder;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/17 13:58
 */
@Slf4j
public class MapUtil {

    /**
     * convertToStringMap
     *
     * @param map
     * @return
     */
    public static Map<String, String> convertToStringMap(Map<String, Object> map) {
        if (CollUtil.isEmpty(map)) {
            return new HashMap<>();
        }

        Map<String, String> stringMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Integer || value instanceof Long || value instanceof BigDecimal) {
                stringMap.put(key, String.valueOf(value));
            } else if (value instanceof String) {
                stringMap.put(key, (String) value);
            } else if (value instanceof Timestamp) {
                // Handle Timestamp (if needed).
                Timestamp timestamp = (Timestamp) value;
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                stringMap.put(key, df.format(timestamp));
            } else {
                log.warn("非法类型：{}", value.getClass());
                // Handle other data types as per your requirements (optional).
                // For unknown data types, you can ignore them or throw an exception, depending on the use case.
                // For this example, we ignore them.
            }
        }

        return stringMap;
    }

    /**
     *
     * @param map
     * @param key
     * @return
     */
    public static Integer getInteger(Map<String, String> map, String key) {
        if(CollUtil.isEmpty(map)) {
            return null;
        }
        String strValue = map.get(key);
        return StrUtil.isBlank(strValue) ? null : Integer.valueOf(strValue);
    }

}
