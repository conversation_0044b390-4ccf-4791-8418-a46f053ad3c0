package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 排序类型
 */
@Getter
@AllArgsConstructor
public enum SortTypeEnum {
    ERROR("error", "", "错误"),
    ASCENDING("ascending", "asc", "升序"),
    DESCENDING("descending", "desc", " 降序"),
    ;

    private String type;
    private String keyWord;
    private String desc;

    public static SortTypeEnum getByType(String type) {
        for (SortTypeEnum sortTypeEnum : SortTypeEnum.values()) {
            if (sortTypeEnum.type.equals(type)) {
                return sortTypeEnum;
            }
        }

        return ERROR;
    }
}
