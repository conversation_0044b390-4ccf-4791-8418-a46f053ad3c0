package net.xianmu.usercenter.common.config;


import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

/**
 * <AUTHOR>
 */
public final class MdcDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable task) {
        Map<String, String> threadContext = MDC.getCopyOfContextMap();
        return () -> {
            try {
                // 将父线程的context set到子线程
                MDC.setContextMap(threadContext);
                task.run();
            } finally {
                MDC.clear();
            }
        };
    }

}
