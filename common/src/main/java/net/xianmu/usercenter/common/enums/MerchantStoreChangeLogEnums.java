package net.xianmu.usercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/7/11 13:44
 */
public interface MerchantStoreChangeLogEnums {

    @Getter
    @AllArgsConstructor
    public enum OpType {
        /**
         * 0-直营店
         */
        CREATE(0, "创建"),
        /**
         * 1-审核
         */
        AUDIT(1, "审核"),
        /**
         * 2-关店
         */
        CLOSE(2, "关店"),
        /**
         * 3-拉黑
         */
        PULL_BLACK(3, "拉黑");

        /**
         * 店铺类型编码
         */
        private Integer code;
        /**
         * 店铺类型描述
         */
        private String desc;

    }
}
