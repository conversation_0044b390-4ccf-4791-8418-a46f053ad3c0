package net.xianmu.usercenter.common.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.usercenter.common.constants.MerchantConstant;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @description 校验参数合法
 */
@Slf4j
public class ValidateUtil {

    /**
     * 校验参数是否在数组中
     *
     * @param parameter
     * @param objectArr
     * @return
     */
    public static boolean isContains(Object parameter, Object[] objectArr) {
        if (null == objectArr || null == parameter) {
            return false;
        }
        for (Object obj : objectArr) {
            if (parameter.equals(obj)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验参数是否在map中
     *
     * @param reqDto map
     * @param keys   key
     * @return
     */
    public static boolean isContains(Map<String, String> reqDto, String... keys) {
        if (reqDto == null || reqDto.size() == 0) {
            return false;
        }
        if (keys.length == 0) {
            return false;
        }
       return Arrays.stream(keys).anyMatch(reqDto::containsKey);
    }


    /**
     * 参数校验
     *
     * @return
     */
    public static void paramValidate(Map<String, Object> reqDto, String... keys) {
        if (reqDto == null || reqDto.size() == 0) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return;
        }
        for (String key : keys) {
            if (reqDto.get(key) == null) {
                log.error("请求参数:{}为空!!", key);
                throw new ParamsException(String.format("请求参数:%s为空!!", key));
            }
        }
    }


    /**
     * 参数校验
     *
     * @return
     */
    public static void paramValidate(Object object, String... keys) {
        if (object == null) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return;
        }
        Class<?> clazz = object.getClass();
        for (String key : keys) {
            try {
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                Object value = field.get(object);
                if (value == null) {
                    log.error("请求参数:{}为空!!", key);
                    throw new ParamsException(String.format("请求参数:%s为空!!", key));
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.warn("参数检验异常", e);
            }
        }
    }



    /**
     * 参数校验,至少存在列表中的一个
     *
     * @return
     */
    public static boolean keyParamExist(Object object, String... keys) {
        if (object == null) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return true;
        }
        Class<?> clazz = object.getClass();
        for (String key : keys) {
            try {
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                Class<?> type = field.getType();
                if(List.class.equals(type)) {
                    List value = (List) field.get(object);
                    if(CollUtil.isNotEmpty(value)) {
                        return true;
                    }
                } else {
                    Object value = field.get(object);
                    if (value != null) {
                        return true;
                    }
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.warn("参数检验异常", e);
            }
        }
        return false;
    }



    /**
     * 参数校验,至少存在列表中的一个
     *
     * @return
     */
    public static void keyParamValidateExist(Object object, String... keys) {
        if (object == null) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return;
        }
        Class<?> clazz = object.getClass();
        for (String key : keys) {
            try {
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                Object value = field.get(object);
                if (value != null) {
                    return;
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.warn("参数检验异常", e);
            }
        }
        log.error("缺失必要参数：{}", (Object) keys);
        throw new ParamsException("缺失必要参数!!");
    }


    public static String transNull(String string) {
        return string == null ? "" : string;
    }


    /**
     * 校验地址是否规范
     *
     * @param storeAddress
     * @return
     */
    public static boolean isAddress(String storeAddress) {
        return StrUtil.isBlank(storeAddress) ? false : storeAddress.length() > 100 ? false : true;
    }

    /**
     * 校验门牌号是否规范
     *
     * @return
     */
    public static boolean isHouseNumber(String houseNumber) {
        return houseNumber.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-]{1,50}");
    }

    /**
     * 校验门店名称是否规范
     *
     * @param storeName
     * @return
     */
    public static boolean isStoreName(String storeName) {
        if (StrUtil.isBlank(storeName)){
            return false;
        }
        if (storeName.length() > MerchantConstant.STORE_NAME_LENGTH){
            return false;
        }
        String emojiStrippedInput = EmojiParser.removeAllEmojis(storeName);
        return emojiStrippedInput.equals(storeName);
        /// return StrUtil.isBlank(storeName) ? false : storeName.matches("[\\u4e00-\\u9fa5a-zA-Z0-9()~@.·\\s（）～。-]{1,20}");
    }


    /**
     * 获取source中存在，target中不存在的数据
     *
     * @param source
     * @param target
     * @return
     */
    public static List<Long> getDifference(List<Long> source, List<Long> target) {
        if (CollUtil.isEmpty(source)) {
            return new ArrayList<>();
        }

        if (CollUtil.isEmpty(target)) {
            return source;
        }
        return source.stream()
                .filter(element -> !target.contains(element))
                .collect(Collectors.toList());
    }

}
