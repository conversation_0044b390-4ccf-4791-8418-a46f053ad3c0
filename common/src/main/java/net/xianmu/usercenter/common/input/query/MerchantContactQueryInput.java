package net.xianmu.usercenter.common.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/5/29 18:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantContactQueryInput {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 地址id列表
     */
    private List<Long> addressIdList;

    /**
     * 联系人名称
     */
    private String name;

    /**
     * 联系人手机号
     */
    private String phone;

    /**
     * 是否是默认联系人：0、否 1、是
     */
    private Integer defaultFlag;
}
